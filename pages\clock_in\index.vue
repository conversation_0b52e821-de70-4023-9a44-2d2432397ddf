<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'上户打卡'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="process_area">
				<view class="process">
					<view class="process-item" :class="{'active': process >= 1}">
						<div class="top">1</div>
						<div class="bottom">绑定微信</div>
					</view>
					<view class="process-item" :class="{'active': process >= 2}">
						<div class="top">2</div>
						<div class="bottom">上户确认</div>
					</view>
					<view class="process-item" :class="{'active': process >= 3}">
						<div class="top">3</div>
						<div class="bottom">开箱检查</div>
					</view>
				</view>
				<view class="content">
					<text class="text1">{{ store.userInfo.name }}您好</text>
					<text class="text1">请将二维码展示给客户扫码</text>
					<text class="text1">绑定本次服务关系</text>
					<text class="text1">并确认上户及开箱检查</text>
				</view>
				<image :src="contractDetail?.qrcode_clock_in" mode="aspectFill" class="qrcode"></image>
				<view class="btn">
					<text class="btn-text" v-if="clockFlowData?.clockInFlow?.flow == 0">微信未绑定</text>
					<text class="btn-text" v-if="clockFlowData?.clockInFlow?.flow == 1">微信已确认</text>
					<text class="btn-text" v-if="clockFlowData?.clockInFlow?.flow == 2">上户已确认</text>
					<text class="btn-text" v-if="clockFlowData?.clockInFlow?.flow == 3">开箱检查已确认</text>
				</view>
			</view>

		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance, onMounted } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { snb } from '@/components/customNavigationBar/snb'
// 分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()


// 页面数据
const { NavigationBarTitle } = snb()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})
const store = useStore()
const process = ref(1)
/**************************** 接口函数 ****************************/
const contractDetail = ref({})
const clockFlowData = ref({})
const getContractDetail = async () => {
	try {
		const res = await api.request.ajax({
			url: '/work/ctDetail',
			type: 'POST',
		})
		
		if (res.code === 1) {
			contractDetail.value = res.data
			await getClockFlow()
		}
	} catch (error) {
		console.error('获取合同详情失败:', error)
		uni.showToast({
			title: '获取合同信息失败',
			icon: 'none'
		})
	}
}
// 添加获取打卡流程函数
const getClockFlow = async () => {
	try {
		if (!contractDetail.value.cid || !contractDetail.value.hid) return
		
		const res = await api.request.ajax({
			url: '/work/clockFlow',
			type: 'POST',
			data:{
				cid: contractDetail.value.cid,
				hid: contractDetail.value.hid
			}
		})
		
		if (res.code === 1) {
			clockFlowData.value = res.data
			process.value = clockFlowData.value.clockInFlow.flow + 1
		}
	} catch (error) {
		console.error('获取打卡流程失败:', error)
		uni.showToast({
			title: '获取打卡进度失败',
			icon: 'none'
		})
	}
}
onLoad(() => {
	getContractDetail()
})


</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>