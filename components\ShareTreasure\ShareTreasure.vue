<template>
	<view>
		<view class="fxb_header">
			<view class="stit">分享宝</view>
			<view class="tabs">
				<view 
					class="tabs_item" 
					:class="{'pick': props.currentTab === index}" 
					v-for="(item,index) in props.subtitle"
					:key="item.id" 
					@click="handleTabChange(index)"
				>{{ item.name }}</view>
			</view>
		</view>
		<view class="fxb">
			<view :class="[props.currentTab === 1 ? 'content' : 'content2']">
				<view class="container" v-for="item in props.list" :key="item.id" @click="goDetail(item)">
					<view class="card">
						<image class="main-image" :src="item.img" mode="aspectFill" v-if="item.img" />
						<view class="left">
							<view class="title">{{item.title}}</view>
							<view class="date" v-if="props.currentTab === 0">{{item.date ||item.time|| '暂无日期'}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
        <view class="getmore" v-if="props.hasMore" @click="handleLoadMore">
            <view class="getmore_text" :class="{ 'loading': props.loading }">
                <text>{{ props.loading ? '加载中...' : '点击查看更多' }}</text>
            </view>
        </view>
        <view class="no-more" v-else>
            <text>没有更多数据了~</text>
        </view>
	</view>
</template>

<script setup lang="ts">
interface ListItem {
    id: string | number;
    img?: string;
    title: string;
    date?: string;
    time?: string;
}

interface SubtitleItem {
    id: string | number;
    name: string;
}

interface Props {
    list: ListItem[];
    subtitle: SubtitleItem[];
    currentTab: number;
    loading: boolean;
    hasMore: boolean;
}

// Props定义
const props = withDefaults(defineProps<Props>(), {
    list: () => [],
    subtitle: () => [],
    currentTab: 0,
    loading: false,
    hasMore: true
})

// Emits定义
const emit = defineEmits<{
    (e: 'tabChange', index: number): void;
    (e: 'loadMore'): void;
    (e: 'detail', item: ListItem): void;
}>()

// 处理标签切换
const handleTabChange = (index: number) => {
    emit('tabChange', index)
}

// 处理加载更多
const handleLoadMore = () => {
    emit('loadMore')
}

// 处理详情跳转
const goDetail = (item: ListItem) => {
    emit('detail', item)
}
</script>

<style lang="less" scoped>
.fxb_header {
	padding: 30rpx 60rpx;
	width: 100%;
	display: flex;
	align-items: center;
	position: sticky;
	top: calc(100rpx + var(--status-bar-height));
	z-index: 2;

	.stit {
		position: relative;
		display: flex;
		align-items: center;
		font-weight: bold;
		font-size: 36rpx;
		color: #333333;
		flex-shrink: 0;
        margin-right: 40rpx;
		&::before {
			content: '';
			width: 8rpx;
			height: 30rpx;
			background: linear-gradient(-31deg, #FF6136, #F31630);
			border-radius: 4rpx;
			margin-right: 18rpx;
		}
	}

	.tabs {
		flex: 1;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;

		.tabs_item {
			font-weight: bold;
			font-size: 30rpx;
			color: #333;
			flex: 1;
			text-align: center;
			position: relative;
			transition: all 0.3s ease;
			padding: 10rpx 0;
		}

		.pick {
			color: #D71E00;
			position: relative;
			
			&::after {
				content: '';
				position: absolute;
				bottom: -6rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background: linear-gradient(-31deg, #FF6136, #F31630);
				border-radius: 2rpx;
				transition: all 0.3s ease;
			}
			
			font-weight: 800;
		}
	}
}

.fxb {
	position: relative;
	margin: 0 auto;
	width: 720rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	overflow: hidden;
	
	.card-base() {
		display: flex;
		position: relative;
		
		.left {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			
			.title {
				color: #333333;
				line-height: 1.5;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
			}
			
			.date {
				font-size: 24rpx;
			}
		}
		
		.main-image {
			flex-shrink: 0;
			border-radius: 16rpx;
		}
	}
	
	.content {
		width: 690rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		
		.card {
			.card-base();
			width: 690rpx;
			margin: 0 0 30rpx;
			background-color: #ffffff;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;
			flex-direction: column;
			
			&::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 6rpx;
				height: 40rpx;
				background: linear-gradient(-31deg, #FF6136, #F31630);
				border-radius: 0 3rpx 3rpx 0;
				opacity: 0;
				transition: all 0.3s;
			}
			
			.main-image {
				width: 100%;
				height: 400rpx;
			}
			
            .left{
                .title {
                    font-size: 32rpx;
                    font-weight: bold;
                    margin: 20rpx 30rpx 12rpx;
                    min-height: 48rpx;
                }

                .date {
                    display: flex;
                    align-items: center;
                    color: #999999;
                    margin: 0 30rpx 20rpx;
                    
                    &::before {
                        content: "";
                        display: inline-block;
                        width: 28rpx;
                        height: 28rpx;
                        margin-right: 10rpx;
                        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' fill='%23999999'%3E%3Cpath d='M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z'%3E%3C/path%3E%3C/svg%3E") no-repeat;
                        background-size: contain;
                    }
                }
            }
		}
	}

	.content2 {
		width: 690rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #FFFFFF;
		box-shadow: 1rpx 1rpx 0px 0px #FFFFFF;
		border-radius: 20rpx 26rpx 26rpx 20rpx;
		padding: 20rpx 40rpx;
		
		.container {
			width: 100%;
			&:last-child {
				.card {
					border-bottom: none;
				}
			}
		}
		
		.card {
			.card-base();
			height: 170rpx;
			border-bottom: 1rpx solid #F3F3F3;
			justify-content: space-between;
			flex-direction: row;
			align-items: center;
			padding: 20rpx 0;
			margin: 0;
			.left{
				margin-left: 20rpx;
                .title {
                    font-size: 30rpx;
                    font-weight: 400;
                    margin: 0;
                }
                
                .date {
                    color: #D3D2D2;
                    
                    &::before {
                        display: none;
                    }
                }
            }

			
			.main-image {
				width: 200rpx;
				height: 120rpx;
				
			}
		}
	}
}

.getmore {
    width: 100%;
    padding: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    
    .getmore_text {
        min-width: 240rpx;
        height: 80rpx;
        background: linear-gradient(to right, #FFFFFF, #F8F8F8);
        border: 2rpx solid #EFEFEF;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10rpx;
        padding: 0 40rpx;
        transition: all 0.3s ease;
        
        text {
            font-size: 28rpx;
            color: #666666;
            font-weight: 500;
        }
    }
}

.no-more {
    width: 100%;
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    
    text {
        font-size: 26rpx;
        color: #999999;
        &::before,
        &::after {
            content: '';
            display: inline-block;
            width: 60rpx;
            height: 2rpx;
            background: #EEEEEE;
            margin: 0 20rpx;
            vertical-align: middle;
        }
    }
}
</style> 