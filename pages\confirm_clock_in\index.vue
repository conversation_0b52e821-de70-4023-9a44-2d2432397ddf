<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'上户确认'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="process_area">
				<view class="process">
					<view class="process-item" :class="{'active': process >= 1}">
						<div class="top">1</div>
						<div class="bottom">绑定微信</div>
					</view>
					<view class="process-item" :class="{'active': process >= 2}">
						<div class="top">2</div>
						<div class="bottom">上户确认</div>
					</view>
					<view class="process-item" :class="{'active': process >= 3}">
						<div class="top">3</div>
						<div class="bottom">开箱检查</div>
					</view>
				</view>
				<template v-if="process <= 1">
					<image src="/static/img/clockicon1.png" mode="aspectFill" class="qrcode qrcode2"></image>
					<view class="content content2">
						<text class="text1">顾客您好</text>
						<text class="text1" >感谢您选择皖嫂家政</text>
						<text class="text1" >后续阿姨上下户确认、及服务评价等操作</text>
						<text class="text1" >均由本次绑定的微信操作哦！</text>
					</view>
					<view class="btn btn2" @click="clockInVerify(1)">
						<text class="btn-text">绑定此微信</text>
					</view>
					<view class="rebind" @click="exit">不绑定，重新扫码</view>
				</template>
				<template v-else-if="process == 2">
					<image src="/static/img/clockicon2.png" mode="aspectFill" class="qrcode qrcode2"></image>
					<view class="content content2">
						<text class="text1">恭喜您已绑定成功！</text>
						<text class="text1" >请确认阿姨已到岗开始上户！</text>
					</view>
					<view class="btn btn2" @click="clockInVerify(2)">
						<text class="btn-text">确认阿姨已到岗上户</text>
					</view>
				</template>
				<template v-else-if="process >= 3">
					<image src="/static/img/clockicon3.png" mode="aspectFill" class="qrcode qrcode2"></image>
					<view class="content content2">
						<text class="text1">请您对阿姨行李开箱检查</text>
						<text class="text1" >确认无违禁物品</text>
					</view>
					<view class="btn btn2" @click="clockInVerify(3)">
						<text class="btn-text">确认开箱检查，无违禁物品</text>
					</view>
				</template>
			</view>

		</view>
		<tm-overlay v-model:show="showWin" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup">
				<image src="/static/img/dktc.png" mode="widthFix" class="dkcg"></image>
				<view class="title">确认上户</view>
				<view class="content content2">
					<text class="content-text">您已成功绑定此微信，并确认阿姨到岗上户，同时已对阿姨所带物品开箱检查，阿姨已开始上工服务咯！</text>
				</view>
				<view class="confirm" @click="exit">确认关闭</view>
			</view>
		</tm-overlay>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance, onMounted } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { snb } from '@/components/customNavigationBar/snb'
// 分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

// 页面数据
const { NavigationBarTitle } = snb()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})
const store = useStore()

const process = ref(1)
const showWin = ref(false)
const exit = () => {
	showWin.value = false
	uni.exitMiniProgram()
}
const id = ref('')

/**************************** 接口函数 ****************************/
const clockFlowData = ref({})
const clockInVerify = async (step: string|number) => {
	try {
		const res = await api.request.ajax({
			url: '/work/clockInVerify',
			type: 'POST',
			data: {
				id: id.value,
				step
			}
		})
		
		if (res.code == 1) {
			if (step == '3') {
				showWin.value = true
			}else{
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
			}
			getClockFlow()
		}else{
			uni.showToast({
				title: res.msg,
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('验证失败:', error)
		uni.showToast({
			title: '验证失败',
			icon: 'none'
		})
		return false
	}
}
// 添加获取打卡流程函数
const getClockFlow = async () => {
	try {
		const res = await api.request.ajax({
			url: '/work/clockFlow',
			type: 'POST',
			data:{
				id: id.value
			}
		})
		
		if (res.code === 1) {
			clockFlowData.value = res.data
			process.value = clockFlowData.value.clockInFlow.flow + 1
		}
	} catch (error) {
		console.error('获取打卡流程失败:', error)
		uni.showToast({
			title: '获取打卡进度失败',
			icon: 'none'
		})
	}
}
onLoad((e) => {
	if(e.scene){
		let scene = decodeURIComponent(e.scene)
		const params = {}
		scene.split(',').forEach(item => {
			const [key, value] = item.split(':')
			params[key] = value
		})
		id.value = params.id || ''
	}else if(e.id){
		id.value = e.id
	}
	getClockFlow()
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	align-items: center;
}
</style>