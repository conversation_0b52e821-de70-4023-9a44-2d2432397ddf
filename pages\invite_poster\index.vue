<template>
    <tm-app>
        <view class="main">
			<image :src="poster" mode="widthFix" class="invite-bg" />
            <view class="save-btn" @click="saveImage">保存海报</view>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import { share } from '@/tmui/tool/lib/share'
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline();

const localPosterPath = ref('')  // 存储本地图片路径

// 获取页面数据
const poster = ref('')
const getPoster = async () => {
    const res = await api.request.ajax({
        url: '/money/poster',
        type: 'POST',
    })

    if (res.code === 1) {
        poster.value = res.data.src
        // 设置分享内容
        setShareApp(res.data.shareData)
        setShareTime(res.data.shareTimeline)
        
        // 预先下载图片
        try {
            const downloadResult = await new Promise((resolve, reject) => {
                uni.downloadFile({
                    url: res.data.src,
                    success: res => {
                        if (res.statusCode === 200) {
                            resolve(res.tempFilePath)
                        } else {
                            reject(new Error('下载失败'))
                        }
                    },
                    fail: reject
                })
            })
            localPosterPath.value = downloadResult
        } catch (error) {
            console.log('图片预下载失败:', error)
        }
    } else {
        uni.showToast({
            title: res.msg,
            icon: 'none'
        })
    }
}

// 保存图片到相册
const saveImage = async () => {
    if (!poster.value) {
        uni.showToast({
            title: '图片加载失败',
            icon: 'none'
        })
        return
    }

    try {
        // 获取保存相册权限
        await new Promise((resolve, reject) => {
            uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: resolve,
                fail: reject
            })
        })

        // 如果没有本地路径，说明预下载失败了，需要重新下载
        if (!localPosterPath.value) {
            const downloadResult = await new Promise((resolve, reject) => {
                uni.downloadFile({
                    url: poster.value,
                    success: res => {
                        if (res.statusCode === 200) {
                            resolve(res.tempFilePath)
                        } else {
                            reject(new Error('下载失败'))
                        }
                    },
                    fail: reject
                })
            })
            localPosterPath.value = downloadResult
        }

        // 保存图片到相册
        await new Promise((resolve, reject) => {
            uni.saveImageToPhotosAlbum({
                filePath: localPosterPath.value,
                success: resolve,
                fail: reject
            })
        })

        uni.showToast({
            title: '保存成功',
            icon: 'success'
        })
    } catch (error) {
        console.log('保存失败:', error)
        // 判断是否是权限问题
        if (error.errMsg && error.errMsg.includes('authorize:fail')) {
            uni.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                success: (res) => {
                    if (res.confirm) {
                        uni.openSetting()
                    }
                }
            })
        } else {
            uni.showToast({
                title: '保存失败，请稍后重试',
                icon: 'none'
            })
        }
    }
}

// 页面加载
onLoad(async (e) => {
    getPoster()
})

</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
    min-height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background-color: #ee0b0b;
    .invite-bg{
        width: 750rpx;
        height: 100vh;
    }
}

.save-btn {
    position: fixed;
    bottom: 140rpx;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(90deg, #FF6B6B, #FF8E8E);
    color: #fff;
    padding: 20rpx 60rpx;
    border-radius: 40rpx;
    font-size: 32rpx;
    box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.3);
    z-index: 100;
}
</style>