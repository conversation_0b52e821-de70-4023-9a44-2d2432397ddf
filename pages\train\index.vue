<template>
	<tm-app>
		<view class="main">
            <view class="swipe">
                <tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
                    color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goLink(bannerList[i].link)"></tm-carousel>
            </view>
			<view class="content">
				<!-- <image src="/static/img/stit2.png" mode="widthFix" class="stit"></image> -->
				<view class="form" >
					<view class="form-item"  @click="openWorkTypePicker">
						<view class="label">
							<text class="ft">服务类型：</text>
						</view>
						<input type="text" v-model="service_type_str" readonly disabled />
					</view>
					<view class="form-item" @click="openAreaPicker">
						<view class="label">
							<text class="ft">所在区域：</text>
						</view>
						<input type="text" v-model="areaStr" readonly disabled />
					</view>
					<view class="form-item">
						<view class="label">
							<text class="ft">您的姓名：</text>
						</view>
						<input type="text" v-model="username" />
					</view>
					<view class="form-item">
						<view class="label">
							<text class="ft">您的电话：</text>
						</view>
						<input type="tel" v-model="phone" />
					</view>
					<view class="bottom">
						<view class="button" @click="submit">提交</view>
					</view>
				</view>
			</view>
            <!-- 服务类型选择器 -->
            <tm-picker
                :show="showPicker"
                :columns="serviceList"
                @confirm="onConfirmWorkType"
                @cancel="showPicker = false"
                v-model:model-str="service_type"
                :immediateChange="true"
				color="#F31630"
            />

            <!-- 地区选择器 -->
            <tm-picker
                :show="showAreaPicker"
                :columns="areaList"
                @confirm="onAreaConfirm"
                @cancel="showAreaPicker = false"
                v-model:model-str="area"
                :immediateChange="true"
				color="#F31630"
            />
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import tmApp from '@/tmui/components/tm-app/tm-app.vue'
import tmPicker from '@/tmui/components/tm-picker/tm-picker.vue' //tm-drawer第25行设置了100vh高度
import tmRadio from '@/tmui/components/tm-radio/tm-radio.vue'
import tmRadioGroup from '@/tmui/components/tm-radio-group/tm-radio-group.vue'
import { useStore } from '@/until/mainpinia';
import { goLink } from '@/until/index'
import { share } from "@/tmui/tool/lib/share";
import * as api from '@/api/index.js'
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share()
onShareAppMessage();
onShareTimeline()

const store = useStore()
const username = ref('')
const experience = ref('')
const phone = ref('')
const area = ref('')
const areaStr = ref('')
const service_type = ref('')
const service_type_str = ref('')
const userInfo = computed(() => store.userInfo)
watch(userInfo, (newVal) => {
	if(newVal){
		phone.value = newVal.phone
		username.value = newVal.name
	}
},{
	immediate: true
})
interface Banner {
	id: string;
	title: string;
	link: string;
	src: string;
	is_auth: string;
}

interface ServiceItem {
	text: string;
	value: number;
}

interface AreaItem {
	text: string;
	value: number;
}
const bannerList = ref<Banner[]>([])
const serviceList = ref<ServiceItem[]>([])
const areaList = ref<AreaItem[]>([])
// 选择器控制
const showPicker = ref(false)
const showAreaPicker = ref(false)

// 打开服务类型选择器
const openWorkTypePicker = () => {
    if (serviceList.value.length === 0) {
        uni.showToast({
            title: '暂无可选服务类型',
            icon: 'none'
        })
        return
    }
    showPicker.value = true
}

// 打开地区选择器
const openAreaPicker = () => {
    if (areaList.value.length === 0) {
        uni.showToast({
            title: '暂无可选服务区域',
            icon: 'none'
        })
        return
    }
    showAreaPicker.value = true
}

// 服务类型选择确认
const onConfirmWorkType = (e: any) => {
    const selectedItem = serviceList.value.find(item => item.text === service_type.value)
    if (selectedItem) {
		service_type.value = String(selectedItem.value)
		service_type_str.value = selectedItem.text
    }
    showPicker.value = false
}

// 地区选择确认
const onAreaConfirm = (e: any) => {
    const selectedItem = areaList.value.find(item => item.text === area.value)
    if (selectedItem) {
        area.value = String(selectedItem.value)
        areaStr.value = selectedItem.text
    }
    showAreaPicker.value = false
}

const getRecommendOptions = async () => {
	const res = await api.request.ajax({
		url: '/money/trainingOptions',
		type: 'POST',
	})

	if (res.code === 1) {
		bannerList.value = res.data.banner
		// 转换服务列表数据结构
		serviceList.value = res.data.trainingList.map(item => ({
			text: item.name,
			value: item.value
		}))
		// 转换地区列表数据结构
		areaList.value = res.data.areaList.map(item => ({
			text: item.name,
			value: item.value
		}))

		// 设置分享内容
		setShareApp(res.data.shareData)
		setShareTime(res.data.shareData)
	}
}
const submit = () => {
    // 表单验证
    if (!service_type.value) {
        uni.showToast({
            title: '请选择服务类型',
            icon: 'none'
        })
        return
    }
    if (!area.value) {
        uni.showToast({
            title: '请选择所在区域',
            icon: 'none'
        })
        return
    }
    if (!username.value) {
        uni.showToast({
            title: '请填写姓名',
            icon: 'none'
        })
        return
    }
    if (!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(phone.value)) {
        uni.showToast({
            title: '请填写正确的手机号',
            icon: 'none'
        })
        return
    }

    // 提交数据
    api.request.ajax({
        url: '/money/trainingSave',
        type: 'POST',
        data: {
			item:service_type.value,
			area: area.value,
			uname: username.value,
			phone: phone.value,
			source: store.source || '',
			pid:store.pid||'',
        }
    }).then(res => {
        uni.showToast({
            title: res.msg,
            icon: res.code === 1 ? 'success' : 'none'
        })
        
        if (res.code === 1) {
            setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        }
    }).catch(() => {
        uni.showToast({
            title: '提交失败，请稍后重试',
            icon: 'none'
        })
    })
}
onLoad(() => {
	getRecommendOptions()
})
</script>

<style lang="less" scoped>
.main {
	display: flex;
	flex-direction: column;
	align-items: center;
	height: 100vh;

	.header {
		width: 750rpx;
		// height: 831rpx;
		flex-shrink: 0;
	}
	.swipe{
		height: auto;
	}

	.content {
		margin-top: -100rpx;
		// animation: move 1s forwards;
		// margin-top: 200rpx;
		width: 750rpx;
		border-radius: 40rpx 40rpx 0 0;
		background: linear-gradient(0deg, #FFE7EA, #FFFFFF);
		box-shadow: 0rpx 8rpx 6rpx 2rpx rgba(242, 145, 161, 0.55);
		padding-bottom: 120rpx;
		flex: 1;
		position: relative;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: center;

		.stit {
			width: 560rpx;
			position: absolute;
			top: -20rpx;
		}

		.form {
			height: 100%;
			margin-top: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			.form-item {
				margin-top: 50rpx;
				margin-right: 40rpx;
				display: flex;
				align-items: center;

				.label {
					width: 200rpx;
					text-align: right;

					.reqir {
						color: #D84040;
						margin-right: 10rpx;
					}

					.ft {

						font-size: 34rpx;
						color: #343434;
					}
				}

				input {
					margin-left: 20rpx;
					width: 420rpx;
					height: 72rpx;
					background: #EEE8E8;
					border-radius: 14rpx;
					text-align: center;
				}
			}

			.bottom {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
			}

			.tip {
				margin-top: 60rpx;
				font-size: 22rpx;
				color: #5D5555;
				line-height: 42rpx;
			}

			.button {
				margin-top: 15rpx;
				width: 510rpx;
				height: 98rpx;
				font-size: 40rpx;
				color: #FFFFFF;
				line-height: 98rpx;
				text-align: center;
				background: linear-gradient(135deg, #F95959 0%, #F73030 100%);
				border-radius: 49rpx;
				box-shadow: 0 8rpx 16rpx rgba(249, 89, 89, 0.3);
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
					box-shadow: 0 4rpx 8rpx rgba(249, 89, 89, 0.2);
				}

				/* 禁用状态 */
				&:disabled {
					opacity: 0.7;
					background: linear-gradient(135deg, #FFB1B1 0%, #FF8080 100%);
					box-shadow: none;
				}
			}
		}
	}

}
</style>