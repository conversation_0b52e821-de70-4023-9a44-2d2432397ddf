<template>
	<tm-tabbar _class="tabber" :autoSelect="false" v-model:active="acc" v-if="mainMenuSwitch">
		<tm-tabbar-item url="" text="一家亲" icon="/static/img/tab1_1.svg" unicon="/static/img/tab1_2.svg" activeColor="#E7290A" color="#858585" fontColor="#858585" openType="switchTab" @click="goLink('/pages/index/index')"></tm-tabbar-item>
		<tm-tabbar-item url="" text="简历宝" icon="/static/img/tab2_1.svg" unicon="/static/img/tab2_2.svg" activeColor="#E7290A" color="#858585" fontColor="#858585" openType="switchTab" @click="goLink('/pages/resume_treasure/index')"></tm-tabbar-item>
		<tm-tabbar-item url="" text="赚钱宝" icon="/static/img/tab3_2.png" unicon="/static/img/tab3_2.png" activeColor="#E7290A" color="#858585" fontColor="#858585" openType="switchTab" @click="goLink('/pages/make_money/index')"></tm-tabbar-item>
		<view class="btn_area" @click="goLink('/pages/make_money/index')">
			<image src="/static/img/tab3_1.png" class="btn" mode="widthFix" v-if="acc===2"></image>
			<image src="/static/img/tab3_2.png" class="btn" mode="widthFix" v-if="acc!==2"></image>
		</view>
		<tm-tabbar-item text="接单宝" icon="/static/img/tab4_1.svg" unicon="/static/img/tab4_2.svg" activeColor="#E7290A" color="#858585" fontColor="#858585" openType="switchTab" @click="goLink('/pages/jdb/index')"></tm-tabbar-item>
		<tm-tabbar-item url="" active text="上户宝" icon="/static/img/tab5_1.svg" unicon="/static/img/tab5_2.svg" activeColor="#E7290A" color="#858585" fontColor="#858585" @click="goLink('/pages/housekeeping/index')"></tm-tabbar-item>
	</tm-tabbar>
</template>
<script lang="ts" setup>
	import { ref,computed } from 'vue'
	import { onShow, onLoad } from '@dcloudio/uni-app'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { useStore } from '@/until/mainpinia';
	const store = useStore();
	const mainMenuSwitch = computed(()=>store.mainMenuSwitch)
	const acc = ref(-1)
	let routeList = getCurrentPages()
	let route = routeList[routeList.length-1].route
	if(route==='pages/index/index'){
		acc.value = 0
		uni.hideTabBar()
	}else if(route==='pages/resume_treasure/index'){
		acc.value = 1
		uni.hideTabBar()
	}else if(route==='pages/make_money/index'){
		acc.value = 2
		uni.hideTabBar()
	}else if(route==='pages/jdb/index'){
		acc.value = 3
		uni.hideTabBar()
	}else if(route==='pages/housekeeping/index'){
		acc.value = 4
		uni.hideTabBar()
	}
	// console.log(acc.value);
	
	
</script>

<style lang="less" scoped>
	.tabber{
		width: 750rpx;
		box-shadow: 0px -5rpx 18rpx 0px rgba(181,181,181,1);
		position: relative;
	}
	.btn_area{
		background-color: #fff;
		padding: 6rpx;
		border-radius: 50%;
		position: absolute;
		top: -10rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.btn{
			width: 100rpx;
			
		}
	}
	
</style>