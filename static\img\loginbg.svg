<svg class="waves" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
	viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
	<style>
		.parallax>use {
			transform: translate3d(-90px, 0, 0);
			animation: move-forever 10s cubic-bezier(0.55, 0.5, 0.45, 0.5) infinite;
		}

		.parallax>use:nth-child(1) {
			animation-duration: 4s;
			animation-delay: -2s;
		}

		.parallax>use:nth-child(2) {
			animation-duration: 4s;
		}

		@keyframes move-forever {
			0% {
				transform: translate3d(-90px, 0, 0);
			}

			100% {
				transform: translate3d(85px, 0, 0);
			}
		}
	</style>
	<defs>
		<linearGradient id="waveGradient" gradientTransform="rotate(60)">
			<stop offset="0%" stop-color="rgba(247, 246, 246, 0.5)"></stop>
			<stop offset="100%" stop-color="rgba(247, 246, 246, 0.5)"></stop>
		</linearGradient>
		<path id="gentle-wave" d="M-160 44
		c30 0 58-18 88-18
		s 58 18 88 18
		s 58-18 88-18
		s 58 18 88 18 
		v44h-352z"></path>
	</defs>
	<g class="parallax">
		<use xlink:href="#gentle-wave" x="48" y="0" fill="url(#waveGradient)"></use>
		<use xlink:href="#gentle-wave" x="48" y="3" fill="#F7F6F6"></use>
	</g>
</svg>