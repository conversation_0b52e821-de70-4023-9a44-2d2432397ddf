import { useStore } from '@/until/mainpinia';

interface LinkData {
    navtype?: number;
    appid?: string;
    is_auth?: string | number;
    is_menu?: string | number;
    [key: string]: string | number | boolean | undefined;
}

export function parseURLParams(url: string): { baseUrl: string; params: Record<string, string> } {
    const params: Record<string, string> = {};
    let baseUrl = url;

    if (url.includes('?')) {
        const [urlPart, queryString] = url.split('?');
        baseUrl = urlPart;
        queryString.split('&').forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
                params[key] = value;
            }
        });
    }

    return { baseUrl, params };
}

export function flattenObjectToURLParams(obj: Record<string, any>): string {
    return Object.entries(obj)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
}

export const goLink = (url: string, data?: LinkData) => {
    if (!url) {
        if (data?.is_menu) {
            uni.showModal({
                title: '敬请期待',
                showCancel: false
            });
        }
        return;
    }
    // 处理URL参数
    const { baseUrl, params } = parseURLParams(url);
    const finalData: LinkData = { ...(data || {}), ...params };
    url = baseUrl;

    // 确定导航类型
    const navtype = finalData?.navtype || 1;

    const queryParams = finalData ? flattenObjectToURLParams(finalData) : '';
    const fullUrl = `${url}${queryParams ? '?' + queryParams : ''}`;

    const handleNavigation = () => {
        switch (navtype) {
            case 1:
                uni.navigateTo({
                    url: fullUrl,
                    fail: () => uni.switchTab({ url })
                });
                break;
            case 2:
                if (process.env.UNI_PLATFORM === 'APP-PLUS') {
                    plus.share.getServices(res => {
                        const weixin = res.find(service => service.id === 'weixin');
                        if (weixin) {
                            weixin.launchMiniProgram({
                                id: 'gh_0febc35e3641',
                                type: 0,
                                path: 'pages/index/index'
                            });
                        } else {
                            uni.showModal({
                                title: '跳转失败',
                                content: '您的手机上未安装微信',
                                showCancel: false
                            });
                        }
                    });
                } else {
                    uni.navigateToMiniProgram({
                        appId: finalData?.appid || '',
                        url
                    });
                }
                break;
        }
    };

    if (finalData?.is_auth === 1) {
        checkPhone(handleNavigation);
    } else {
        handleNavigation();
    }
};

export const checkPhone = (callback?: Function) => {
    const store = useStore();
    
    if (process.env.UNI_PLATFORM === 'MP-WEIXIN' && !store.userInfo.phone) {
        uni.showModal({
            title: '提示',
            content: '微信授权您的联系方式，将为您提供更贴心的服务',
            success: (res) => {
                if (res.confirm) {
                    const pages = getCurrentPages();
                    const currentRoute = '/' + pages[pages.length - 1].route;
                    uni.navigateTo({
                        url: '/pages/login/phone',
                        success: (res) => {
                            res.eventChannel.emit('frompath', { data: currentRoute });
                        }
                    });
                } else if (callback) {
                    callback();
                }
            }
        });
    } else if (callback) {
        callback();
    }
};

