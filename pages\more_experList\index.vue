<template>
	<tm-app ref="app">
		<view class="main">
			<view class="card">
				<view class="experience_card" v-for="item in list" :key="item.id">
					<view class="info_item" v-if="item.area">服务区域：{{ item.area }}</view>
					<view class="info_item" v-if="item.remark">服务情况：{{ item.remark }}</view>
				</view>
				<tm-divider v-if="isEnd" label="没有更多了" />
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import { parseParams } from '@/until/parseParams'

const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

const store = useStore()

const list = ref([])
const page = ref(1)
const isEnd = ref(false)
const hid = ref('')
const port = ref('')

const getExperListMore = async () => {
	const res = await api.request.ajax({
		url: '/Center/experListMore',
		type: 'POST',
		data: {
			hid: hid.value,
			port: port.value,
			page: page.value
		}
	})
	if (res.code === 1) {
		if (page.value === 1) {
			list.value = res.data.list
		} else {
			list.value = [...list.value, ...res.data.list]
		}
		if (page.value >= res.data.pageTotal) {
			isEnd.value = true
		}
		setShareApp(res.data.shareData)
		setShareTime(res.data.shareTimeline)
	}
}

onLoad((e) => {
	const params = parseParams(e)
	hid.value = params.hid || hid.value
	port.value = params.port||''
	getExperListMore()
})

onReachBottom(() => {
	if (!isEnd.value) {
		page.value++
		getExperListMore()
	}
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	box-sizing: border-box;

	.card {
		width: 710rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227, 227, 227, 0.87);
		border-radius: 26rpx;
		padding: 30rpx;
		box-sizing: border-box;
		margin-bottom: 30rpx;

		.experience_card {
			position: relative;
			padding: 20rpx 0 20rpx 30rpx;
			border-left: 4rpx solid #FB243C;
			
			&:before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 30rpx;
				width: 20rpx;
				height: 20rpx;
				background: #FB243C;
				border-radius: 50%;
			}
			
			&:last-child {
				margin-bottom: 0;
				
				&:after {
					display: none;
				}
			}
			
			.info_item {
				position: relative;
				font-size: 28rpx;
				color: #606060;
				line-height: 44rpx;
				padding: 8rpx 0;
				
				&:first-child {
					color: #FB243C;
					padding-top: 0;
				}
				
				&:last-child {
					padding-bottom: 0;
					color: #000;
				}
			}
		}


	}
}
</style> 