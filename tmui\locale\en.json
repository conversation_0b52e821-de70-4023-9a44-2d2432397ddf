{"language": "English-US", "index.search.subtext": "Fully compatible with vue3 TypeScript pinia component library", "index.search.tips": "Chinese/English name", "index.search.btntext": "search", "index.com.navtitle": "TMUI All platforms", "index.com.title": "Category Navigation", "index.com.tongyong": "Universal", "index.com.row": "Layout", "index.com.show": "Display", "index.com.form": "Form", "index.com.fd": "Reminder", "index.com.nav": "Navigation", "index.com.yewu": "Business", "index.com.other": "Other", "index.com.tubiao": "Chart", "index.com.tongyongSub": "can't translate", "index.com.rowSub": "can't translate", "index.com.showSub": "can't translate", "index.com.formSub": "can't translate", "index.com.fdSub": "can't translate", "index.com.navSub": "can't translate", "index.com.yewuSub": "can't translate", "index.com.otherSub": "can't translate", "index.com.tubiaoSub": "Echarts 5.3.2", "index.com.render": "Render", "index.com.renderSub": "cavas render", "index.com.pag": "PAG", "index.com.pagSub": "pag animation", "index.com.bottom": "TMUI3.0", "index.com.setLocal": "language setting", "index.com.autoDark": "followDark system", "index.com.love": "Action support", "index.com.loveSub": "Watch an ad", "index.com.themetext": "Dynamically switch theme sore of see docs", "index.com.themeGreen": "Yellow", "index.com.themeBlue": "Blue", "index.com.themeRed": "Red", "index.com.themeDefault": "<PERSON><PERSON><PERSON>", "index.com.themeCustText": "custom", "message.load.text": "Loading", "message.error.text": "Error", "message.info.text": "Tips", "message.warn.text": "Warning", "message.quest.text": "Question", "message.success.text": "Success", "message.disabled.text": "Disabled", "message.wait.text": "Waiting"}