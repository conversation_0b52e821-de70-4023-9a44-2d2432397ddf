.overflow {
	overflow: hidden;
}

.relative {
	position: relative;
}

.absolute {
	position: absolute;
}

.fixed {
	position: fixed;
}

.sticky {
	position: sticky;
}

.fulled-height {
	display: flex;
	align-items: stretch;
}

.fulled-height {
	display: flex;
	align-items: stretch;
}

.zIndex-0 {
	z-index: 0;
}

.zIndex-n0 {
	z-index: 0;
}

.zIndex-1 {
	z-index: 1;
}

.zIndex-n1 {
	z-index: 4;
}

.zIndex-2 {
	z-index: 2;
}

.zIndex-n2 {
	z-index: 8;
}

.zIndex-3 {
	z-index: 3;
}

.zIndex-n3 {
	z-index: 12;
}

.zIndex-4 {
	z-index: 4;
}

.zIndex-n4 {
	z-index: 16;
}

.zIndex-5 {
	z-index: 5;
}

.zIndex-n5 {
	z-index: 20;
}

.zIndex-6 {
	z-index: 6;
}

.zIndex-n6 {
	z-index: 24;
}

.zIndex-7 {
	z-index: 7;
}

.zIndex-n7 {
	z-index: 28;
}

.zIndex-8 {
	z-index: 8;
}

.zIndex-n8 {
	z-index: 32;
}

.zIndex-9 {
	z-index: 9;
}

.zIndex-n9 {
	z-index: 36;
}

.zIndex-10 {
	z-index: 10;
}

.zIndex-n10 {
	z-index: 40;
}

.zIndex-11 {
	z-index: 11;
}

.zIndex-n11 {
	z-index: 44;
}

.zIndex-12 {
	z-index: 12;
}

.zIndex-n12 {
	z-index: 48;
}

.zIndex-13 {
	z-index: 13;
}

.zIndex-n13 {
	z-index: 52;
}

.zIndex-14 {
	z-index: 14;
}

.zIndex-n14 {
	z-index: 56;
}

.zIndex-15 {
	z-index: 15;
}

.zIndex-n15 {
	z-index: 60;
}

.zIndex-16 {
	z-index: 16;
}

.zIndex-n16 {
	z-index: 64;
}

.zIndex-17 {
	z-index: 17;
}

.zIndex-n17 {
	z-index: 68;
}

.zIndex-18 {
	z-index: 18;
}

.zIndex-n18 {
	z-index: 72;
}

.zIndex-19 {
	z-index: 19;
}

.zIndex-n19 {
	z-index: 76;
}

.zIndex-20 {
	z-index: 20;
}

.zIndex-n20 {
	z-index: 80;
}

.zIndex-21 {
	z-index: 21;
}

.zIndex-n21 {
	z-index: 84;
}

.zIndex-22 {
	z-index: 22;
}

.zIndex-n22 {
	z-index: 88;
}

.zIndex-23 {
	z-index: 23;
}

.zIndex-n23 {
	z-index: 92;
}

.zIndex-24 {
	z-index: 24;
}

.zIndex-n24 {
	z-index: 96;
}

.zIndex-25 {
	z-index: 25;
}

.zIndex-n25 {
	z-index: 100;
}

.zIndex-26 {
	z-index: 26;
}

.zIndex-n26 {
	z-index: 104;
}

.text-overflow {
	text-overflow: ellipsis;
}

.text-overflow-1 {
	text-overflow: ellipsis;
	lines: 1;
}

.text-overflow-2 {
	text-overflow: ellipsis;
	lines: 2;
}

.text-overflow-3 {
	text-overflow: ellipsis;
	lines: 3;
}

.text-overflow-4 {
	text-overflow: ellipsis;
	lines: 4;
}

.text-delete {
	text-decoration: line-through;
}

.text-underline {
	text-decoration: underline;
}

.text-size-xxs {
	font-size: 20rpx;
}

.text-size-xs {
	font-size: 22rpx;
}

.text-size-s {
	font-size: 24rpx;
}

.text-size-m {
	font-size: 28rpx;
}

.text-size-n {
	font-size: 30rpx;
}

.text-size-g {
	font-size: 34rpx;
}

.text-size-lg {
	font-size: 36rpx;
}

.text-size-xl {
	font-size: 40rpx;
}

.text-weight-s {
	font-weight: 100;
}

.text-weight-n {
	font-weight: 400;
}

.text-weight-b {
	font-weight: 700;
}

.text-align-left {
	text-align: left;
}

.text-align-right {
	text-align: right;
}

.text-align-center {
	text-align: center;
}

.round-tl-0 {
	border-top-left-radius: 0rpx;
}

.round-tl-1 {
	border-top-left-radius: 4rpx;
}

.round-tl-2 {
	border-top-left-radius: 8rpx;
}

.round-tl-3 {
	border-top-left-radius: 12rpx;
}

.round-tl-4 {
	border-top-left-radius: 16rpx;
}

.round-tl-5 {
	border-top-left-radius: 20rpx;
}

.round-tl-6 {
	border-top-left-radius: 24rpx;
}

.round-tl-7 {
	border-top-left-radius: 28rpx;
}

.round-tl-8 {
	border-top-left-radius: 32rpx;
}

.round-tl-9 {
	border-top-left-radius: 36rpx;
}

.round-tl-10 {
	border-top-left-radius: 40rpx;
}

.round-tl-11 {
	border-top-left-radius: 44rpx;
}

.round-tl-12 {
	border-top-left-radius: 48rpx;
}

.round-tl-13 {
	border-top-left-radius: 52rpx;
}

.round-tl-14 {
	border-top-left-radius: 56rpx;
}

.round-tl-15 {
	border-top-left-radius: 60rpx;
}

.round-tl-16 {
	border-top-left-radius: 64rpx;
}

.round-tl-17 {
	border-top-left-radius: 68rpx;
}

.round-tl-18 {
	border-top-left-radius: 72rpx;
}

.round-tl-19 {
	border-top-left-radius: 76rpx;
}

.round-tl-20 {
	border-top-left-radius: 80rpx;
}

.round-tl-21 {
	border-top-left-radius: 84rpx;
}

.round-tl-22 {
	border-top-left-radius: 88rpx;
}

.round-tl-23 {
	border-top-left-radius: 92rpx;
}

.round-tl-24 {
	border-top-left-radius: 96rpx;
}

.round-tl-25 {
	border-top-left-radius: 100rpx;
}

.round-tr-0 {
	border-top-right-radius: 0rpx;
}

.round-tr-1 {
	border-top-right-radius: 4rpx;
}

.round-tr-2 {
	border-top-right-radius: 8rpx;
}

.round-tr-3 {
	border-top-right-radius: 12rpx;
}

.round-tr-4 {
	border-top-right-radius: 16rpx;
}

.round-tr-5 {
	border-top-right-radius: 20rpx;
}

.round-tr-6 {
	border-top-right-radius: 24rpx;
}

.round-tr-7 {
	border-top-right-radius: 28rpx;
}

.round-tr-8 {
	border-top-right-radius: 32rpx;
}

.round-tr-9 {
	border-top-right-radius: 36rpx;
}

.round-tr-10 {
	border-top-right-radius: 40rpx;
}

.round-tr-11 {
	border-top-right-radius: 44rpx;
}

.round-tr-12 {
	border-top-right-radius: 48rpx;
}

.round-tr-13 {
	border-top-right-radius: 52rpx;
}

.round-tr-14 {
	border-top-right-radius: 56rpx;
}

.round-tr-15 {
	border-top-right-radius: 60rpx;
}

.round-tr-16 {
	border-top-right-radius: 64rpx;
}

.round-tr-17 {
	border-top-right-radius: 68rpx;
}

.round-tr-18 {
	border-top-right-radius: 72rpx;
}

.round-tr-19 {
	border-top-right-radius: 76rpx;
}

.round-tr-20 {
	border-top-right-radius: 80rpx;
}

.round-tr-21 {
	border-top-right-radius: 84rpx;
}

.round-tr-22 {
	border-top-right-radius: 88rpx;
}

.round-tr-23 {
	border-top-right-radius: 92rpx;
}

.round-tr-24 {
	border-top-right-radius: 96rpx;
}

.round-tr-25 {
	border-top-right-radius: 100rpx;
}

.round-bl-0 {
	border-bottom-left-radius: 0rpx;
}

.round-bl-1 {
	border-bottom-left-radius: 4rpx;
}

.round-bl-2 {
	border-bottom-left-radius: 8rpx;
}

.round-bl-3 {
	border-bottom-left-radius: 12rpx;
}

.round-bl-4 {
	border-bottom-left-radius: 16rpx;
}

.round-bl-5 {
	border-bottom-left-radius: 20rpx;
}

.round-bl-6 {
	border-bottom-left-radius: 24rpx;
}

.round-bl-7 {
	border-bottom-left-radius: 28rpx;
}

.round-bl-8 {
	border-bottom-left-radius: 32rpx;
}

.round-bl-9 {
	border-bottom-left-radius: 36rpx;
}

.round-bl-10 {
	border-bottom-left-radius: 40rpx;
}

.round-bl-11 {
	border-bottom-left-radius: 44rpx;
}

.round-bl-12 {
	border-bottom-left-radius: 48rpx;
}

.round-bl-13 {
	border-bottom-left-radius: 52rpx;
}

.round-bl-14 {
	border-bottom-left-radius: 56rpx;
}

.round-bl-15 {
	border-bottom-left-radius: 60rpx;
}

.round-bl-16 {
	border-bottom-left-radius: 64rpx;
}

.round-bl-17 {
	border-bottom-left-radius: 68rpx;
}

.round-bl-18 {
	border-bottom-left-radius: 72rpx;
}

.round-bl-19 {
	border-bottom-left-radius: 76rpx;
}

.round-bl-20 {
	border-bottom-left-radius: 80rpx;
}

.round-bl-21 {
	border-bottom-left-radius: 84rpx;
}

.round-bl-22 {
	border-bottom-left-radius: 88rpx;
}

.round-bl-23 {
	border-bottom-left-radius: 92rpx;
}

.round-bl-24 {
	border-bottom-left-radius: 96rpx;
}

.round-bl-25 {
	border-bottom-left-radius: 100rpx;
}

.round-br-0 {
	border-bottom-right-radius: 0rpx;
}

.round-br-1 {
	border-bottom-right-radius: 4rpx;
}

.round-br-2 {
	border-bottom-right-radius: 8rpx;
}

.round-br-3 {
	border-bottom-right-radius: 12rpx;
}

.round-br-4 {
	border-bottom-right-radius: 16rpx;
}

.round-br-5 {
	border-bottom-right-radius: 20rpx;
}

.round-br-6 {
	border-bottom-right-radius: 24rpx;
}

.round-br-7 {
	border-bottom-right-radius: 28rpx;
}

.round-br-8 {
	border-bottom-right-radius: 32rpx;
}

.round-br-9 {
	border-bottom-right-radius: 36rpx;
}

.round-br-10 {
	border-bottom-right-radius: 40rpx;
}

.round-br-11 {
	border-bottom-right-radius: 44rpx;
}

.round-br-12 {
	border-bottom-right-radius: 48rpx;
}

.round-br-13 {
	border-bottom-right-radius: 52rpx;
}

.round-br-14 {
	border-bottom-right-radius: 56rpx;
}

.round-br-15 {
	border-bottom-right-radius: 60rpx;
}

.round-br-16 {
	border-bottom-right-radius: 64rpx;
}

.round-br-17 {
	border-bottom-right-radius: 68rpx;
}

.round-br-18 {
	border-bottom-right-radius: 72rpx;
}

.round-br-19 {
	border-bottom-right-radius: 76rpx;
}

.round-br-20 {
	border-bottom-right-radius: 80rpx;
}

.round-br-21 {
	border-bottom-right-radius: 84rpx;
}

.round-br-22 {
	border-bottom-right-radius: 88rpx;
}

.round-br-23 {
	border-bottom-right-radius: 92rpx;
}

.round-br-24 {
	border-bottom-right-radius: 96rpx;
}

.round-br-25 {
	border-bottom-right-radius: 100rpx;
}

.round-a-0 {
	border-radius: 0rpx;
}

.round-a-1 {
	border-radius: 4rpx;
}

.round-a-2 {
	border-radius: 8rpx;
}

.round-a-3 {
	border-radius: 12rpx;
}

.round-a-4 {
	border-radius: 16rpx;
}

.round-a-5 {
	border-radius: 20rpx;
}

.round-a-6 {
	border-radius: 24rpx;
}

.round-a-7 {
	border-radius: 28rpx;
}

.round-a-8 {
	border-radius: 32rpx;
}

.round-a-9 {
	border-radius: 36rpx;
}

.round-a-10 {
	border-radius: 40rpx;
}

.round-a-11 {
	border-radius: 44rpx;
}

.round-a-12 {
	border-radius: 48rpx;
}

.round-a-13 {
	border-radius: 52rpx;
}

.round-a-14 {
	border-radius: 56rpx;
}

.round-a-15 {
	border-radius: 60rpx;
}

.round-a-16 {
	border-radius: 64rpx;
}

.round-a-17 {
	border-radius: 68rpx;
}

.round-a-18 {
	border-radius: 72rpx;
}

.round-a-19 {
	border-radius: 76rpx;
}

.round-a-20 {
	border-radius: 80rpx;
}

.round-a-21 {
	border-radius: 84rpx;
}

.round-a-22 {
	border-radius: 88rpx;
}

.round-a-23 {
	border-radius: 92rpx;
}

.round-a-24 {
	border-radius: 96rpx;
}

.round-a-25 {
	border-radius: 100rpx;
}

.round-t-0 {
	border-top-left-radius: 0rpx;
	border-top-right-radius: 0rpx;
}

.round-t-1 {
	border-top-left-radius: 4rpx;
	border-top-right-radius: 4rpx;
}

.round-t-2 {
	border-top-left-radius: 8rpx;
	border-top-right-radius: 8rpx;
}

.round-t-3 {
	border-top-left-radius: 12rpx;
	border-top-right-radius: 12rpx;
}

.round-t-4 {
	border-top-left-radius: 16rpx;
	border-top-right-radius: 16rpx;
}

.round-t-5 {
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
}

.round-t-6 {
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
}

.round-t-7 {
	border-top-left-radius: 28rpx;
	border-top-right-radius: 28rpx;
}

.round-t-8 {
	border-top-left-radius: 32rpx;
	border-top-right-radius: 32rpx;
}

.round-t-9 {
	border-top-left-radius: 36rpx;
	border-top-right-radius: 36rpx;
}

.round-t-10 {
	border-top-left-radius: 40rpx;
	border-top-right-radius: 40rpx;
}

.round-t-11 {
	border-top-left-radius: 44rpx;
	border-top-right-radius: 44rpx;
}

.round-t-12 {
	border-top-left-radius: 48rpx;
	border-top-right-radius: 48rpx;
}

.round-t-13 {
	border-top-left-radius: 52rpx;
	border-top-right-radius: 52rpx;
}

.round-t-14 {
	border-top-left-radius: 56rpx;
	border-top-right-radius: 56rpx;
}

.round-t-15 {
	border-top-left-radius: 60rpx;
	border-top-right-radius: 60rpx;
}

.round-t-16 {
	border-top-left-radius: 64rpx;
	border-top-right-radius: 64rpx;
}

.round-t-17 {
	border-top-left-radius: 68rpx;
	border-top-right-radius: 68rpx;
}

.round-t-18 {
	border-top-left-radius: 72rpx;
	border-top-right-radius: 72rpx;
}

.round-t-19 {
	border-top-left-radius: 76rpx;
	border-top-right-radius: 76rpx;
}

.round-t-20 {
	border-top-left-radius: 80rpx;
	border-top-right-radius: 80rpx;
}

.round-t-21 {
	border-top-left-radius: 84rpx;
	border-top-right-radius: 84rpx;
}

.round-t-22 {
	border-top-left-radius: 88rpx;
	border-top-right-radius: 88rpx;
}

.round-t-23 {
	border-top-left-radius: 92rpx;
	border-top-right-radius: 92rpx;
}

.round-t-24 {
	border-top-left-radius: 96rpx;
	border-top-right-radius: 96rpx;
}

.round-t-25 {
	border-top-left-radius: 100rpx;
	border-top-right-radius: 100rpx;
}

.round-b-0 {
	border-bottom-left-radius: 0rpx;
	border-bottom-right-radius: 0rpx;
}

.round-b-1 {
	border-bottom-left-radius: 4rpx;
	border-bottom-right-radius: 4rpx;
}

.round-b-2 {
	border-bottom-left-radius: 8rpx;
	border-bottom-right-radius: 8rpx;
}

.round-b-3 {
	border-bottom-left-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
}

.round-b-4 {
	border-bottom-left-radius: 16rpx;
	border-bottom-right-radius: 16rpx;
}

.round-b-5 {
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
}

.round-b-6 {
	border-bottom-left-radius: 24rpx;
	border-bottom-right-radius: 24rpx;
}

.round-b-7 {
	border-bottom-left-radius: 28rpx;
	border-bottom-right-radius: 28rpx;
}

.round-b-8 {
	border-bottom-left-radius: 32rpx;
	border-bottom-right-radius: 32rpx;
}

.round-b-9 {
	border-bottom-left-radius: 36rpx;
	border-bottom-right-radius: 36rpx;
}

.round-b-10 {
	border-bottom-left-radius: 40rpx;
	border-bottom-right-radius: 40rpx;
}

.round-b-11 {
	border-bottom-left-radius: 44rpx;
	border-bottom-right-radius: 44rpx;
}

.round-b-12 {
	border-bottom-left-radius: 48rpx;
	border-bottom-right-radius: 48rpx;
}

.round-b-13 {
	border-bottom-left-radius: 52rpx;
	border-bottom-right-radius: 52rpx;
}

.round-b-14 {
	border-bottom-left-radius: 56rpx;
	border-bottom-right-radius: 56rpx;
}

.round-b-15 {
	border-bottom-left-radius: 60rpx;
	border-bottom-right-radius: 60rpx;
}

.round-b-16 {
	border-bottom-left-radius: 64rpx;
	border-bottom-right-radius: 64rpx;
}

.round-b-17 {
	border-bottom-left-radius: 68rpx;
	border-bottom-right-radius: 68rpx;
}

.round-b-18 {
	border-bottom-left-radius: 72rpx;
	border-bottom-right-radius: 72rpx;
}

.round-b-19 {
	border-bottom-left-radius: 76rpx;
	border-bottom-right-radius: 76rpx;
}

.round-b-20 {
	border-bottom-left-radius: 80rpx;
	border-bottom-right-radius: 80rpx;
}

.round-b-21 {
	border-bottom-left-radius: 84rpx;
	border-bottom-right-radius: 84rpx;
}

.round-b-22 {
	border-bottom-left-radius: 88rpx;
	border-bottom-right-radius: 88rpx;
}

.round-b-23 {
	border-bottom-left-radius: 92rpx;
	border-bottom-right-radius: 92rpx;
}

.round-b-24 {
	border-bottom-left-radius: 96rpx;
	border-bottom-right-radius: 96rpx;
}

.round-b-25 {
	border-bottom-left-radius: 100rpx;
	border-bottom-right-radius: 100rpx;
}

.round-l-0 {
	border-top-left-radius: 0rpx;
	border-bottom-left-radius: 0rpx;
}

.round-l-1 {
	border-top-left-radius: 4rpx;
	border-bottom-left-radius: 4rpx;
}

.round-l-2 {
	border-top-left-radius: 8rpx;
	border-bottom-left-radius: 8rpx;
}

.round-l-3 {
	border-top-left-radius: 12rpx;
	border-bottom-left-radius: 12rpx;
}

.round-l-4 {
	border-top-left-radius: 16rpx;
	border-bottom-left-radius: 16rpx;
}

.round-l-5 {
	border-top-left-radius: 20rpx;
	border-bottom-left-radius: 20rpx;
}

.round-l-6 {
	border-top-left-radius: 24rpx;
	border-bottom-left-radius: 24rpx;
}

.round-l-7 {
	border-top-left-radius: 28rpx;
	border-bottom-left-radius: 28rpx;
}

.round-l-8 {
	border-top-left-radius: 32rpx;
	border-bottom-left-radius: 32rpx;
}

.round-l-9 {
	border-top-left-radius: 36rpx;
	border-bottom-left-radius: 36rpx;
}

.round-l-10 {
	border-top-left-radius: 40rpx;
	border-bottom-left-radius: 40rpx;
}

.round-l-11 {
	border-top-left-radius: 44rpx;
	border-bottom-left-radius: 44rpx;
}

.round-l-12 {
	border-top-left-radius: 48rpx;
	border-bottom-left-radius: 48rpx;
}

.round-l-13 {
	border-top-left-radius: 52rpx;
	border-bottom-left-radius: 52rpx;
}

.round-l-14 {
	border-top-left-radius: 56rpx;
	border-bottom-left-radius: 56rpx;
}

.round-l-15 {
	border-top-left-radius: 60rpx;
	border-bottom-left-radius: 60rpx;
}

.round-l-16 {
	border-top-left-radius: 64rpx;
	border-bottom-left-radius: 64rpx;
}

.round-l-17 {
	border-top-left-radius: 68rpx;
	border-bottom-left-radius: 68rpx;
}

.round-l-18 {
	border-top-left-radius: 72rpx;
	border-bottom-left-radius: 72rpx;
}

.round-l-19 {
	border-top-left-radius: 76rpx;
	border-bottom-left-radius: 76rpx;
}

.round-l-20 {
	border-top-left-radius: 80rpx;
	border-bottom-left-radius: 80rpx;
}

.round-l-21 {
	border-top-left-radius: 84rpx;
	border-bottom-left-radius: 84rpx;
}

.round-l-22 {
	border-top-left-radius: 88rpx;
	border-bottom-left-radius: 88rpx;
}

.round-l-23 {
	border-top-left-radius: 92rpx;
	border-bottom-left-radius: 92rpx;
}

.round-l-24 {
	border-top-left-radius: 96rpx;
	border-bottom-left-radius: 96rpx;
}

.round-l-25 {
	border-top-left-radius: 100rpx;
	border-bottom-left-radius: 100rpx;
}

.round-r-0 {
	border-top-right-radius: 0rpx;
	border-bottom-right-radius: 0rpx;
}

.round-r-1 {
	border-top-right-radius: 4rpx;
	border-bottom-right-radius: 4rpx;
}

.round-r-2 {
	border-top-right-radius: 8rpx;
	border-bottom-right-radius: 8rpx;
}

.round-r-3 {
	border-top-right-radius: 12rpx;
	border-bottom-right-radius: 12rpx;
}

.round-r-4 {
	border-top-right-radius: 16rpx;
	border-bottom-right-radius: 16rpx;
}

.round-r-5 {
	border-top-right-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
}

.round-r-6 {
	border-top-right-radius: 24rpx;
	border-bottom-right-radius: 24rpx;
}

.round-r-7 {
	border-top-right-radius: 28rpx;
	border-bottom-right-radius: 28rpx;
}

.round-r-8 {
	border-top-right-radius: 32rpx;
	border-bottom-right-radius: 32rpx;
}

.round-r-9 {
	border-top-right-radius: 36rpx;
	border-bottom-right-radius: 36rpx;
}

.round-r-10 {
	border-top-right-radius: 40rpx;
	border-bottom-right-radius: 40rpx;
}

.round-r-11 {
	border-top-right-radius: 44rpx;
	border-bottom-right-radius: 44rpx;
}

.round-r-12 {
	border-top-right-radius: 48rpx;
	border-bottom-right-radius: 48rpx;
}

.round-r-13 {
	border-top-right-radius: 52rpx;
	border-bottom-right-radius: 52rpx;
}

.round-r-14 {
	border-top-right-radius: 56rpx;
	border-bottom-right-radius: 56rpx;
}

.round-r-15 {
	border-top-right-radius: 60rpx;
	border-bottom-right-radius: 60rpx;
}

.round-r-16 {
	border-top-right-radius: 64rpx;
	border-bottom-right-radius: 64rpx;
}

.round-r-17 {
	border-top-right-radius: 68rpx;
	border-bottom-right-radius: 68rpx;
}

.round-r-18 {
	border-top-right-radius: 72rpx;
	border-bottom-right-radius: 72rpx;
}

.round-r-19 {
	border-top-right-radius: 76rpx;
	border-bottom-right-radius: 76rpx;
}

.round-r-20 {
	border-top-right-radius: 80rpx;
	border-bottom-right-radius: 80rpx;
}

.round-r-21 {
	border-top-right-radius: 84rpx;
	border-bottom-right-radius: 84rpx;
}

.round-r-22 {
	border-top-right-radius: 88rpx;
	border-bottom-right-radius: 88rpx;
}

.round-r-23 {
	border-top-right-radius: 92rpx;
	border-bottom-right-radius: 92rpx;
}

.round-r-24 {
	border-top-right-radius: 96rpx;
	border-bottom-right-radius: 96rpx;
}

.round-r-25 {
	border-top-right-radius: 100rpx;
	border-bottom-right-radius: 100rpx;
}

.round-0 {
	border-radius: 0rpx;
}

.round-1 {
	border-radius: 4rpx;
}

.round-2 {
	border-radius: 8rpx;
}

.round-3 {
	border-radius: 12rpx;
}

.round-4 {
	border-radius: 16rpx;
}

.round-5 {
	border-radius: 20rpx;
}

.round-6 {
	border-radius: 24rpx;
}

.round-7 {
	border-radius: 28rpx;
}

.round-8 {
	border-radius: 32rpx;
}

.round-9 {
	border-radius: 36rpx;
}

.round-10 {
	border-radius: 40rpx;
}

.round-11 {
	border-radius: 44rpx;
}

.round-12 {
	border-radius: 48rpx;
}

.round-13 {
	border-radius: 52rpx;
}

.round-14 {
	border-radius: 56rpx;
}

.round-15 {
	border-radius: 60rpx;
}

.round-16 {
	border-radius: 64rpx;
}

.round-17 {
	border-radius: 68rpx;
}

.round-18 {
	border-radius: 72rpx;
}

.round-19 {
	border-radius: 76rpx;
}

.round-20 {
	border-radius: 80rpx;
}

.round-21 {
	border-radius: 84rpx;
}

.round-22 {
	border-radius: 88rpx;
}

.round-23 {
	border-radius: 92rpx;
}

.round-24 {
	border-radius: 96rpx;
}

.round-25 {
	border-radius: 100rpx;
}

.round-26 {
	border-radius: 104rpx;
}

.rounded {
	border-radius: 50%;
}

.opacity-0 {
	opacity: 0;
}

.opacity-1 {
	opacity: 0.1;
}

.opacity-2 {
	opacity: 0.2;
}

.opacity-3 {
	opacity: 0.3;
}

.opacity-4 {
	opacity: 0.4;
}

.opacity-5 {
	opacity: 0.5;
}

.opacity-6 {
	opacity: 0.6;
}

.opacity-7 {
	opacity: 0.7;
}

.opacity-8 {
	opacity: 0.8;
}

.opacity-9 {
	opacity: 0.9;
}

.opacity-10 {
	opacity: 1;
}

.border-0 {
	border: solid 0rpx #f5f5f5;
}

.border-0-bk {
	border: solid 0rpx #282828;
}

.border {
	border: solid 2rpx #f5f5f5;
}

.border-bk {
	border: solid 2rpx #282828;
}

.border-1 {
	border: solid 2px #f5f5f5;
}

.border-1-bk {
	border: solid 2px #f5f5f5;
}

.border-2 {
	border: solid 4px #f5f5f5;
}

.border-2-bk {
	border: solid 4px #f5f5f5;
}

.border-3 {
	border: solid 6px #f5f5f5;
}

.border-3-bk {
	border: solid 6px #f5f5f5;
}

.border-4 {
	border: solid 8px #f5f5f5;
}

.border-4-bk {
	border: solid 8px #f5f5f5;
}

.border-5 {
	border: solid 10px #f5f5f5;
}

.border-5-bk {
	border: solid 10px #f5f5f5;
}

.border-l-1 {
	border-left: solid 2rpx whitesmoke;
}

.border-l-1-bk {
	border-left: solid 2rpx #282828;
}

.border-l-2 {
	border-left: solid 4rpx whitesmoke;
}

.border-l-2-bk {
	border-left: solid 4rpx #282828;
}

.border-l-3 {
	border-left: solid 6rpx whitesmoke;
}

.border-l-3-bk {
	border-left: solid 6rpx #282828;
}

.border-l-4 {
	border-left: solid 8rpx whitesmoke;
}

.border-l-4-bk {
	border-left: solid 8rpx #282828;
}

.border-l-5 {
	border-left: solid 10rpx whitesmoke;
}

.border-l-5-bk {
	border-left: solid 10rpx #282828;
}

.border-r-1 {
	border-right: solid 2rpx whitesmoke;
}

.border-r-1-bk {
	border-right: solid 2rpx #282828;
}

.border-r-2 {
	border-right: solid 4rpx whitesmoke;
}

.border-r-2-bk {
	border-right: solid 4rpx #282828;
}

.border-r-3 {
	border-right: solid 6rpx whitesmoke;
}

.border-r-3-bk {
	border-right: solid 6rpx #282828;
}

.border-r-4 {
	border-right: solid 8rpx whitesmoke;
}

.border-r-4-bk {
	border-right: solid 8rpx #282828;
}

.border-r-5 {
	border-right: solid 10rpx whitesmoke;
}

.border-r-5-bk {
	border-right: solid 10rpx #282828;
}

.border-t-1 {
	border-top: solid 2rpx whitesmoke;
}

.border-t-1-bk {
	border-top: solid 2rpx #282828;
}

.border-t-2 {
	border-top: solid 4rpx whitesmoke;
}

.border-t-2-bk {
	border-top: solid 4rpx #282828;
}

.border-t-3 {
	border-top: solid 6rpx whitesmoke;
}

.border-t-3-bk {
	border-top: solid 6rpx #282828;
}

.border-t-4 {
	border-top: solid 8rpx whitesmoke;
}

.border-t-4-bk {
	border-top: solid 8rpx #282828;
}

.border-t-5 {
	border-top: solid 10rpx whitesmoke;
}

.border-t-5-bk {
	border-top: solid 10rpx #282828;
}

.border-b-1 {
	border-bottom: solid 2rpx whitesmoke;
}

.border-b-1-bk {
	border-bottom: solid 2rpx #282828;
}

.border-b-2 {
	border-bottom: solid 4rpx whitesmoke;
}

.border-b-2-bk {
	border-bottom: solid 4rpx #282828;
}

.border-b-3 {
	border-bottom: solid 6rpx whitesmoke;
}

.border-b-3-bk {
	border-bottom: solid 6rpx #282828;
}

.border-b-4 {
	border-bottom: solid 8rpx whitesmoke;
}

.border-b-4-bk {
	border-bottom: solid 8rpx #282828;
}

.border-b-5 {
	border-bottom: solid 10rpx whitesmoke;
}

.border-b-5-bk {
	border-bottom: solid 10rpx #282828;
}

.border-a-1 {
	border: solid 2rpx whitesmoke;
}

.border-a-1-bk {
	border: solid 2rpx #282828;
}

.border-a-2 {
	border: solid 4rpx whitesmoke;
}

.border-a-2-bk {
	border: solid 4rpx #282828;
}

.border-a-3 {
	border: solid 6rpx whitesmoke;
}

.border-a-3-bk {
	border: solid 6rpx #282828;
}

.border-a-4 {
	border: solid 8rpx whitesmoke;
}

.border-a-4-bk {
	border: solid 8rpx #282828;
}

.border-a-5 {
	border: solid 10rpx whitesmoke;
}

.border-a-5-bk {
	border: solid 10rpx #282828;
}

.pa-0 {
	padding: 0rpx;
}

.pa-1 {
	padding: 1rpx;
}

.pa-2 {
	padding: 2rpx;
}

.pa-3 {
	padding: 3rpx;
}

.pa-4 {
	padding: 4rpx;
}

.pa-5 {
	padding: 5rpx;
}

.pa-6 {
	padding: 6rpx;
}

.pa-7 {
	padding: 7rpx;
}

.pa-8 {
	padding: 8rpx;
}

.pa-9 {
	padding: 9rpx;
}

.pa-10 {
	padding: 10rpx;
}

.pa-11 {
	padding: 11rpx;
}

.pa-12 {
	padding: 12rpx;
}

.pa-13 {
	padding: 13rpx;
}

.pa-14 {
	padding: 14rpx;
}

.pa-15 {
	padding: 15rpx;
}

.pa-16 {
	padding: 16rpx;
}

.pa-17 {
	padding: 17rpx;
}

.pa-18 {
	padding: 18rpx;
}

.pa-19 {
	padding: 19rpx;
}

.pa-20 {
	padding: 20rpx;
}

.pa-21 {
	padding: 21rpx;
}

.pa-22 {
	padding: 22rpx;
}

.pa-23 {
	padding: 23rpx;
}

.pa-24 {
	padding: 24rpx;
}

.pa-25 {
	padding: 25rpx;
}

.pa-26 {
	padding: 26rpx;
}

.pa-27 {
	padding: 27rpx;
}

.pa-28 {
	padding: 28rpx;
}

.pa-29 {
	padding: 29rpx;
}

.pa-30 {
	padding: 30rpx;
}

.pa-31 {
	padding: 31rpx;
}

.pa-32 {
	padding: 32rpx;
}

.pa-33 {
	padding: 33rpx;
}

.pa-34 {
	padding: 34rpx;
}

.pa-35 {
	padding: 35rpx;
}

.pa-36 {
	padding: 36rpx;
}

.pa-37 {
	padding: 37rpx;
}

.pa-38 {
	padding: 38rpx;
}

.pa-39 {
	padding: 39rpx;
}

.pa-40 {
	padding: 40rpx;
}

.pa-41 {
	padding: 41rpx;
}

.pa-42 {
	padding: 42rpx;
}

.pa-43 {
	padding: 43rpx;
}

.pa-44 {
	padding: 44rpx;
}

.pa-45 {
	padding: 45rpx;
}

.pa-46 {
	padding: 46rpx;
}

.pa-47 {
	padding: 47rpx;
}

.pa-48 {
	padding: 48rpx;
}

.pa-49 {
	padding: 49rpx;
}

.pa-50 {
	padding: 50rpx;
}

.pa-n1 {
	padding: 4rpx;
}

.pa-n2 {
	padding: 8rpx;
}

.pa-n3 {
	padding: 12rpx;
}

.pa-n4 {
	padding: 16rpx;
}

.pa-n5 {
	padding: 20rpx;
}

.pa-n6 {
	padding: 24rpx;
}

.pa-n7 {
	padding: 28rpx;
}

.pa-n8 {
	padding: 32rpx;
}

.pa-n9 {
	padding: 36rpx;
}

.pa-n10 {
	padding: 40rpx;
}

.pa-n11 {
	padding: 44rpx;
}

.pa-n12 {
	padding: 48rpx;
}

.pa-n13 {
	padding: 52rpx;
}

.pa-n14 {
	padding: 56rpx;
}

.pa-n15 {
	padding: 60rpx;
}

.pa-n16 {
	padding: 64rpx;
}

.pa-n17 {
	padding: 68rpx;
}

.pa-n18 {
	padding: 72rpx;
}

.pa-n19 {
	padding: 76rpx;
}

.pa-n20 {
	padding: 80rpx;
}

.pa-n21 {
	padding: 84rpx;
}

.pa-n22 {
	padding: 88rpx;
}

.pa-n23 {
	padding: 92rpx;
}

.pa-n24 {
	padding: 96rpx;
}

.pa-n25 {
	padding: 100rpx;
}

.pt-0 {
	padding-top: 0rpx;
}

.pt-1 {
	padding-top: 1rpx;
}

.pt-2 {
	padding-top: 2rpx;
}

.pt-3 {
	padding-top: 3rpx;
}

.pt-4 {
	padding-top: 4rpx;
}

.pt-5 {
	padding-top: 5rpx;
}

.pt-6 {
	padding-top: 6rpx;
}

.pt-7 {
	padding-top: 7rpx;
}

.pt-8 {
	padding-top: 8rpx;
}

.pt-9 {
	padding-top: 9rpx;
}

.pt-10 {
	padding-top: 10rpx;
}

.pt-11 {
	padding-top: 11rpx;
}

.pt-12 {
	padding-top: 12rpx;
}

.pt-13 {
	padding-top: 13rpx;
}

.pt-14 {
	padding-top: 14rpx;
}

.pt-15 {
	padding-top: 15rpx;
}

.pt-16 {
	padding-top: 16rpx;
}

.pt-17 {
	padding-top: 17rpx;
}

.pt-18 {
	padding-top: 18rpx;
}

.pt-19 {
	padding-top: 19rpx;
}

.pt-20 {
	padding-top: 20rpx;
}

.pt-21 {
	padding-top: 21rpx;
}

.pt-22 {
	padding-top: 22rpx;
}

.pt-23 {
	padding-top: 23rpx;
}

.pt-24 {
	padding-top: 24rpx;
}

.pt-25 {
	padding-top: 25rpx;
}

.pt-26 {
	padding-top: 26rpx;
}

.pt-27 {
	padding-top: 27rpx;
}

.pt-28 {
	padding-top: 28rpx;
}

.pt-29 {
	padding-top: 29rpx;
}

.pt-30 {
	padding-top: 30rpx;
}

.pt-31 {
	padding-top: 31rpx;
}

.pt-32 {
	padding-top: 32rpx;
}

.pt-33 {
	padding-top: 33rpx;
}

.pt-34 {
	padding-top: 34rpx;
}

.pt-35 {
	padding-top: 35rpx;
}

.pt-36 {
	padding-top: 36rpx;
}

.pt-37 {
	padding-top: 37rpx;
}

.pt-38 {
	padding-top: 38rpx;
}

.pt-39 {
	padding-top: 39rpx;
}

.pt-40 {
	padding-top: 40rpx;
}

.pt-41 {
	padding-top: 41rpx;
}

.pt-42 {
	padding-top: 42rpx;
}

.pt-43 {
	padding-top: 43rpx;
}

.pt-44 {
	padding-top: 44rpx;
}

.pt-45 {
	padding-top: 45rpx;
}

.pt-46 {
	padding-top: 46rpx;
}

.pt-47 {
	padding-top: 47rpx;
}

.pt-48 {
	padding-top: 48rpx;
}

.pt-49 {
	padding-top: 49rpx;
}

.pt-50 {
	padding-top: 50rpx;
}

.pt-n1 {
	padding-top: 4rpx;
}

.pt-n2 {
	padding-top: 8rpx;
}

.pt-n3 {
	padding-top: 12rpx;
}

.pt-n4 {
	padding-top: 16rpx;
}

.pt-n5 {
	padding-top: 20rpx;
}

.pt-n6 {
	padding-top: 24rpx;
}

.pt-n7 {
	padding-top: 28rpx;
}

.pt-n8 {
	padding-top: 32rpx;
}

.pt-n9 {
	padding-top: 36rpx;
}

.pt-n10 {
	padding-top: 40rpx;
}

.pt-n11 {
	padding-top: 44rpx;
}

.pt-n12 {
	padding-top: 48rpx;
}

.pt-n13 {
	padding-top: 52rpx;
}

.pt-n14 {
	padding-top: 56rpx;
}

.pt-n15 {
	padding-top: 60rpx;
}

.pt-n16 {
	padding-top: 64rpx;
}

.pt-n17 {
	padding-top: 68rpx;
}

.pt-n18 {
	padding-top: 72rpx;
}

.pt-n19 {
	padding-top: 76rpx;
}

.pt-n20 {
	padding-top: 80rpx;
}

.pt-n21 {
	padding-top: 84rpx;
}

.pt-n22 {
	padding-top: 88rpx;
}

.pt-n23 {
	padding-top: 92rpx;
}

.pt-n24 {
	padding-top: 96rpx;
}

.pt-n25 {
	padding-top: 100rpx;
}

.pr-0 {
	padding-right: 0rpx;
}

.pr-1 {
	padding-right: 1rpx;
}

.pr-2 {
	padding-right: 2rpx;
}

.pr-3 {
	padding-right: 3rpx;
}

.pr-4 {
	padding-right: 4rpx;
}

.pr-5 {
	padding-right: 5rpx;
}

.pr-6 {
	padding-right: 6rpx;
}

.pr-7 {
	padding-right: 7rpx;
}

.pr-8 {
	padding-right: 8rpx;
}

.pr-9 {
	padding-right: 9rpx;
}

.pr-10 {
	padding-right: 10rpx;
}

.pr-11 {
	padding-right: 11rpx;
}

.pr-12 {
	padding-right: 12rpx;
}

.pr-13 {
	padding-right: 13rpx;
}

.pr-14 {
	padding-right: 14rpx;
}

.pr-15 {
	padding-right: 15rpx;
}

.pr-16 {
	padding-right: 16rpx;
}

.pr-17 {
	padding-right: 17rpx;
}

.pr-18 {
	padding-right: 18rpx;
}

.pr-19 {
	padding-right: 19rpx;
}

.pr-20 {
	padding-right: 20rpx;
}

.pr-21 {
	padding-right: 21rpx;
}

.pr-22 {
	padding-right: 22rpx;
}

.pr-23 {
	padding-right: 23rpx;
}

.pr-24 {
	padding-right: 24rpx;
}

.pr-25 {
	padding-right: 25rpx;
}

.pr-26 {
	padding-right: 26rpx;
}

.pr-27 {
	padding-right: 27rpx;
}

.pr-28 {
	padding-right: 28rpx;
}

.pr-29 {
	padding-right: 29rpx;
}

.pr-30 {
	padding-right: 30rpx;
}

.pr-31 {
	padding-right: 31rpx;
}

.pr-32 {
	padding-right: 32rpx;
}

.pr-33 {
	padding-right: 33rpx;
}

.pr-34 {
	padding-right: 34rpx;
}

.pr-35 {
	padding-right: 35rpx;
}

.pr-36 {
	padding-right: 36rpx;
}

.pr-37 {
	padding-right: 37rpx;
}

.pr-38 {
	padding-right: 38rpx;
}

.pr-39 {
	padding-right: 39rpx;
}

.pr-40 {
	padding-right: 40rpx;
}

.pr-41 {
	padding-right: 41rpx;
}

.pr-42 {
	padding-right: 42rpx;
}

.pr-43 {
	padding-right: 43rpx;
}

.pr-44 {
	padding-right: 44rpx;
}

.pr-45 {
	padding-right: 45rpx;
}

.pr-46 {
	padding-right: 46rpx;
}

.pr-47 {
	padding-right: 47rpx;
}

.pr-48 {
	padding-right: 48rpx;
}

.pr-49 {
	padding-right: 49rpx;
}

.pr-50 {
	padding-right: 50rpx;
}

.pr-n1 {
	padding-right: 4rpx;
}

.pr-n2 {
	padding-right: 8rpx;
}

.pr-n3 {
	padding-right: 12rpx;
}

.pr-n4 {
	padding-right: 16rpx;
}

.pr-n5 {
	padding-right: 20rpx;
}

.pr-n6 {
	padding-right: 24rpx;
}

.pr-n7 {
	padding-right: 28rpx;
}

.pr-n8 {
	padding-right: 32rpx;
}

.pr-n9 {
	padding-right: 36rpx;
}

.pr-n10 {
	padding-right: 40rpx;
}

.pr-n11 {
	padding-right: 44rpx;
}

.pr-n12 {
	padding-right: 48rpx;
}

.pr-n13 {
	padding-right: 52rpx;
}

.pr-n14 {
	padding-right: 56rpx;
}

.pr-n15 {
	padding-right: 60rpx;
}

.pr-n16 {
	padding-right: 64rpx;
}

.pr-n17 {
	padding-right: 68rpx;
}

.pr-n18 {
	padding-right: 72rpx;
}

.pr-n19 {
	padding-right: 76rpx;
}

.pr-n20 {
	padding-right: 80rpx;
}

.pr-n21 {
	padding-right: 84rpx;
}

.pr-n22 {
	padding-right: 88rpx;
}

.pr-n23 {
	padding-right: 92rpx;
}

.pr-n24 {
	padding-right: 96rpx;
}

.pr-n25 {
	padding-right: 100rpx;
}

.pb-0 {
	padding-bottom: 0rpx;
}

.pb-1 {
	padding-bottom: 1rpx;
}

.pb-2 {
	padding-bottom: 2rpx;
}

.pb-3 {
	padding-bottom: 3rpx;
}

.pb-4 {
	padding-bottom: 4rpx;
}

.pb-5 {
	padding-bottom: 5rpx;
}

.pb-6 {
	padding-bottom: 6rpx;
}

.pb-7 {
	padding-bottom: 7rpx;
}

.pb-8 {
	padding-bottom: 8rpx;
}

.pb-9 {
	padding-bottom: 9rpx;
}

.pb-10 {
	padding-bottom: 10rpx;
}

.pb-11 {
	padding-bottom: 11rpx;
}

.pb-12 {
	padding-bottom: 12rpx;
}

.pb-13 {
	padding-bottom: 13rpx;
}

.pb-14 {
	padding-bottom: 14rpx;
}

.pb-15 {
	padding-bottom: 15rpx;
}

.pb-16 {
	padding-bottom: 16rpx;
}

.pb-17 {
	padding-bottom: 17rpx;
}

.pb-18 {
	padding-bottom: 18rpx;
}

.pb-19 {
	padding-bottom: 19rpx;
}

.pb-20 {
	padding-bottom: 20rpx;
}

.pb-21 {
	padding-bottom: 21rpx;
}

.pb-22 {
	padding-bottom: 22rpx;
}

.pb-23 {
	padding-bottom: 23rpx;
}

.pb-24 {
	padding-bottom: 24rpx;
}

.pb-25 {
	padding-bottom: 25rpx;
}

.pb-26 {
	padding-bottom: 26rpx;
}

.pb-27 {
	padding-bottom: 27rpx;
}

.pb-28 {
	padding-bottom: 28rpx;
}

.pb-29 {
	padding-bottom: 29rpx;
}

.pb-30 {
	padding-bottom: 30rpx;
}

.pb-31 {
	padding-bottom: 31rpx;
}

.pb-32 {
	padding-bottom: 32rpx;
}

.pb-33 {
	padding-bottom: 33rpx;
}

.pb-34 {
	padding-bottom: 34rpx;
}

.pb-35 {
	padding-bottom: 35rpx;
}

.pb-36 {
	padding-bottom: 36rpx;
}

.pb-37 {
	padding-bottom: 37rpx;
}

.pb-38 {
	padding-bottom: 38rpx;
}

.pb-39 {
	padding-bottom: 39rpx;
}

.pb-40 {
	padding-bottom: 40rpx;
}

.pb-41 {
	padding-bottom: 41rpx;
}

.pb-42 {
	padding-bottom: 42rpx;
}

.pb-43 {
	padding-bottom: 43rpx;
}

.pb-44 {
	padding-bottom: 44rpx;
}

.pb-45 {
	padding-bottom: 45rpx;
}

.pb-46 {
	padding-bottom: 46rpx;
}

.pb-47 {
	padding-bottom: 47rpx;
}

.pb-48 {
	padding-bottom: 48rpx;
}

.pb-49 {
	padding-bottom: 49rpx;
}

.pb-50 {
	padding-bottom: 50rpx;
}

.pb-n1 {
	padding-bottom: 4rpx;
}

.pb-n2 {
	padding-bottom: 8rpx;
}

.pb-n3 {
	padding-bottom: 12rpx;
}

.pb-n4 {
	padding-bottom: 16rpx;
}

.pb-n5 {
	padding-bottom: 20rpx;
}

.pb-n6 {
	padding-bottom: 24rpx;
}

.pb-n7 {
	padding-bottom: 28rpx;
}

.pb-n8 {
	padding-bottom: 32rpx;
}

.pb-n9 {
	padding-bottom: 36rpx;
}

.pb-n10 {
	padding-bottom: 40rpx;
}

.pb-n11 {
	padding-bottom: 44rpx;
}

.pb-n12 {
	padding-bottom: 48rpx;
}

.pb-n13 {
	padding-bottom: 52rpx;
}

.pb-n14 {
	padding-bottom: 56rpx;
}

.pb-n15 {
	padding-bottom: 60rpx;
}

.pb-n16 {
	padding-bottom: 64rpx;
}

.pb-n17 {
	padding-bottom: 68rpx;
}

.pb-n18 {
	padding-bottom: 72rpx;
}

.pb-n19 {
	padding-bottom: 76rpx;
}

.pb-n20 {
	padding-bottom: 80rpx;
}

.pb-n21 {
	padding-bottom: 84rpx;
}

.pb-n22 {
	padding-bottom: 88rpx;
}

.pb-n23 {
	padding-bottom: 92rpx;
}

.pb-n24 {
	padding-bottom: 96rpx;
}

.pb-n25 {
	padding-bottom: 100rpx;
}

.pl-0 {
	padding-left: 0rpx;
}

.pl-1 {
	padding-left: 1rpx;
}

.pl-2 {
	padding-left: 2rpx;
}

.pl-3 {
	padding-left: 3rpx;
}

.pl-4 {
	padding-left: 4rpx;
}

.pl-5 {
	padding-left: 5rpx;
}

.pl-6 {
	padding-left: 6rpx;
}

.pl-7 {
	padding-left: 7rpx;
}

.pl-8 {
	padding-left: 8rpx;
}

.pl-9 {
	padding-left: 9rpx;
}

.pl-10 {
	padding-left: 10rpx;
}

.pl-11 {
	padding-left: 11rpx;
}

.pl-12 {
	padding-left: 12rpx;
}

.pl-13 {
	padding-left: 13rpx;
}

.pl-14 {
	padding-left: 14rpx;
}

.pl-15 {
	padding-left: 15rpx;
}

.pl-16 {
	padding-left: 16rpx;
}

.pl-17 {
	padding-left: 17rpx;
}

.pl-18 {
	padding-left: 18rpx;
}

.pl-19 {
	padding-left: 19rpx;
}

.pl-20 {
	padding-left: 20rpx;
}

.pl-21 {
	padding-left: 21rpx;
}

.pl-22 {
	padding-left: 22rpx;
}

.pl-23 {
	padding-left: 23rpx;
}

.pl-24 {
	padding-left: 24rpx;
}

.pl-25 {
	padding-left: 25rpx;
}

.pl-26 {
	padding-left: 26rpx;
}

.pl-27 {
	padding-left: 27rpx;
}

.pl-28 {
	padding-left: 28rpx;
}

.pl-29 {
	padding-left: 29rpx;
}

.pl-30 {
	padding-left: 30rpx;
}

.pl-31 {
	padding-left: 31rpx;
}

.pl-32 {
	padding-left: 32rpx;
}

.pl-33 {
	padding-left: 33rpx;
}

.pl-34 {
	padding-left: 34rpx;
}

.pl-35 {
	padding-left: 35rpx;
}

.pl-36 {
	padding-left: 36rpx;
}

.pl-37 {
	padding-left: 37rpx;
}

.pl-38 {
	padding-left: 38rpx;
}

.pl-39 {
	padding-left: 39rpx;
}

.pl-40 {
	padding-left: 40rpx;
}

.pl-41 {
	padding-left: 41rpx;
}

.pl-42 {
	padding-left: 42rpx;
}

.pl-43 {
	padding-left: 43rpx;
}

.pl-44 {
	padding-left: 44rpx;
}

.pl-45 {
	padding-left: 45rpx;
}

.pl-46 {
	padding-left: 46rpx;
}

.pl-47 {
	padding-left: 47rpx;
}

.pl-48 {
	padding-left: 48rpx;
}

.pl-49 {
	padding-left: 49rpx;
}

.pl-50 {
	padding-left: 50rpx;
}

.pl-n1 {
	padding-left: 4rpx;
}

.pl-n2 {
	padding-left: 8rpx;
}

.pl-n3 {
	padding-left: 12rpx;
}

.pl-n4 {
	padding-left: 16rpx;
}

.pl-n5 {
	padding-left: 20rpx;
}

.pl-n6 {
	padding-left: 24rpx;
}

.pl-n7 {
	padding-left: 28rpx;
}

.pl-n8 {
	padding-left: 32rpx;
}

.pl-n9 {
	padding-left: 36rpx;
}

.pl-n10 {
	padding-left: 40rpx;
}

.pl-n11 {
	padding-left: 44rpx;
}

.pl-n12 {
	padding-left: 48rpx;
}

.pl-n13 {
	padding-left: 52rpx;
}

.pl-n14 {
	padding-left: 56rpx;
}

.pl-n15 {
	padding-left: 60rpx;
}

.pl-n16 {
	padding-left: 64rpx;
}

.pl-n17 {
	padding-left: 68rpx;
}

.pl-n18 {
	padding-left: 72rpx;
}

.pl-n19 {
	padding-left: 76rpx;
}

.pl-n20 {
	padding-left: 80rpx;
}

.pl-n21 {
	padding-left: 84rpx;
}

.pl-n22 {
	padding-left: 88rpx;
}

.pl-n23 {
	padding-left: 92rpx;
}

.pl-n24 {
	padding-left: 96rpx;
}

.pl-n25 {
	padding-left: 100rpx;
}

.px-0 {
	padding-left: 0rpx;
	padding-right: 0rpx;
}

.px-1 {
	padding-left: 1rpx;
	padding-right: 1rpx;
}

.px-2 {
	padding-left: 2rpx;
	padding-right: 2rpx;
}

.px-3 {
	padding-left: 3rpx;
	padding-right: 3rpx;
}

.px-4 {
	padding-left: 4rpx;
	padding-right: 4rpx;
}

.px-5 {
	padding-left: 5rpx;
	padding-right: 5rpx;
}

.px-6 {
	padding-left: 6rpx;
	padding-right: 6rpx;
}

.px-7 {
	padding-left: 7rpx;
	padding-right: 7rpx;
}

.px-8 {
	padding-left: 8rpx;
	padding-right: 8rpx;
}

.px-9 {
	padding-left: 9rpx;
	padding-right: 9rpx;
}

.px-10 {
	padding-left: 10rpx;
	padding-right: 10rpx;
}

.px-11 {
	padding-left: 11rpx;
	padding-right: 11rpx;
}

.px-12 {
	padding-left: 12rpx;
	padding-right: 12rpx;
}

.px-13 {
	padding-left: 13rpx;
	padding-right: 13rpx;
}

.px-14 {
	padding-left: 14rpx;
	padding-right: 14rpx;
}

.px-15 {
	padding-left: 15rpx;
	padding-right: 15rpx;
}

.px-16 {
	padding-left: 16rpx;
	padding-right: 16rpx;
}

.px-17 {
	padding-left: 17rpx;
	padding-right: 17rpx;
}

.px-18 {
	padding-left: 18rpx;
	padding-right: 18rpx;
}

.px-19 {
	padding-left: 19rpx;
	padding-right: 19rpx;
}

.px-20 {
	padding-left: 20rpx;
	padding-right: 20rpx;
}

.px-21 {
	padding-left: 21rpx;
	padding-right: 21rpx;
}

.px-22 {
	padding-left: 22rpx;
	padding-right: 22rpx;
}

.px-23 {
	padding-left: 23rpx;
	padding-right: 23rpx;
}

.px-24 {
	padding-left: 24rpx;
	padding-right: 24rpx;
}

.px-25 {
	padding-left: 25rpx;
	padding-right: 25rpx;
}

.px-26 {
	padding-left: 26rpx;
	padding-right: 26rpx;
}

.px-27 {
	padding-left: 27rpx;
	padding-right: 27rpx;
}

.px-28 {
	padding-left: 28rpx;
	padding-right: 28rpx;
}

.px-29 {
	padding-left: 29rpx;
	padding-right: 29rpx;
}

.px-30 {
	padding-left: 30rpx;
	padding-right: 30rpx;
}

.px-31 {
	padding-left: 31rpx;
	padding-right: 31rpx;
}

.px-32 {
	padding-left: 32rpx;
	padding-right: 32rpx;
}

.px-33 {
	padding-left: 33rpx;
	padding-right: 33rpx;
}

.px-34 {
	padding-left: 34rpx;
	padding-right: 34rpx;
}

.px-35 {
	padding-left: 35rpx;
	padding-right: 35rpx;
}

.px-36 {
	padding-left: 36rpx;
	padding-right: 36rpx;
}

.px-37 {
	padding-left: 37rpx;
	padding-right: 37rpx;
}

.px-38 {
	padding-left: 38rpx;
	padding-right: 38rpx;
}

.px-39 {
	padding-left: 39rpx;
	padding-right: 39rpx;
}

.px-40 {
	padding-left: 40rpx;
	padding-right: 40rpx;
}

.px-41 {
	padding-left: 41rpx;
	padding-right: 41rpx;
}

.px-42 {
	padding-left: 42rpx;
	padding-right: 42rpx;
}

.px-43 {
	padding-left: 43rpx;
	padding-right: 43rpx;
}

.px-44 {
	padding-left: 44rpx;
	padding-right: 44rpx;
}

.px-45 {
	padding-left: 45rpx;
	padding-right: 45rpx;
}

.px-46 {
	padding-left: 46rpx;
	padding-right: 46rpx;
}

.px-47 {
	padding-left: 47rpx;
	padding-right: 47rpx;
}

.px-48 {
	padding-left: 48rpx;
	padding-right: 48rpx;
}

.px-49 {
	padding-left: 49rpx;
	padding-right: 49rpx;
}

.px-50 {
	padding-left: 50rpx;
	padding-right: 50rpx;
}

.px-n1 {
	padding-left: 4rpx;
	padding-right: 4rpx;
}

.px-n2 {
	padding-left: 8rpx;
	padding-right: 8rpx;
}

.px-n3 {
	padding-left: 12rpx;
	padding-right: 12rpx;
}

.px-n4 {
	padding-left: 16rpx;
	padding-right: 16rpx;
}

.px-n5 {
	padding-left: 20rpx;
	padding-right: 20rpx;
}

.px-n6 {
	padding-left: 24rpx;
	padding-right: 24rpx;
}

.px-n7 {
	padding-left: 28rpx;
	padding-right: 28rpx;
}

.px-n8 {
	padding-left: 32rpx;
	padding-right: 32rpx;
}

.px-n9 {
	padding-left: 36rpx;
	padding-right: 36rpx;
}

.px-n10 {
	padding-left: 40rpx;
	padding-right: 40rpx;
}

.px-n11 {
	padding-left: 44rpx;
	padding-right: 44rpx;
}

.px-n12 {
	padding-left: 48rpx;
	padding-right: 48rpx;
}

.px-n13 {
	padding-left: 52rpx;
	padding-right: 52rpx;
}

.px-n14 {
	padding-left: 56rpx;
	padding-right: 56rpx;
}

.px-n15 {
	padding-left: 60rpx;
	padding-right: 60rpx;
}

.px-n16 {
	padding-left: 64rpx;
	padding-right: 64rpx;
}

.px-n17 {
	padding-left: 68rpx;
	padding-right: 68rpx;
}

.px-n18 {
	padding-left: 72rpx;
	padding-right: 72rpx;
}

.px-n19 {
	padding-left: 76rpx;
	padding-right: 76rpx;
}

.px-n20 {
	padding-left: 80rpx;
	padding-right: 80rpx;
}

.px-n21 {
	padding-left: 84rpx;
	padding-right: 84rpx;
}

.px-n22 {
	padding-left: 88rpx;
	padding-right: 88rpx;
}

.px-n23 {
	padding-left: 92rpx;
	padding-right: 92rpx;
}

.px-n24 {
	padding-left: 96rpx;
	padding-right: 96rpx;
}

.px-n25 {
	padding-left: 100rpx;
	padding-right: 100rpx;
}

.py-0 {
	padding-top: 0rpx;
	padding-bottom: 0rpx;
}

.py-1 {
	padding-top: 1rpx;
	padding-bottom: 1rpx;
}

.py-2 {
	padding-top: 2rpx;
	padding-bottom: 2rpx;
}

.py-3 {
	padding-top: 3rpx;
	padding-bottom: 3rpx;
}

.py-4 {
	padding-top: 4rpx;
	padding-bottom: 4rpx;
}

.py-5 {
	padding-top: 5rpx;
	padding-bottom: 5rpx;
}

.py-6 {
	padding-top: 6rpx;
	padding-bottom: 6rpx;
}

.py-7 {
	padding-top: 7rpx;
	padding-bottom: 7rpx;
}

.py-8 {
	padding-top: 8rpx;
	padding-bottom: 8rpx;
}

.py-9 {
	padding-top: 9rpx;
	padding-bottom: 9rpx;
}

.py-10 {
	padding-top: 10rpx;
	padding-bottom: 10rpx;
}

.py-11 {
	padding-top: 11rpx;
	padding-bottom: 11rpx;
}

.py-12 {
	padding-top: 12rpx;
	padding-bottom: 12rpx;
}

.py-13 {
	padding-top: 13rpx;
	padding-bottom: 13rpx;
}

.py-14 {
	padding-top: 14rpx;
	padding-bottom: 14rpx;
}

.py-15 {
	padding-top: 15rpx;
	padding-bottom: 15rpx;
}

.py-16 {
	padding-top: 16rpx;
	padding-bottom: 16rpx;
}

.py-17 {
	padding-top: 17rpx;
	padding-bottom: 17rpx;
}

.py-18 {
	padding-top: 18rpx;
	padding-bottom: 18rpx;
}

.py-19 {
	padding-top: 19rpx;
	padding-bottom: 19rpx;
}

.py-20 {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.py-21 {
	padding-top: 21rpx;
	padding-bottom: 21rpx;
}

.py-22 {
	padding-top: 22rpx;
	padding-bottom: 22rpx;
}

.py-23 {
	padding-top: 23rpx;
	padding-bottom: 23rpx;
}

.py-24 {
	padding-top: 24rpx;
	padding-bottom: 24rpx;
}

.py-25 {
	padding-top: 25rpx;
	padding-bottom: 25rpx;
}

.py-26 {
	padding-top: 26rpx;
	padding-bottom: 26rpx;
}

.py-27 {
	padding-top: 27rpx;
	padding-bottom: 27rpx;
}

.py-28 {
	padding-top: 28rpx;
	padding-bottom: 28rpx;
}

.py-29 {
	padding-top: 29rpx;
	padding-bottom: 29rpx;
}

.py-30 {
	padding-top: 30rpx;
	padding-bottom: 30rpx;
}

.py-31 {
	padding-top: 31rpx;
	padding-bottom: 31rpx;
}

.py-32 {
	padding-top: 32rpx;
	padding-bottom: 32rpx;
}

.py-33 {
	padding-top: 33rpx;
	padding-bottom: 33rpx;
}

.py-34 {
	padding-top: 34rpx;
	padding-bottom: 34rpx;
}

.py-35 {
	padding-top: 35rpx;
	padding-bottom: 35rpx;
}

.py-36 {
	padding-top: 36rpx;
	padding-bottom: 36rpx;
}

.py-37 {
	padding-top: 37rpx;
	padding-bottom: 37rpx;
}

.py-38 {
	padding-top: 38rpx;
	padding-bottom: 38rpx;
}

.py-39 {
	padding-top: 39rpx;
	padding-bottom: 39rpx;
}

.py-40 {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
}

.py-41 {
	padding-top: 41rpx;
	padding-bottom: 41rpx;
}

.py-42 {
	padding-top: 42rpx;
	padding-bottom: 42rpx;
}

.py-43 {
	padding-top: 43rpx;
	padding-bottom: 43rpx;
}

.py-44 {
	padding-top: 44rpx;
	padding-bottom: 44rpx;
}

.py-45 {
	padding-top: 45rpx;
	padding-bottom: 45rpx;
}

.py-46 {
	padding-top: 46rpx;
	padding-bottom: 46rpx;
}

.py-47 {
	padding-top: 47rpx;
	padding-bottom: 47rpx;
}

.py-48 {
	padding-top: 48rpx;
	padding-bottom: 48rpx;
}

.py-49 {
	padding-top: 49rpx;
	padding-bottom: 49rpx;
}

.py-50 {
	padding-top: 50rpx;
	padding-bottom: 50rpx;
}

.py-n1 {
	padding-top: 4rpx;
	padding-bottom: 4rpx;
}

.py-n2 {
	padding-top: 8rpx;
	padding-bottom: 8rpx;
}

.py-n3 {
	padding-top: 12rpx;
	padding-bottom: 12rpx;
}

.py-n4 {
	padding-top: 16rpx;
	padding-bottom: 16rpx;
}

.py-n5 {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}

.py-n6 {
	padding-top: 24rpx;
	padding-bottom: 24rpx;
}

.py-n7 {
	padding-top: 28rpx;
	padding-bottom: 28rpx;
}

.py-n8 {
	padding-top: 32rpx;
	padding-bottom: 32rpx;
}

.py-n9 {
	padding-top: 36rpx;
	padding-bottom: 36rpx;
}

.py-n10 {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
}

.py-n11 {
	padding-top: 44rpx;
	padding-bottom: 44rpx;
}

.py-n12 {
	padding-top: 48rpx;
	padding-bottom: 48rpx;
}

.py-n13 {
	padding-top: 52rpx;
	padding-bottom: 52rpx;
}

.py-n14 {
	padding-top: 56rpx;
	padding-bottom: 56rpx;
}

.py-n15 {
	padding-top: 60rpx;
	padding-bottom: 60rpx;
}

.py-n16 {
	padding-top: 64rpx;
	padding-bottom: 64rpx;
}

.py-n17 {
	padding-top: 68rpx;
	padding-bottom: 68rpx;
}

.py-n18 {
	padding-top: 72rpx;
	padding-bottom: 72rpx;
}

.py-n19 {
	padding-top: 76rpx;
	padding-bottom: 76rpx;
}

.py-n20 {
	padding-top: 80rpx;
	padding-bottom: 80rpx;
}

.py-n21 {
	padding-top: 84rpx;
	padding-bottom: 84rpx;
}

.py-n22 {
	padding-top: 88rpx;
	padding-bottom: 88rpx;
}

.py-n23 {
	padding-top: 92rpx;
	padding-bottom: 92rpx;
}

.py-n24 {
	padding-top: 96rpx;
	padding-bottom: 96rpx;
}

.py-n25 {
	padding-top: 100rpx;
	padding-bottom: 100rpx;
}

.ma-0 {
	margin: 0rpx;
}

.ma-1 {
	margin: 1rpx;
}

.ma-2 {
	margin: 2rpx;
}

.ma-3 {
	margin: 3rpx;
}

.ma-4 {
	margin: 4rpx;
}

.ma-5 {
	margin: 5rpx;
}

.ma-6 {
	margin: 6rpx;
}

.ma-7 {
	margin: 7rpx;
}

.ma-8 {
	margin: 8rpx;
}

.ma-9 {
	margin: 9rpx;
}

.ma-10 {
	margin: 10rpx;
}

.ma-11 {
	margin: 11rpx;
}

.ma-12 {
	margin: 12rpx;
}

.ma-13 {
	margin: 13rpx;
}

.ma-14 {
	margin: 14rpx;
}

.ma-15 {
	margin: 15rpx;
}

.ma-16 {
	margin: 16rpx;
}

.ma-17 {
	margin: 17rpx;
}

.ma-18 {
	margin: 18rpx;
}

.ma-19 {
	margin: 19rpx;
}

.ma-20 {
	margin: 20rpx;
}

.ma-21 {
	margin: 21rpx;
}

.ma-22 {
	margin: 22rpx;
}

.ma-23 {
	margin: 23rpx;
}

.ma-24 {
	margin: 24rpx;
}

.ma-25 {
	margin: 25rpx;
}

.ma-26 {
	margin: 26rpx;
}

.ma-27 {
	margin: 27rpx;
}

.ma-28 {
	margin: 28rpx;
}

.ma-29 {
	margin: 29rpx;
}

.ma-30 {
	margin: 30rpx;
}

.ma-31 {
	margin: 31rpx;
}

.ma-32 {
	margin: 32rpx;
}

.ma-33 {
	margin: 33rpx;
}

.ma-34 {
	margin: 34rpx;
}

.ma-35 {
	margin: 35rpx;
}

.ma-36 {
	margin: 36rpx;
}

.ma-37 {
	margin: 37rpx;
}

.ma-38 {
	margin: 38rpx;
}

.ma-39 {
	margin: 39rpx;
}

.ma-40 {
	margin: 40rpx;
}

.ma-41 {
	margin: 41rpx;
}

.ma-42 {
	margin: 42rpx;
}

.ma-43 {
	margin: 43rpx;
}

.ma-44 {
	margin: 44rpx;
}

.ma-45 {
	margin: 45rpx;
}

.ma-46 {
	margin: 46rpx;
}

.ma-47 {
	margin: 47rpx;
}

.ma-48 {
	margin: 48rpx;
}

.ma-49 {
	margin: 49rpx;
}

.ma-50 {
	margin: 50rpx;
}

.ma-n1 {
	margin: 4rpx;
}

.ma-n2 {
	margin: 8rpx;
}

.ma-n3 {
	margin: 12rpx;
}

.ma-n4 {
	margin: 16rpx;
}

.ma-n5 {
	margin: 20rpx;
}

.ma-n6 {
	margin: 24rpx;
}

.ma-n7 {
	margin: 28rpx;
}

.ma-n8 {
	margin: 32rpx;
}

.ma-n9 {
	margin: 36rpx;
}

.ma-n10 {
	margin: 40rpx;
}

.ma-n11 {
	margin: 44rpx;
}

.ma-n12 {
	margin: 48rpx;
}

.ma-n13 {
	margin: 52rpx;
}

.ma-n14 {
	margin: 56rpx;
}

.ma-n15 {
	margin: 60rpx;
}

.ma-n16 {
	margin: 64rpx;
}

.ma-n17 {
	margin: 68rpx;
}

.ma-n18 {
	margin: 72rpx;
}

.ma-n19 {
	margin: 76rpx;
}

.ma-n20 {
	margin: 80rpx;
}

.ma-n21 {
	margin: 84rpx;
}

.ma-n22 {
	margin: 88rpx;
}

.ma-n23 {
	margin: 92rpx;
}

.ma-n24 {
	margin: 96rpx;
}

.ma-n25 {
	margin: 100rpx;
}

.mt-0 {
	margin-top: 0rpx;
}

.mt--0 {
	margin-top: -0rpx;
}

.mt-1 {
	margin-top: 1rpx;
}

.mt--1 {
	margin-top: -1rpx;
}

.mt-2 {
	margin-top: 2rpx;
}

.mt--2 {
	margin-top: -2rpx;
}

.mt-3 {
	margin-top: 3rpx;
}

.mt--3 {
	margin-top: -3rpx;
}

.mt-4 {
	margin-top: 4rpx;
}

.mt--4 {
	margin-top: -4rpx;
}

.mt-5 {
	margin-top: 5rpx;
}

.mt--5 {
	margin-top: -5rpx;
}

.mt-6 {
	margin-top: 6rpx;
}

.mt--6 {
	margin-top: -6rpx;
}

.mt-7 {
	margin-top: 7rpx;
}

.mt--7 {
	margin-top: -7rpx;
}

.mt-8 {
	margin-top: 8rpx;
}

.mt--8 {
	margin-top: -8rpx;
}

.mt-9 {
	margin-top: 9rpx;
}

.mt--9 {
	margin-top: -9rpx;
}

.mt-10 {
	margin-top: 10rpx;
}

.mt--10 {
	margin-top: -10rpx;
}

.mt-11 {
	margin-top: 11rpx;
}

.mt--11 {
	margin-top: -11rpx;
}

.mt-12 {
	margin-top: 12rpx;
}

.mt--12 {
	margin-top: -12rpx;
}

.mt-13 {
	margin-top: 13rpx;
}

.mt--13 {
	margin-top: -13rpx;
}

.mt-14 {
	margin-top: 14rpx;
}

.mt--14 {
	margin-top: -14rpx;
}

.mt-15 {
	margin-top: 15rpx;
}

.mt--15 {
	margin-top: -15rpx;
}

.mt-16 {
	margin-top: 16rpx;
}

.mt--16 {
	margin-top: -16rpx;
}

.mt-17 {
	margin-top: 17rpx;
}

.mt--17 {
	margin-top: -17rpx;
}

.mt-18 {
	margin-top: 18rpx;
}

.mt--18 {
	margin-top: -18rpx;
}

.mt-19 {
	margin-top: 19rpx;
}

.mt--19 {
	margin-top: -19rpx;
}

.mt-20 {
	margin-top: 20rpx;
}

.mt--20 {
	margin-top: -20rpx;
}

.mt-21 {
	margin-top: 21rpx;
}

.mt--21 {
	margin-top: -21rpx;
}

.mt-22 {
	margin-top: 22rpx;
}

.mt--22 {
	margin-top: -22rpx;
}

.mt-23 {
	margin-top: 23rpx;
}

.mt--23 {
	margin-top: -23rpx;
}

.mt-24 {
	margin-top: 24rpx;
}

.mt--24 {
	margin-top: -24rpx;
}

.mt-25 {
	margin-top: 25rpx;
}

.mt--25 {
	margin-top: -25rpx;
}

.mt-26 {
	margin-top: 26rpx;
}

.mt--26 {
	margin-top: -26rpx;
}

.mt-27 {
	margin-top: 27rpx;
}

.mt--27 {
	margin-top: -27rpx;
}

.mt-28 {
	margin-top: 28rpx;
}

.mt--28 {
	margin-top: -28rpx;
}

.mt-29 {
	margin-top: 29rpx;
}

.mt--29 {
	margin-top: -29rpx;
}

.mt-30 {
	margin-top: 30rpx;
}

.mt--30 {
	margin-top: -30rpx;
}

.mt-31 {
	margin-top: 31rpx;
}

.mt--31 {
	margin-top: -31rpx;
}

.mt-32 {
	margin-top: 32rpx;
}

.mt--32 {
	margin-top: -32rpx;
}

.mt-33 {
	margin-top: 33rpx;
}

.mt--33 {
	margin-top: -33rpx;
}

.mt-34 {
	margin-top: 34rpx;
}

.mt--34 {
	margin-top: -34rpx;
}

.mt-35 {
	margin-top: 35rpx;
}

.mt--35 {
	margin-top: -35rpx;
}

.mt-36 {
	margin-top: 36rpx;
}

.mt--36 {
	margin-top: -36rpx;
}

.mt-37 {
	margin-top: 37rpx;
}

.mt--37 {
	margin-top: -37rpx;
}

.mt-38 {
	margin-top: 38rpx;
}

.mt--38 {
	margin-top: -38rpx;
}

.mt-39 {
	margin-top: 39rpx;
}

.mt--39 {
	margin-top: -39rpx;
}

.mt-40 {
	margin-top: 40rpx;
}

.mt--40 {
	margin-top: -40rpx;
}

.mt-41 {
	margin-top: 41rpx;
}

.mt--41 {
	margin-top: -41rpx;
}

.mt-42 {
	margin-top: 42rpx;
}

.mt--42 {
	margin-top: -42rpx;
}

.mt-43 {
	margin-top: 43rpx;
}

.mt--43 {
	margin-top: -43rpx;
}

.mt-44 {
	margin-top: 44rpx;
}

.mt--44 {
	margin-top: -44rpx;
}

.mt-45 {
	margin-top: 45rpx;
}

.mt--45 {
	margin-top: -45rpx;
}

.mt-46 {
	margin-top: 46rpx;
}

.mt--46 {
	margin-top: -46rpx;
}

.mt-47 {
	margin-top: 47rpx;
}

.mt--47 {
	margin-top: -47rpx;
}

.mt-48 {
	margin-top: 48rpx;
}

.mt--48 {
	margin-top: -48rpx;
}

.mt-49 {
	margin-top: 49rpx;
}

.mt--49 {
	margin-top: -49rpx;
}

.mt-50 {
	margin-top: 50rpx;
}

.mt--50 {
	margin-top: -50rpx;
}

.mt-n1 {
	margin-top: 4rpx;
}

.mt--n1 {
	margin-top: -4rpx;
}

.mt-n2 {
	margin-top: 8rpx;
}

.mt--n2 {
	margin-top: -8rpx;
}

.mt-n3 {
	margin-top: 12rpx;
}

.mt--n3 {
	margin-top: -12rpx;
}

.mt-n4 {
	margin-top: 16rpx;
}

.mt--n4 {
	margin-top: -16rpx;
}

.mt-n5 {
	margin-top: 20rpx;
}

.mt--n5 {
	margin-top: -20rpx;
}

.mt-n6 {
	margin-top: 24rpx;
}

.mt--n6 {
	margin-top: -24rpx;
}

.mt-n7 {
	margin-top: 28rpx;
}

.mt--n7 {
	margin-top: -28rpx;
}

.mt-n8 {
	margin-top: 32rpx;
}

.mt--n8 {
	margin-top: -32rpx;
}

.mt-n9 {
	margin-top: 36rpx;
}

.mt--n9 {
	margin-top: -36rpx;
}

.mt-n10 {
	margin-top: 40rpx;
}

.mt--n10 {
	margin-top: -40rpx;
}

.mt-n11 {
	margin-top: 44rpx;
}

.mt--n11 {
	margin-top: -44rpx;
}

.mt-n12 {
	margin-top: 48rpx;
}

.mt--n12 {
	margin-top: -48rpx;
}

.mt-n13 {
	margin-top: 52rpx;
}

.mt--n13 {
	margin-top: -52rpx;
}

.mt-n14 {
	margin-top: 56rpx;
}

.mt--n14 {
	margin-top: -56rpx;
}

.mt-n15 {
	margin-top: 60rpx;
}

.mt--n15 {
	margin-top: -60rpx;
}

.mt-n16 {
	margin-top: 64rpx;
}

.mt--n16 {
	margin-top: -64rpx;
}

.mt-n17 {
	margin-top: 68rpx;
}

.mt--n17 {
	margin-top: -68rpx;
}

.mt-n18 {
	margin-top: 72rpx;
}

.mt--n18 {
	margin-top: -72rpx;
}

.mt-n19 {
	margin-top: 76rpx;
}

.mt--n19 {
	margin-top: -76rpx;
}

.mt-n20 {
	margin-top: 80rpx;
}

.mt--n20 {
	margin-top: -80rpx;
}

.mt-n21 {
	margin-top: 84rpx;
}

.mt--n21 {
	margin-top: -84rpx;
}

.mt-n22 {
	margin-top: 88rpx;
}

.mt--n22 {
	margin-top: -88rpx;
}

.mt-n23 {
	margin-top: 92rpx;
}

.mt--n23 {
	margin-top: -92rpx;
}

.mt-n24 {
	margin-top: 96rpx;
}

.mt--n24 {
	margin-top: -96rpx;
}

.mt-n25 {
	margin-top: 100rpx;
}

.mt--n25 {
	margin-top: -100rpx;
}

.mr-0 {
	margin-right: 0rpx;
}

.mr--0 {
	margin-right: -0rpx;
}

.mr-1 {
	margin-right: 1rpx;
}

.mr--1 {
	margin-right: -1rpx;
}

.mr-2 {
	margin-right: 2rpx;
}

.mr--2 {
	margin-right: -2rpx;
}

.mr-3 {
	margin-right: 3rpx;
}

.mr--3 {
	margin-right: -3rpx;
}

.mr-4 {
	margin-right: 4rpx;
}

.mr--4 {
	margin-right: -4rpx;
}

.mr-5 {
	margin-right: 5rpx;
}

.mr--5 {
	margin-right: -5rpx;
}

.mr-6 {
	margin-right: 6rpx;
}

.mr--6 {
	margin-right: -6rpx;
}

.mr-7 {
	margin-right: 7rpx;
}

.mr--7 {
	margin-right: -7rpx;
}

.mr-8 {
	margin-right: 8rpx;
}

.mr--8 {
	margin-right: -8rpx;
}

.mr-9 {
	margin-right: 9rpx;
}

.mr--9 {
	margin-right: -9rpx;
}

.mr-10 {
	margin-right: 10rpx;
}

.mr--10 {
	margin-right: -10rpx;
}

.mr-11 {
	margin-right: 11rpx;
}

.mr--11 {
	margin-right: -11rpx;
}

.mr-12 {
	margin-right: 12rpx;
}

.mr--12 {
	margin-right: -12rpx;
}

.mr-13 {
	margin-right: 13rpx;
}

.mr--13 {
	margin-right: -13rpx;
}

.mr-14 {
	margin-right: 14rpx;
}

.mr--14 {
	margin-right: -14rpx;
}

.mr-15 {
	margin-right: 15rpx;
}

.mr--15 {
	margin-right: -15rpx;
}

.mr-16 {
	margin-right: 16rpx;
}

.mr--16 {
	margin-right: -16rpx;
}

.mr-17 {
	margin-right: 17rpx;
}

.mr--17 {
	margin-right: -17rpx;
}

.mr-18 {
	margin-right: 18rpx;
}

.mr--18 {
	margin-right: -18rpx;
}

.mr-19 {
	margin-right: 19rpx;
}

.mr--19 {
	margin-right: -19rpx;
}

.mr-20 {
	margin-right: 20rpx;
}

.mr--20 {
	margin-right: -20rpx;
}

.mr-21 {
	margin-right: 21rpx;
}

.mr--21 {
	margin-right: -21rpx;
}

.mr-22 {
	margin-right: 22rpx;
}

.mr--22 {
	margin-right: -22rpx;
}

.mr-23 {
	margin-right: 23rpx;
}

.mr--23 {
	margin-right: -23rpx;
}

.mr-24 {
	margin-right: 24rpx;
}

.mr--24 {
	margin-right: -24rpx;
}

.mr-25 {
	margin-right: 25rpx;
}

.mr--25 {
	margin-right: -25rpx;
}

.mr-26 {
	margin-right: 26rpx;
}

.mr--26 {
	margin-right: -26rpx;
}

.mr-27 {
	margin-right: 27rpx;
}

.mr--27 {
	margin-right: -27rpx;
}

.mr-28 {
	margin-right: 28rpx;
}

.mr--28 {
	margin-right: -28rpx;
}

.mr-29 {
	margin-right: 29rpx;
}

.mr--29 {
	margin-right: -29rpx;
}

.mr-30 {
	margin-right: 30rpx;
}

.mr--30 {
	margin-right: -30rpx;
}

.mr-31 {
	margin-right: 31rpx;
}

.mr--31 {
	margin-right: -31rpx;
}

.mr-32 {
	margin-right: 32rpx;
}

.mr--32 {
	margin-right: -32rpx;
}

.mr-33 {
	margin-right: 33rpx;
}

.mr--33 {
	margin-right: -33rpx;
}

.mr-34 {
	margin-right: 34rpx;
}

.mr--34 {
	margin-right: -34rpx;
}

.mr-35 {
	margin-right: 35rpx;
}

.mr--35 {
	margin-right: -35rpx;
}

.mr-36 {
	margin-right: 36rpx;
}

.mr--36 {
	margin-right: -36rpx;
}

.mr-37 {
	margin-right: 37rpx;
}

.mr--37 {
	margin-right: -37rpx;
}

.mr-38 {
	margin-right: 38rpx;
}

.mr--38 {
	margin-right: -38rpx;
}

.mr-39 {
	margin-right: 39rpx;
}

.mr--39 {
	margin-right: -39rpx;
}

.mr-40 {
	margin-right: 40rpx;
}

.mr--40 {
	margin-right: -40rpx;
}

.mr-41 {
	margin-right: 41rpx;
}

.mr--41 {
	margin-right: -41rpx;
}

.mr-42 {
	margin-right: 42rpx;
}

.mr--42 {
	margin-right: -42rpx;
}

.mr-43 {
	margin-right: 43rpx;
}

.mr--43 {
	margin-right: -43rpx;
}

.mr-44 {
	margin-right: 44rpx;
}

.mr--44 {
	margin-right: -44rpx;
}

.mr-45 {
	margin-right: 45rpx;
}

.mr--45 {
	margin-right: -45rpx;
}

.mr-46 {
	margin-right: 46rpx;
}

.mr--46 {
	margin-right: -46rpx;
}

.mr-47 {
	margin-right: 47rpx;
}

.mr--47 {
	margin-right: -47rpx;
}

.mr-48 {
	margin-right: 48rpx;
}

.mr--48 {
	margin-right: -48rpx;
}

.mr-49 {
	margin-right: 49rpx;
}

.mr--49 {
	margin-right: -49rpx;
}

.mr-50 {
	margin-right: 50rpx;
}

.mr--50 {
	margin-right: -50rpx;
}

.mr-n1 {
	margin-right: 4rpx;
}

.mr--n1 {
	margin-right: -4rpx;
}

.mr-n2 {
	margin-right: 8rpx;
}

.mr--n2 {
	margin-right: -8rpx;
}

.mr-n3 {
	margin-right: 12rpx;
}

.mr--n3 {
	margin-right: -12rpx;
}

.mr-n4 {
	margin-right: 16rpx;
}

.mr--n4 {
	margin-right: -16rpx;
}

.mr-n5 {
	margin-right: 20rpx;
}

.mr--n5 {
	margin-right: -20rpx;
}

.mr-n6 {
	margin-right: 24rpx;
}

.mr--n6 {
	margin-right: -24rpx;
}

.mr-n7 {
	margin-right: 28rpx;
}

.mr--n7 {
	margin-right: -28rpx;
}

.mr-n8 {
	margin-right: 32rpx;
}

.mr--n8 {
	margin-right: -32rpx;
}

.mr-n9 {
	margin-right: 36rpx;
}

.mr--n9 {
	margin-right: -36rpx;
}

.mr-n10 {
	margin-right: 40rpx;
}

.mr--n10 {
	margin-right: -40rpx;
}

.mr-n11 {
	margin-right: 44rpx;
}

.mr--n11 {
	margin-right: -44rpx;
}

.mr-n12 {
	margin-right: 48rpx;
}

.mr--n12 {
	margin-right: -48rpx;
}

.mr-n13 {
	margin-right: 52rpx;
}

.mr--n13 {
	margin-right: -52rpx;
}

.mr-n14 {
	margin-right: 56rpx;
}

.mr--n14 {
	margin-right: -56rpx;
}

.mr-n15 {
	margin-right: 60rpx;
}

.mr--n15 {
	margin-right: -60rpx;
}

.mr-n16 {
	margin-right: 64rpx;
}

.mr--n16 {
	margin-right: -64rpx;
}

.mr-n17 {
	margin-right: 68rpx;
}

.mr--n17 {
	margin-right: -68rpx;
}

.mr-n18 {
	margin-right: 72rpx;
}

.mr--n18 {
	margin-right: -72rpx;
}

.mr-n19 {
	margin-right: 76rpx;
}

.mr--n19 {
	margin-right: -76rpx;
}

.mr-n20 {
	margin-right: 80rpx;
}

.mr--n20 {
	margin-right: -80rpx;
}

.mr-n21 {
	margin-right: 84rpx;
}

.mr--n21 {
	margin-right: -84rpx;
}

.mr-n22 {
	margin-right: 88rpx;
}

.mr--n22 {
	margin-right: -88rpx;
}

.mr-n23 {
	margin-right: 92rpx;
}

.mr--n23 {
	margin-right: -92rpx;
}

.mr-n24 {
	margin-right: 96rpx;
}

.mr--n24 {
	margin-right: -96rpx;
}

.mr-n25 {
	margin-right: 100rpx;
}

.mr--n25 {
	margin-right: -100rpx;
}

.mb-0 {
	margin-bottom: 0rpx;
}

.mb--0 {
	margin-bottom: -0rpx;
}

.mb-1 {
	margin-bottom: 1rpx;
}

.mb--1 {
	margin-bottom: -1rpx;
}

.mb-2 {
	margin-bottom: 2rpx;
}

.mb--2 {
	margin-bottom: -2rpx;
}

.mb-3 {
	margin-bottom: 3rpx;
}

.mb--3 {
	margin-bottom: -3rpx;
}

.mb-4 {
	margin-bottom: 4rpx;
}

.mb--4 {
	margin-bottom: -4rpx;
}

.mb-5 {
	margin-bottom: 5rpx;
}

.mb--5 {
	margin-bottom: -5rpx;
}

.mb-6 {
	margin-bottom: 6rpx;
}

.mb--6 {
	margin-bottom: -6rpx;
}

.mb-7 {
	margin-bottom: 7rpx;
}

.mb--7 {
	margin-bottom: -7rpx;
}

.mb-8 {
	margin-bottom: 8rpx;
}

.mb--8 {
	margin-bottom: -8rpx;
}

.mb-9 {
	margin-bottom: 9rpx;
}

.mb--9 {
	margin-bottom: -9rpx;
}

.mb-10 {
	margin-bottom: 10rpx;
}

.mb--10 {
	margin-bottom: -10rpx;
}

.mb-11 {
	margin-bottom: 11rpx;
}

.mb--11 {
	margin-bottom: -11rpx;
}

.mb-12 {
	margin-bottom: 12rpx;
}

.mb--12 {
	margin-bottom: -12rpx;
}

.mb-13 {
	margin-bottom: 13rpx;
}

.mb--13 {
	margin-bottom: -13rpx;
}

.mb-14 {
	margin-bottom: 14rpx;
}

.mb--14 {
	margin-bottom: -14rpx;
}

.mb-15 {
	margin-bottom: 15rpx;
}

.mb--15 {
	margin-bottom: -15rpx;
}

.mb-16 {
	margin-bottom: 16rpx;
}

.mb--16 {
	margin-bottom: -16rpx;
}

.mb-17 {
	margin-bottom: 17rpx;
}

.mb--17 {
	margin-bottom: -17rpx;
}

.mb-18 {
	margin-bottom: 18rpx;
}

.mb--18 {
	margin-bottom: -18rpx;
}

.mb-19 {
	margin-bottom: 19rpx;
}

.mb--19 {
	margin-bottom: -19rpx;
}

.mb-20 {
	margin-bottom: 20rpx;
}

.mb--20 {
	margin-bottom: -20rpx;
}

.mb-21 {
	margin-bottom: 21rpx;
}

.mb--21 {
	margin-bottom: -21rpx;
}

.mb-22 {
	margin-bottom: 22rpx;
}

.mb--22 {
	margin-bottom: -22rpx;
}

.mb-23 {
	margin-bottom: 23rpx;
}

.mb--23 {
	margin-bottom: -23rpx;
}

.mb-24 {
	margin-bottom: 24rpx;
}

.mb--24 {
	margin-bottom: -24rpx;
}

.mb-25 {
	margin-bottom: 25rpx;
}

.mb--25 {
	margin-bottom: -25rpx;
}

.mb-26 {
	margin-bottom: 26rpx;
}

.mb--26 {
	margin-bottom: -26rpx;
}

.mb-27 {
	margin-bottom: 27rpx;
}

.mb--27 {
	margin-bottom: -27rpx;
}

.mb-28 {
	margin-bottom: 28rpx;
}

.mb--28 {
	margin-bottom: -28rpx;
}

.mb-29 {
	margin-bottom: 29rpx;
}

.mb--29 {
	margin-bottom: -29rpx;
}

.mb-30 {
	margin-bottom: 30rpx;
}

.mb--30 {
	margin-bottom: -30rpx;
}

.mb-31 {
	margin-bottom: 31rpx;
}

.mb--31 {
	margin-bottom: -31rpx;
}

.mb-32 {
	margin-bottom: 32rpx;
}

.mb--32 {
	margin-bottom: -32rpx;
}

.mb-33 {
	margin-bottom: 33rpx;
}

.mb--33 {
	margin-bottom: -33rpx;
}

.mb-34 {
	margin-bottom: 34rpx;
}

.mb--34 {
	margin-bottom: -34rpx;
}

.mb-35 {
	margin-bottom: 35rpx;
}

.mb--35 {
	margin-bottom: -35rpx;
}

.mb-36 {
	margin-bottom: 36rpx;
}

.mb--36 {
	margin-bottom: -36rpx;
}

.mb-37 {
	margin-bottom: 37rpx;
}

.mb--37 {
	margin-bottom: -37rpx;
}

.mb-38 {
	margin-bottom: 38rpx;
}

.mb--38 {
	margin-bottom: -38rpx;
}

.mb-39 {
	margin-bottom: 39rpx;
}

.mb--39 {
	margin-bottom: -39rpx;
}

.mb-40 {
	margin-bottom: 40rpx;
}

.mb--40 {
	margin-bottom: -40rpx;
}

.mb-41 {
	margin-bottom: 41rpx;
}

.mb--41 {
	margin-bottom: -41rpx;
}

.mb-42 {
	margin-bottom: 42rpx;
}

.mb--42 {
	margin-bottom: -42rpx;
}

.mb-43 {
	margin-bottom: 43rpx;
}

.mb--43 {
	margin-bottom: -43rpx;
}

.mb-44 {
	margin-bottom: 44rpx;
}

.mb--44 {
	margin-bottom: -44rpx;
}

.mb-45 {
	margin-bottom: 45rpx;
}

.mb--45 {
	margin-bottom: -45rpx;
}

.mb-46 {
	margin-bottom: 46rpx;
}

.mb--46 {
	margin-bottom: -46rpx;
}

.mb-47 {
	margin-bottom: 47rpx;
}

.mb--47 {
	margin-bottom: -47rpx;
}

.mb-48 {
	margin-bottom: 48rpx;
}

.mb--48 {
	margin-bottom: -48rpx;
}

.mb-49 {
	margin-bottom: 49rpx;
}

.mb--49 {
	margin-bottom: -49rpx;
}

.mb-50 {
	margin-bottom: 50rpx;
}

.mb--50 {
	margin-bottom: -50rpx;
}

.mb-n1 {
	margin-bottom: 4rpx;
}

.mb--n1 {
	margin-bottom: -4rpx;
}

.mb-n2 {
	margin-bottom: 8rpx;
}

.mb--n2 {
	margin-bottom: -8rpx;
}

.mb-n3 {
	margin-bottom: 12rpx;
}

.mb--n3 {
	margin-bottom: -12rpx;
}

.mb-n4 {
	margin-bottom: 16rpx;
}

.mb--n4 {
	margin-bottom: -16rpx;
}

.mb-n5 {
	margin-bottom: 20rpx;
}

.mb--n5 {
	margin-bottom: -20rpx;
}

.mb-n6 {
	margin-bottom: 24rpx;
}

.mb--n6 {
	margin-bottom: -24rpx;
}

.mb-n7 {
	margin-bottom: 28rpx;
}

.mb--n7 {
	margin-bottom: -28rpx;
}

.mb-n8 {
	margin-bottom: 32rpx;
}

.mb--n8 {
	margin-bottom: -32rpx;
}

.mb-n9 {
	margin-bottom: 36rpx;
}

.mb--n9 {
	margin-bottom: -36rpx;
}

.mb-n10 {
	margin-bottom: 40rpx;
}

.mb--n10 {
	margin-bottom: -40rpx;
}

.mb-n11 {
	margin-bottom: 44rpx;
}

.mb--n11 {
	margin-bottom: -44rpx;
}

.mb-n12 {
	margin-bottom: 48rpx;
}

.mb--n12 {
	margin-bottom: -48rpx;
}

.mb-n13 {
	margin-bottom: 52rpx;
}

.mb--n13 {
	margin-bottom: -52rpx;
}

.mb-n14 {
	margin-bottom: 56rpx;
}

.mb--n14 {
	margin-bottom: -56rpx;
}

.mb-n15 {
	margin-bottom: 60rpx;
}

.mb--n15 {
	margin-bottom: -60rpx;
}

.mb-n16 {
	margin-bottom: 64rpx;
}

.mb--n16 {
	margin-bottom: -64rpx;
}

.mb-n17 {
	margin-bottom: 68rpx;
}

.mb--n17 {
	margin-bottom: -68rpx;
}

.mb-n18 {
	margin-bottom: 72rpx;
}

.mb--n18 {
	margin-bottom: -72rpx;
}

.mb-n19 {
	margin-bottom: 76rpx;
}

.mb--n19 {
	margin-bottom: -76rpx;
}

.mb-n20 {
	margin-bottom: 80rpx;
}

.mb--n20 {
	margin-bottom: -80rpx;
}

.mb-n21 {
	margin-bottom: 84rpx;
}

.mb--n21 {
	margin-bottom: -84rpx;
}

.mb-n22 {
	margin-bottom: 88rpx;
}

.mb--n22 {
	margin-bottom: -88rpx;
}

.mb-n23 {
	margin-bottom: 92rpx;
}

.mb--n23 {
	margin-bottom: -92rpx;
}

.mb-n24 {
	margin-bottom: 96rpx;
}

.mb--n24 {
	margin-bottom: -96rpx;
}

.mb-n25 {
	margin-bottom: 100rpx;
}

.mb--n25 {
	margin-bottom: -100rpx;
}

.ml-0 {
	margin-left: 0rpx;
}

.ml--0 {
	margin-left: -0rpx;
}

.ml-1 {
	margin-left: 1rpx;
}

.ml--1 {
	margin-left: -1rpx;
}

.ml-2 {
	margin-left: 2rpx;
}

.ml--2 {
	margin-left: -2rpx;
}

.ml-3 {
	margin-left: 3rpx;
}

.ml--3 {
	margin-left: -3rpx;
}

.ml-4 {
	margin-left: 4rpx;
}

.ml--4 {
	margin-left: -4rpx;
}

.ml-5 {
	margin-left: 5rpx;
}

.ml--5 {
	margin-left: -5rpx;
}

.ml-6 {
	margin-left: 6rpx;
}

.ml--6 {
	margin-left: -6rpx;
}

.ml-7 {
	margin-left: 7rpx;
}

.ml--7 {
	margin-left: -7rpx;
}

.ml-8 {
	margin-left: 8rpx;
}

.ml--8 {
	margin-left: -8rpx;
}

.ml-9 {
	margin-left: 9rpx;
}

.ml--9 {
	margin-left: -9rpx;
}

.ml-10 {
	margin-left: 10rpx;
}

.ml--10 {
	margin-left: -10rpx;
}

.ml-11 {
	margin-left: 11rpx;
}

.ml--11 {
	margin-left: -11rpx;
}

.ml-12 {
	margin-left: 12rpx;
}

.ml--12 {
	margin-left: -12rpx;
}

.ml-13 {
	margin-left: 13rpx;
}

.ml--13 {
	margin-left: -13rpx;
}

.ml-14 {
	margin-left: 14rpx;
}

.ml--14 {
	margin-left: -14rpx;
}

.ml-15 {
	margin-left: 15rpx;
}

.ml--15 {
	margin-left: -15rpx;
}

.ml-16 {
	margin-left: 16rpx;
}

.ml--16 {
	margin-left: -16rpx;
}

.ml-17 {
	margin-left: 17rpx;
}

.ml--17 {
	margin-left: -17rpx;
}

.ml-18 {
	margin-left: 18rpx;
}

.ml--18 {
	margin-left: -18rpx;
}

.ml-19 {
	margin-left: 19rpx;
}

.ml--19 {
	margin-left: -19rpx;
}

.ml-20 {
	margin-left: 20rpx;
}

.ml--20 {
	margin-left: -20rpx;
}

.ml-21 {
	margin-left: 21rpx;
}

.ml--21 {
	margin-left: -21rpx;
}

.ml-22 {
	margin-left: 22rpx;
}

.ml--22 {
	margin-left: -22rpx;
}

.ml-23 {
	margin-left: 23rpx;
}

.ml--23 {
	margin-left: -23rpx;
}

.ml-24 {
	margin-left: 24rpx;
}

.ml--24 {
	margin-left: -24rpx;
}

.ml-25 {
	margin-left: 25rpx;
}

.ml--25 {
	margin-left: -25rpx;
}

.ml-26 {
	margin-left: 26rpx;
}

.ml--26 {
	margin-left: -26rpx;
}

.ml-27 {
	margin-left: 27rpx;
}

.ml--27 {
	margin-left: -27rpx;
}

.ml-28 {
	margin-left: 28rpx;
}

.ml--28 {
	margin-left: -28rpx;
}

.ml-29 {
	margin-left: 29rpx;
}

.ml--29 {
	margin-left: -29rpx;
}

.ml-30 {
	margin-left: 30rpx;
}

.ml--30 {
	margin-left: -30rpx;
}

.ml-31 {
	margin-left: 31rpx;
}

.ml--31 {
	margin-left: -31rpx;
}

.ml-32 {
	margin-left: 32rpx;
}

.ml--32 {
	margin-left: -32rpx;
}

.ml-33 {
	margin-left: 33rpx;
}

.ml--33 {
	margin-left: -33rpx;
}

.ml-34 {
	margin-left: 34rpx;
}

.ml--34 {
	margin-left: -34rpx;
}

.ml-35 {
	margin-left: 35rpx;
}

.ml--35 {
	margin-left: -35rpx;
}

.ml-36 {
	margin-left: 36rpx;
}

.ml--36 {
	margin-left: -36rpx;
}

.ml-37 {
	margin-left: 37rpx;
}

.ml--37 {
	margin-left: -37rpx;
}

.ml-38 {
	margin-left: 38rpx;
}

.ml--38 {
	margin-left: -38rpx;
}

.ml-39 {
	margin-left: 39rpx;
}

.ml--39 {
	margin-left: -39rpx;
}

.ml-40 {
	margin-left: 40rpx;
}

.ml--40 {
	margin-left: -40rpx;
}

.ml-41 {
	margin-left: 41rpx;
}

.ml--41 {
	margin-left: -41rpx;
}

.ml-42 {
	margin-left: 42rpx;
}

.ml--42 {
	margin-left: -42rpx;
}

.ml-43 {
	margin-left: 43rpx;
}

.ml--43 {
	margin-left: -43rpx;
}

.ml-44 {
	margin-left: 44rpx;
}

.ml--44 {
	margin-left: -44rpx;
}

.ml-45 {
	margin-left: 45rpx;
}

.ml--45 {
	margin-left: -45rpx;
}

.ml-46 {
	margin-left: 46rpx;
}

.ml--46 {
	margin-left: -46rpx;
}

.ml-47 {
	margin-left: 47rpx;
}

.ml--47 {
	margin-left: -47rpx;
}

.ml-48 {
	margin-left: 48rpx;
}

.ml--48 {
	margin-left: -48rpx;
}

.ml-49 {
	margin-left: 49rpx;
}

.ml--49 {
	margin-left: -49rpx;
}

.ml-50 {
	margin-left: 50rpx;
}

.ml--50 {
	margin-left: -50rpx;
}

.ml-n1 {
	margin-left: 4rpx;
}

.ml--n1 {
	margin-left: -4rpx;
}

.ml-n2 {
	margin-left: 8rpx;
}

.ml--n2 {
	margin-left: -8rpx;
}

.ml-n3 {
	margin-left: 12rpx;
}

.ml--n3 {
	margin-left: -12rpx;
}

.ml-n4 {
	margin-left: 16rpx;
}

.ml--n4 {
	margin-left: -16rpx;
}

.ml-n5 {
	margin-left: 20rpx;
}

.ml--n5 {
	margin-left: -20rpx;
}

.ml-n6 {
	margin-left: 24rpx;
}

.ml--n6 {
	margin-left: -24rpx;
}

.ml-n7 {
	margin-left: 28rpx;
}

.ml--n7 {
	margin-left: -28rpx;
}

.ml-n8 {
	margin-left: 32rpx;
}

.ml--n8 {
	margin-left: -32rpx;
}

.ml-n9 {
	margin-left: 36rpx;
}

.ml--n9 {
	margin-left: -36rpx;
}

.ml-n10 {
	margin-left: 40rpx;
}

.ml--n10 {
	margin-left: -40rpx;
}

.ml-n11 {
	margin-left: 44rpx;
}

.ml--n11 {
	margin-left: -44rpx;
}

.ml-n12 {
	margin-left: 48rpx;
}

.ml--n12 {
	margin-left: -48rpx;
}

.ml-n13 {
	margin-left: 52rpx;
}

.ml--n13 {
	margin-left: -52rpx;
}

.ml-n14 {
	margin-left: 56rpx;
}

.ml--n14 {
	margin-left: -56rpx;
}

.ml-n15 {
	margin-left: 60rpx;
}

.ml--n15 {
	margin-left: -60rpx;
}

.ml-n16 {
	margin-left: 64rpx;
}

.ml--n16 {
	margin-left: -64rpx;
}

.ml-n17 {
	margin-left: 68rpx;
}

.ml--n17 {
	margin-left: -68rpx;
}

.ml-n18 {
	margin-left: 72rpx;
}

.ml--n18 {
	margin-left: -72rpx;
}

.ml-n19 {
	margin-left: 76rpx;
}

.ml--n19 {
	margin-left: -76rpx;
}

.ml-n20 {
	margin-left: 80rpx;
}

.ml--n20 {
	margin-left: -80rpx;
}

.ml-n21 {
	margin-left: 84rpx;
}

.ml--n21 {
	margin-left: -84rpx;
}

.ml-n22 {
	margin-left: 88rpx;
}

.ml--n22 {
	margin-left: -88rpx;
}

.ml-n23 {
	margin-left: 92rpx;
}

.ml--n23 {
	margin-left: -92rpx;
}

.ml-n24 {
	margin-left: 96rpx;
}

.ml--n24 {
	margin-left: -96rpx;
}

.ml-n25 {
	margin-left: 100rpx;
}

.ml--n25 {
	margin-left: -100rpx;
}

.mx-0 {
	margin-left: 0rpx;
	margin-right: 0rpx;
}

.mx-1 {
	margin-left: 1rpx;
	margin-right: 1rpx;
}

.mx-2 {
	margin-left: 2rpx;
	margin-right: 2rpx;
}

.mx-3 {
	margin-left: 3rpx;
	margin-right: 3rpx;
}

.mx-4 {
	margin-left: 4rpx;
	margin-right: 4rpx;
}

.mx-5 {
	margin-left: 5rpx;
	margin-right: 5rpx;
}

.mx-6 {
	margin-left: 6rpx;
	margin-right: 6rpx;
}

.mx-7 {
	margin-left: 7rpx;
	margin-right: 7rpx;
}

.mx-8 {
	margin-left: 8rpx;
	margin-right: 8rpx;
}

.mx-9 {
	margin-left: 9rpx;
	margin-right: 9rpx;
}

.mx-10 {
	margin-left: 10rpx;
	margin-right: 10rpx;
}

.mx-11 {
	margin-left: 11rpx;
	margin-right: 11rpx;
}

.mx-12 {
	margin-left: 12rpx;
	margin-right: 12rpx;
}

.mx-13 {
	margin-left: 13rpx;
	margin-right: 13rpx;
}

.mx-14 {
	margin-left: 14rpx;
	margin-right: 14rpx;
}

.mx-15 {
	margin-left: 15rpx;
	margin-right: 15rpx;
}

.mx-16 {
	margin-left: 16rpx;
	margin-right: 16rpx;
}

.mx-17 {
	margin-left: 17rpx;
	margin-right: 17rpx;
}

.mx-18 {
	margin-left: 18rpx;
	margin-right: 18rpx;
}

.mx-19 {
	margin-left: 19rpx;
	margin-right: 19rpx;
}

.mx-20 {
	margin-left: 20rpx;
	margin-right: 20rpx;
}

.mx-21 {
	margin-left: 21rpx;
	margin-right: 21rpx;
}

.mx-22 {
	margin-left: 22rpx;
	margin-right: 22rpx;
}

.mx-23 {
	margin-left: 23rpx;
	margin-right: 23rpx;
}

.mx-24 {
	margin-left: 24rpx;
	margin-right: 24rpx;
}

.mx-25 {
	margin-left: 25rpx;
	margin-right: 25rpx;
}

.mx-26 {
	margin-left: 26rpx;
	margin-right: 26rpx;
}

.mx-27 {
	margin-left: 27rpx;
	margin-right: 27rpx;
}

.mx-28 {
	margin-left: 28rpx;
	margin-right: 28rpx;
}

.mx-29 {
	margin-left: 29rpx;
	margin-right: 29rpx;
}

.mx-30 {
	margin-left: 30rpx;
	margin-right: 30rpx;
}

.mx-31 {
	margin-left: 31rpx;
	margin-right: 31rpx;
}

.mx-32 {
	margin-left: 32rpx;
	margin-right: 32rpx;
}

.mx-33 {
	margin-left: 33rpx;
	margin-right: 33rpx;
}

.mx-34 {
	margin-left: 34rpx;
	margin-right: 34rpx;
}

.mx-35 {
	margin-left: 35rpx;
	margin-right: 35rpx;
}

.mx-36 {
	margin-left: 36rpx;
	margin-right: 36rpx;
}

.mx-37 {
	margin-left: 37rpx;
	margin-right: 37rpx;
}

.mx-38 {
	margin-left: 38rpx;
	margin-right: 38rpx;
}

.mx-39 {
	margin-left: 39rpx;
	margin-right: 39rpx;
}

.mx-40 {
	margin-left: 40rpx;
	margin-right: 40rpx;
}

.mx-41 {
	margin-left: 41rpx;
	margin-right: 41rpx;
}

.mx-42 {
	margin-left: 42rpx;
	margin-right: 42rpx;
}

.mx-43 {
	margin-left: 43rpx;
	margin-right: 43rpx;
}

.mx-44 {
	margin-left: 44rpx;
	margin-right: 44rpx;
}

.mx-45 {
	margin-left: 45rpx;
	margin-right: 45rpx;
}

.mx-46 {
	margin-left: 46rpx;
	margin-right: 46rpx;
}

.mx-47 {
	margin-left: 47rpx;
	margin-right: 47rpx;
}

.mx-48 {
	margin-left: 48rpx;
	margin-right: 48rpx;
}

.mx-49 {
	margin-left: 49rpx;
	margin-right: 49rpx;
}

.mx-50 {
	margin-left: 50rpx;
	margin-right: 50rpx;
}

.mx-n1 {
	margin-left: 4rpx;
	margin-right: 4rpx;
}

.mx-n2 {
	margin-left: 8rpx;
	margin-right: 8rpx;
}

.mx-n3 {
	margin-left: 12rpx;
	margin-right: 12rpx;
}

.mx-n4 {
	margin-left: 16rpx;
	margin-right: 16rpx;
}

.mx-n5 {
	margin-left: 20rpx;
	margin-right: 20rpx;
}

.mx-n6 {
	margin-left: 24rpx;
	margin-right: 24rpx;
}

.mx-n7 {
	margin-left: 28rpx;
	margin-right: 28rpx;
}

.mx-n8 {
	margin-left: 32rpx;
	margin-right: 32rpx;
}

.mx-n9 {
	margin-left: 36rpx;
	margin-right: 36rpx;
}

.mx-n10 {
	margin-left: 40rpx;
	margin-right: 40rpx;
}

.mx-n11 {
	margin-left: 44rpx;
	margin-right: 44rpx;
}

.mx-n12 {
	margin-left: 48rpx;
	margin-right: 48rpx;
}

.mx-n13 {
	margin-left: 52rpx;
	margin-right: 52rpx;
}

.mx-n14 {
	margin-left: 56rpx;
	margin-right: 56rpx;
}

.mx-n15 {
	margin-left: 60rpx;
	margin-right: 60rpx;
}

.mx-n16 {
	margin-left: 64rpx;
	margin-right: 64rpx;
}

.mx-n17 {
	margin-left: 68rpx;
	margin-right: 68rpx;
}

.mx-n18 {
	margin-left: 72rpx;
	margin-right: 72rpx;
}

.mx-n19 {
	margin-left: 76rpx;
	margin-right: 76rpx;
}

.mx-n20 {
	margin-left: 80rpx;
	margin-right: 80rpx;
}

.mx-n21 {
	margin-left: 84rpx;
	margin-right: 84rpx;
}

.mx-n22 {
	margin-left: 88rpx;
	margin-right: 88rpx;
}

.mx-n23 {
	margin-left: 92rpx;
	margin-right: 92rpx;
}

.mx-n24 {
	margin-left: 96rpx;
	margin-right: 96rpx;
}

.mx-n25 {
	margin-left: 100rpx;
	margin-right: 100rpx;
}

.my-0 {
	margin-top: 0rpx;
	margin-bottom: 0rpx;
}

.my-1 {
	margin-top: 1rpx;
	margin-bottom: 1rpx;
}

.my-2 {
	margin-top: 2rpx;
	margin-bottom: 2rpx;
}

.my-3 {
	margin-top: 3rpx;
	margin-bottom: 3rpx;
}

.my-4 {
	margin-top: 4rpx;
	margin-bottom: 4rpx;
}

.my-5 {
	margin-top: 5rpx;
	margin-bottom: 5rpx;
}

.my-6 {
	margin-top: 6rpx;
	margin-bottom: 6rpx;
}

.my-7 {
	margin-top: 7rpx;
	margin-bottom: 7rpx;
}

.my-8 {
	margin-top: 8rpx;
	margin-bottom: 8rpx;
}

.my-9 {
	margin-top: 9rpx;
	margin-bottom: 9rpx;
}

.my-10 {
	margin-top: 10rpx;
	margin-bottom: 10rpx;
}

.my-11 {
	margin-top: 11rpx;
	margin-bottom: 11rpx;
}

.my-12 {
	margin-top: 12rpx;
	margin-bottom: 12rpx;
}

.my-13 {
	margin-top: 13rpx;
	margin-bottom: 13rpx;
}

.my-14 {
	margin-top: 14rpx;
	margin-bottom: 14rpx;
}

.my-15 {
	margin-top: 15rpx;
	margin-bottom: 15rpx;
}

.my-16 {
	margin-top: 16rpx;
	margin-bottom: 16rpx;
}

.my-17 {
	margin-top: 17rpx;
	margin-bottom: 17rpx;
}

.my-18 {
	margin-top: 18rpx;
	margin-bottom: 18rpx;
}

.my-19 {
	margin-top: 19rpx;
	margin-bottom: 19rpx;
}

.my-20 {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}

.my-21 {
	margin-top: 21rpx;
	margin-bottom: 21rpx;
}

.my-22 {
	margin-top: 22rpx;
	margin-bottom: 22rpx;
}

.my-23 {
	margin-top: 23rpx;
	margin-bottom: 23rpx;
}

.my-24 {
	margin-top: 24rpx;
	margin-bottom: 24rpx;
}

.my-25 {
	margin-top: 25rpx;
	margin-bottom: 25rpx;
}

.my-26 {
	margin-top: 26rpx;
	margin-bottom: 26rpx;
}

.my-27 {
	margin-top: 27rpx;
	margin-bottom: 27rpx;
}

.my-28 {
	margin-top: 28rpx;
	margin-bottom: 28rpx;
}

.my-29 {
	margin-top: 29rpx;
	margin-bottom: 29rpx;
}

.my-30 {
	margin-top: 30rpx;
	margin-bottom: 30rpx;
}

.my-31 {
	margin-top: 31rpx;
	margin-bottom: 31rpx;
}

.my-32 {
	margin-top: 32rpx;
	margin-bottom: 32rpx;
}

.my-33 {
	margin-top: 33rpx;
	margin-bottom: 33rpx;
}

.my-34 {
	margin-top: 34rpx;
	margin-bottom: 34rpx;
}

.my-35 {
	margin-top: 35rpx;
	margin-bottom: 35rpx;
}

.my-36 {
	margin-top: 36rpx;
	margin-bottom: 36rpx;
}

.my-37 {
	margin-top: 37rpx;
	margin-bottom: 37rpx;
}

.my-38 {
	margin-top: 38rpx;
	margin-bottom: 38rpx;
}

.my-39 {
	margin-top: 39rpx;
	margin-bottom: 39rpx;
}

.my-40 {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
}

.my-41 {
	margin-top: 41rpx;
	margin-bottom: 41rpx;
}

.my-42 {
	margin-top: 42rpx;
	margin-bottom: 42rpx;
}

.my-43 {
	margin-top: 43rpx;
	margin-bottom: 43rpx;
}

.my-44 {
	margin-top: 44rpx;
	margin-bottom: 44rpx;
}

.my-45 {
	margin-top: 45rpx;
	margin-bottom: 45rpx;
}

.my-46 {
	margin-top: 46rpx;
	margin-bottom: 46rpx;
}

.my-47 {
	margin-top: 47rpx;
	margin-bottom: 47rpx;
}

.my-48 {
	margin-top: 48rpx;
	margin-bottom: 48rpx;
}

.my-49 {
	margin-top: 49rpx;
	margin-bottom: 49rpx;
}

.my-50 {
	margin-top: 50rpx;
	margin-bottom: 50rpx;
}

.my-n1 {
	margin-top: 4rpx;
	margin-bottom: 4rpx;
}

.my-n2 {
	margin-top: 8rpx;
	margin-bottom: 8rpx;
}

.my-n3 {
	margin-top: 12rpx;
	margin-bottom: 12rpx;
}

.my-n4 {
	margin-top: 16rpx;
	margin-bottom: 16rpx;
}

.my-n5 {
	margin-top: 20rpx;
	margin-bottom: 20rpx;
}

.my-n6 {
	margin-top: 24rpx;
	margin-bottom: 24rpx;
}

.my-n7 {
	margin-top: 28rpx;
	margin-bottom: 28rpx;
}

.my-n8 {
	margin-top: 32rpx;
	margin-bottom: 32rpx;
}

.my-n9 {
	margin-top: 36rpx;
	margin-bottom: 36rpx;
}

.my-n10 {
	margin-top: 40rpx;
	margin-bottom: 40rpx;
}

.my-n11 {
	margin-top: 44rpx;
	margin-bottom: 44rpx;
}

.my-n12 {
	margin-top: 48rpx;
	margin-bottom: 48rpx;
}

.my-n13 {
	margin-top: 52rpx;
	margin-bottom: 52rpx;
}

.my-n14 {
	margin-top: 56rpx;
	margin-bottom: 56rpx;
}

.my-n15 {
	margin-top: 60rpx;
	margin-bottom: 60rpx;
}

.my-n16 {
	margin-top: 64rpx;
	margin-bottom: 64rpx;
}

.my-n17 {
	margin-top: 68rpx;
	margin-bottom: 68rpx;
}

.my-n18 {
	margin-top: 72rpx;
	margin-bottom: 72rpx;
}

.my-n19 {
	margin-top: 76rpx;
	margin-bottom: 76rpx;
}

.my-n20 {
	margin-top: 80rpx;
	margin-bottom: 80rpx;
}

.my-n21 {
	margin-top: 84rpx;
	margin-bottom: 84rpx;
}

.my-n22 {
	margin-top: 88rpx;
	margin-bottom: 88rpx;
}

.my-n23 {
	margin-top: 92rpx;
	margin-bottom: 92rpx;
}

.my-n24 {
	margin-top: 96rpx;
	margin-bottom: 96rpx;
}

.my-n25 {
	margin-top: 100rpx;
	margin-bottom: 100rpx;
}

.t-0 {
	top: 0rpx;
}

.t--0 {
	top: -0rpx;
}

.t-1 {
	top: 1rpx;
}

.t--1 {
	top: -1rpx;
}

.t-2 {
	top: 2rpx;
}

.t--2 {
	top: -2rpx;
}

.t-3 {
	top: 3rpx;
}

.t--3 {
	top: -3rpx;
}

.t-4 {
	top: 4rpx;
}

.t--4 {
	top: -4rpx;
}

.t-5 {
	top: 5rpx;
}

.t--5 {
	top: -5rpx;
}

.t-6 {
	top: 6rpx;
}

.t--6 {
	top: -6rpx;
}

.t-7 {
	top: 7rpx;
}

.t--7 {
	top: -7rpx;
}

.t-8 {
	top: 8rpx;
}

.t--8 {
	top: -8rpx;
}

.t-9 {
	top: 9rpx;
}

.t--9 {
	top: -9rpx;
}

.t-10 {
	top: 10rpx;
}

.t--10 {
	top: -10rpx;
}

.t-11 {
	top: 11rpx;
}

.t--11 {
	top: -11rpx;
}

.t-12 {
	top: 12rpx;
}

.t--12 {
	top: -12rpx;
}

.t-13 {
	top: 13rpx;
}

.t--13 {
	top: -13rpx;
}

.t-14 {
	top: 14rpx;
}

.t--14 {
	top: -14rpx;
}

.t-15 {
	top: 15rpx;
}

.t--15 {
	top: -15rpx;
}

.t-16 {
	top: 16rpx;
}

.t--16 {
	top: -16rpx;
}

.t-17 {
	top: 17rpx;
}

.t--17 {
	top: -17rpx;
}

.t-18 {
	top: 18rpx;
}

.t--18 {
	top: -18rpx;
}

.t-19 {
	top: 19rpx;
}

.t--19 {
	top: -19rpx;
}

.t-20 {
	top: 20rpx;
}

.t--20 {
	top: -20rpx;
}

.t-21 {
	top: 21rpx;
}

.t--21 {
	top: -21rpx;
}

.t-22 {
	top: 22rpx;
}

.t--22 {
	top: -22rpx;
}

.t-23 {
	top: 23rpx;
}

.t--23 {
	top: -23rpx;
}

.t-24 {
	top: 24rpx;
}

.t--24 {
	top: -24rpx;
}

.t-25 {
	top: 25rpx;
}

.t--25 {
	top: -25rpx;
}

.t-26 {
	top: 26rpx;
}

.t--26 {
	top: -26rpx;
}

.t-27 {
	top: 27rpx;
}

.t--27 {
	top: -27rpx;
}

.t-28 {
	top: 28rpx;
}

.t--28 {
	top: -28rpx;
}

.t-29 {
	top: 29rpx;
}

.t--29 {
	top: -29rpx;
}

.t-30 {
	top: 30rpx;
}

.t--30 {
	top: -30rpx;
}

.t-31 {
	top: 31rpx;
}

.t--31 {
	top: -31rpx;
}

.t-32 {
	top: 32rpx;
}

.t--32 {
	top: -32rpx;
}

.t-33 {
	top: 33rpx;
}

.t--33 {
	top: -33rpx;
}

.t-34 {
	top: 34rpx;
}

.t--34 {
	top: -34rpx;
}

.t-35 {
	top: 35rpx;
}

.t--35 {
	top: -35rpx;
}

.t-36 {
	top: 36rpx;
}

.t--36 {
	top: -36rpx;
}

.t-37 {
	top: 37rpx;
}

.t--37 {
	top: -37rpx;
}

.t-38 {
	top: 38rpx;
}

.t--38 {
	top: -38rpx;
}

.t-39 {
	top: 39rpx;
}

.t--39 {
	top: -39rpx;
}

.t-40 {
	top: 40rpx;
}

.t--40 {
	top: -40rpx;
}

.t-41 {
	top: 41rpx;
}

.t--41 {
	top: -41rpx;
}

.t-42 {
	top: 42rpx;
}

.t--42 {
	top: -42rpx;
}

.t-43 {
	top: 43rpx;
}

.t--43 {
	top: -43rpx;
}

.t-44 {
	top: 44rpx;
}

.t--44 {
	top: -44rpx;
}

.t-45 {
	top: 45rpx;
}

.t--45 {
	top: -45rpx;
}

.t-46 {
	top: 46rpx;
}

.t--46 {
	top: -46rpx;
}

.t-47 {
	top: 47rpx;
}

.t--47 {
	top: -47rpx;
}

.t-48 {
	top: 48rpx;
}

.t--48 {
	top: -48rpx;
}

.t-49 {
	top: 49rpx;
}

.t--49 {
	top: -49rpx;
}

.t-50 {
	top: 50rpx;
}

.t--50 {
	top: -50rpx;
}

.t-n1 {
	top: 4rpx;
}

.t--n1 {
	top: -4rpx;
}

.t-n2 {
	top: 8rpx;
}

.t--n2 {
	top: -8rpx;
}

.t-n3 {
	top: 12rpx;
}

.t--n3 {
	top: -12rpx;
}

.t-n4 {
	top: 16rpx;
}

.t--n4 {
	top: -16rpx;
}

.t-n5 {
	top: 20rpx;
}

.t--n5 {
	top: -20rpx;
}

.t-n6 {
	top: 24rpx;
}

.t--n6 {
	top: -24rpx;
}

.t-n7 {
	top: 28rpx;
}

.t--n7 {
	top: -28rpx;
}

.t-n8 {
	top: 32rpx;
}

.t--n8 {
	top: -32rpx;
}

.t-n9 {
	top: 36rpx;
}

.t--n9 {
	top: -36rpx;
}

.t-n10 {
	top: 40rpx;
}

.t--n10 {
	top: -40rpx;
}

.t-n11 {
	top: 44rpx;
}

.t--n11 {
	top: -44rpx;
}

.t-n12 {
	top: 48rpx;
}

.t--n12 {
	top: -48rpx;
}

.t-n13 {
	top: 52rpx;
}

.t--n13 {
	top: -52rpx;
}

.t-n14 {
	top: 56rpx;
}

.t--n14 {
	top: -56rpx;
}

.t-n15 {
	top: 60rpx;
}

.t--n15 {
	top: -60rpx;
}

.t-n16 {
	top: 64rpx;
}

.t--n16 {
	top: -64rpx;
}

.t-n17 {
	top: 68rpx;
}

.t--n17 {
	top: -68rpx;
}

.t-n18 {
	top: 72rpx;
}

.t--n18 {
	top: -72rpx;
}

.t-n19 {
	top: 76rpx;
}

.t--n19 {
	top: -76rpx;
}

.t-n20 {
	top: 80rpx;
}

.t--n20 {
	top: -80rpx;
}

.t-n21 {
	top: 84rpx;
}

.t--n21 {
	top: -84rpx;
}

.t-n22 {
	top: 88rpx;
}

.t--n22 {
	top: -88rpx;
}

.t-n23 {
	top: 92rpx;
}

.t--n23 {
	top: -92rpx;
}

.t-n24 {
	top: 96rpx;
}

.t--n24 {
	top: -96rpx;
}

.t-n25 {
	top: 100rpx;
}

.t--n25 {
	top: -100rpx;
}

.r-0 {
	right: 0rpx;
}

.r--0 {
	right: -0rpx;
}

.r-1 {
	right: 1rpx;
}

.r--1 {
	right: -1rpx;
}

.r-2 {
	right: 2rpx;
}

.r--2 {
	right: -2rpx;
}

.r-3 {
	right: 3rpx;
}

.r--3 {
	right: -3rpx;
}

.r-4 {
	right: 4rpx;
}

.r--4 {
	right: -4rpx;
}

.r-5 {
	right: 5rpx;
}

.r--5 {
	right: -5rpx;
}

.r-6 {
	right: 6rpx;
}

.r--6 {
	right: -6rpx;
}

.r-7 {
	right: 7rpx;
}

.r--7 {
	right: -7rpx;
}

.r-8 {
	right: 8rpx;
}

.r--8 {
	right: -8rpx;
}

.r-9 {
	right: 9rpx;
}

.r--9 {
	right: -9rpx;
}

.r-10 {
	right: 10rpx;
}

.r--10 {
	right: -10rpx;
}

.r-11 {
	right: 11rpx;
}

.r--11 {
	right: -11rpx;
}

.r-12 {
	right: 12rpx;
}

.r--12 {
	right: -12rpx;
}

.r-13 {
	right: 13rpx;
}

.r--13 {
	right: -13rpx;
}

.r-14 {
	right: 14rpx;
}

.r--14 {
	right: -14rpx;
}

.r-15 {
	right: 15rpx;
}

.r--15 {
	right: -15rpx;
}

.r-16 {
	right: 16rpx;
}

.r--16 {
	right: -16rpx;
}

.r-17 {
	right: 17rpx;
}

.r--17 {
	right: -17rpx;
}

.r-18 {
	right: 18rpx;
}

.r--18 {
	right: -18rpx;
}

.r-19 {
	right: 19rpx;
}

.r--19 {
	right: -19rpx;
}

.r-20 {
	right: 20rpx;
}

.r--20 {
	right: -20rpx;
}

.r-21 {
	right: 21rpx;
}

.r--21 {
	right: -21rpx;
}

.r-22 {
	right: 22rpx;
}

.r--22 {
	right: -22rpx;
}

.r-23 {
	right: 23rpx;
}

.r--23 {
	right: -23rpx;
}

.r-24 {
	right: 24rpx;
}

.r--24 {
	right: -24rpx;
}

.r-25 {
	right: 25rpx;
}

.r--25 {
	right: -25rpx;
}

.r-26 {
	right: 26rpx;
}

.r--26 {
	right: -26rpx;
}

.r-27 {
	right: 27rpx;
}

.r--27 {
	right: -27rpx;
}

.r-28 {
	right: 28rpx;
}

.r--28 {
	right: -28rpx;
}

.r-29 {
	right: 29rpx;
}

.r--29 {
	right: -29rpx;
}

.r-30 {
	right: 30rpx;
}

.r--30 {
	right: -30rpx;
}

.r-31 {
	right: 31rpx;
}

.r--31 {
	right: -31rpx;
}

.r-32 {
	right: 32rpx;
}

.r--32 {
	right: -32rpx;
}

.r-33 {
	right: 33rpx;
}

.r--33 {
	right: -33rpx;
}

.r-34 {
	right: 34rpx;
}

.r--34 {
	right: -34rpx;
}

.r-35 {
	right: 35rpx;
}

.r--35 {
	right: -35rpx;
}

.r-36 {
	right: 36rpx;
}

.r--36 {
	right: -36rpx;
}

.r-37 {
	right: 37rpx;
}

.r--37 {
	right: -37rpx;
}

.r-38 {
	right: 38rpx;
}

.r--38 {
	right: -38rpx;
}

.r-39 {
	right: 39rpx;
}

.r--39 {
	right: -39rpx;
}

.r-40 {
	right: 40rpx;
}

.r--40 {
	right: -40rpx;
}

.r-41 {
	right: 41rpx;
}

.r--41 {
	right: -41rpx;
}

.r-42 {
	right: 42rpx;
}

.r--42 {
	right: -42rpx;
}

.r-43 {
	right: 43rpx;
}

.r--43 {
	right: -43rpx;
}

.r-44 {
	right: 44rpx;
}

.r--44 {
	right: -44rpx;
}

.r-45 {
	right: 45rpx;
}

.r--45 {
	right: -45rpx;
}

.r-46 {
	right: 46rpx;
}

.r--46 {
	right: -46rpx;
}

.r-47 {
	right: 47rpx;
}

.r--47 {
	right: -47rpx;
}

.r-48 {
	right: 48rpx;
}

.r--48 {
	right: -48rpx;
}

.r-49 {
	right: 49rpx;
}

.r--49 {
	right: -49rpx;
}

.r-50 {
	right: 50rpx;
}

.r--50 {
	right: -50rpx;
}

.r-n1 {
	right: 4rpx;
}

.r--n1 {
	right: -4rpx;
}

.r-n2 {
	right: 8rpx;
}

.r--n2 {
	right: -8rpx;
}

.r-n3 {
	right: 12rpx;
}

.r--n3 {
	right: -12rpx;
}

.r-n4 {
	right: 16rpx;
}

.r--n4 {
	right: -16rpx;
}

.r-n5 {
	right: 20rpx;
}

.r--n5 {
	right: -20rpx;
}

.r-n6 {
	right: 24rpx;
}

.r--n6 {
	right: -24rpx;
}

.r-n7 {
	right: 28rpx;
}

.r--n7 {
	right: -28rpx;
}

.r-n8 {
	right: 32rpx;
}

.r--n8 {
	right: -32rpx;
}

.r-n9 {
	right: 36rpx;
}

.r--n9 {
	right: -36rpx;
}

.r-n10 {
	right: 40rpx;
}

.r--n10 {
	right: -40rpx;
}

.r-n11 {
	right: 44rpx;
}

.r--n11 {
	right: -44rpx;
}

.r-n12 {
	right: 48rpx;
}

.r--n12 {
	right: -48rpx;
}

.r-n13 {
	right: 52rpx;
}

.r--n13 {
	right: -52rpx;
}

.r-n14 {
	right: 56rpx;
}

.r--n14 {
	right: -56rpx;
}

.r-n15 {
	right: 60rpx;
}

.r--n15 {
	right: -60rpx;
}

.r-n16 {
	right: 64rpx;
}

.r--n16 {
	right: -64rpx;
}

.r-n17 {
	right: 68rpx;
}

.r--n17 {
	right: -68rpx;
}

.r-n18 {
	right: 72rpx;
}

.r--n18 {
	right: -72rpx;
}

.r-n19 {
	right: 76rpx;
}

.r--n19 {
	right: -76rpx;
}

.r-n20 {
	right: 80rpx;
}

.r--n20 {
	right: -80rpx;
}

.r-n21 {
	right: 84rpx;
}

.r--n21 {
	right: -84rpx;
}

.r-n22 {
	right: 88rpx;
}

.r--n22 {
	right: -88rpx;
}

.r-n23 {
	right: 92rpx;
}

.r--n23 {
	right: -92rpx;
}

.r-n24 {
	right: 96rpx;
}

.r--n24 {
	right: -96rpx;
}

.r-n25 {
	right: 100rpx;
}

.r--n25 {
	right: -100rpx;
}

.b-0 {
	bottom: 0rpx;
}

.b--0 {
	bottom: -0rpx;
}

.b-1 {
	bottom: 1rpx;
}

.b--1 {
	bottom: -1rpx;
}

.b-2 {
	bottom: 2rpx;
}

.b--2 {
	bottom: -2rpx;
}

.b-3 {
	bottom: 3rpx;
}

.b--3 {
	bottom: -3rpx;
}

.b-4 {
	bottom: 4rpx;
}

.b--4 {
	bottom: -4rpx;
}

.b-5 {
	bottom: 5rpx;
}

.b--5 {
	bottom: -5rpx;
}

.b-6 {
	bottom: 6rpx;
}

.b--6 {
	bottom: -6rpx;
}

.b-7 {
	bottom: 7rpx;
}

.b--7 {
	bottom: -7rpx;
}

.b-8 {
	bottom: 8rpx;
}

.b--8 {
	bottom: -8rpx;
}

.b-9 {
	bottom: 9rpx;
}

.b--9 {
	bottom: -9rpx;
}

.b-10 {
	bottom: 10rpx;
}

.b--10 {
	bottom: -10rpx;
}

.b-11 {
	bottom: 11rpx;
}

.b--11 {
	bottom: -11rpx;
}

.b-12 {
	bottom: 12rpx;
}

.b--12 {
	bottom: -12rpx;
}

.b-13 {
	bottom: 13rpx;
}

.b--13 {
	bottom: -13rpx;
}

.b-14 {
	bottom: 14rpx;
}

.b--14 {
	bottom: -14rpx;
}

.b-15 {
	bottom: 15rpx;
}

.b--15 {
	bottom: -15rpx;
}

.b-16 {
	bottom: 16rpx;
}

.b--16 {
	bottom: -16rpx;
}

.b-17 {
	bottom: 17rpx;
}

.b--17 {
	bottom: -17rpx;
}

.b-18 {
	bottom: 18rpx;
}

.b--18 {
	bottom: -18rpx;
}

.b-19 {
	bottom: 19rpx;
}

.b--19 {
	bottom: -19rpx;
}

.b-20 {
	bottom: 20rpx;
}

.b--20 {
	bottom: -20rpx;
}

.b-21 {
	bottom: 21rpx;
}

.b--21 {
	bottom: -21rpx;
}

.b-22 {
	bottom: 22rpx;
}

.b--22 {
	bottom: -22rpx;
}

.b-23 {
	bottom: 23rpx;
}

.b--23 {
	bottom: -23rpx;
}

.b-24 {
	bottom: 24rpx;
}

.b--24 {
	bottom: -24rpx;
}

.b-25 {
	bottom: 25rpx;
}

.b--25 {
	bottom: -25rpx;
}

.b-26 {
	bottom: 26rpx;
}

.b--26 {
	bottom: -26rpx;
}

.b-27 {
	bottom: 27rpx;
}

.b--27 {
	bottom: -27rpx;
}

.b-28 {
	bottom: 28rpx;
}

.b--28 {
	bottom: -28rpx;
}

.b-29 {
	bottom: 29rpx;
}

.b--29 {
	bottom: -29rpx;
}

.b-30 {
	bottom: 30rpx;
}

.b--30 {
	bottom: -30rpx;
}

.b-31 {
	bottom: 31rpx;
}

.b--31 {
	bottom: -31rpx;
}

.b-32 {
	bottom: 32rpx;
}

.b--32 {
	bottom: -32rpx;
}

.b-33 {
	bottom: 33rpx;
}

.b--33 {
	bottom: -33rpx;
}

.b-34 {
	bottom: 34rpx;
}

.b--34 {
	bottom: -34rpx;
}

.b-35 {
	bottom: 35rpx;
}

.b--35 {
	bottom: -35rpx;
}

.b-36 {
	bottom: 36rpx;
}

.b--36 {
	bottom: -36rpx;
}

.b-37 {
	bottom: 37rpx;
}

.b--37 {
	bottom: -37rpx;
}

.b-38 {
	bottom: 38rpx;
}

.b--38 {
	bottom: -38rpx;
}

.b-39 {
	bottom: 39rpx;
}

.b--39 {
	bottom: -39rpx;
}

.b-40 {
	bottom: 40rpx;
}

.b--40 {
	bottom: -40rpx;
}

.b-41 {
	bottom: 41rpx;
}

.b--41 {
	bottom: -41rpx;
}

.b-42 {
	bottom: 42rpx;
}

.b--42 {
	bottom: -42rpx;
}

.b-43 {
	bottom: 43rpx;
}

.b--43 {
	bottom: -43rpx;
}

.b-44 {
	bottom: 44rpx;
}

.b--44 {
	bottom: -44rpx;
}

.b-45 {
	bottom: 45rpx;
}

.b--45 {
	bottom: -45rpx;
}

.b-46 {
	bottom: 46rpx;
}

.b--46 {
	bottom: -46rpx;
}

.b-47 {
	bottom: 47rpx;
}

.b--47 {
	bottom: -47rpx;
}

.b-48 {
	bottom: 48rpx;
}

.b--48 {
	bottom: -48rpx;
}

.b-49 {
	bottom: 49rpx;
}

.b--49 {
	bottom: -49rpx;
}

.b-50 {
	bottom: 50rpx;
}

.b--50 {
	bottom: -50rpx;
}

.b-n1 {
	bottom: 4rpx;
}

.b--n1 {
	bottom: -4rpx;
}

.b-n2 {
	bottom: 8rpx;
}

.b--n2 {
	bottom: -8rpx;
}

.b-n3 {
	bottom: 12rpx;
}

.b--n3 {
	bottom: -12rpx;
}

.b-n4 {
	bottom: 16rpx;
}

.b--n4 {
	bottom: -16rpx;
}

.b-n5 {
	bottom: 20rpx;
}

.b--n5 {
	bottom: -20rpx;
}

.b-n6 {
	bottom: 24rpx;
}

.b--n6 {
	bottom: -24rpx;
}

.b-n7 {
	bottom: 28rpx;
}

.b--n7 {
	bottom: -28rpx;
}

.b-n8 {
	bottom: 32rpx;
}

.b--n8 {
	bottom: -32rpx;
}

.b-n9 {
	bottom: 36rpx;
}

.b--n9 {
	bottom: -36rpx;
}

.b-n10 {
	bottom: 40rpx;
}

.b--n10 {
	bottom: -40rpx;
}

.b-n11 {
	bottom: 44rpx;
}

.b--n11 {
	bottom: -44rpx;
}

.b-n12 {
	bottom: 48rpx;
}

.b--n12 {
	bottom: -48rpx;
}

.b-n13 {
	bottom: 52rpx;
}

.b--n13 {
	bottom: -52rpx;
}

.b-n14 {
	bottom: 56rpx;
}

.b--n14 {
	bottom: -56rpx;
}

.b-n15 {
	bottom: 60rpx;
}

.b--n15 {
	bottom: -60rpx;
}

.b-n16 {
	bottom: 64rpx;
}

.b--n16 {
	bottom: -64rpx;
}

.b-n17 {
	bottom: 68rpx;
}

.b--n17 {
	bottom: -68rpx;
}

.b-n18 {
	bottom: 72rpx;
}

.b--n18 {
	bottom: -72rpx;
}

.b-n19 {
	bottom: 76rpx;
}

.b--n19 {
	bottom: -76rpx;
}

.b-n20 {
	bottom: 80rpx;
}

.b--n20 {
	bottom: -80rpx;
}

.b-n21 {
	bottom: 84rpx;
}

.b--n21 {
	bottom: -84rpx;
}

.b-n22 {
	bottom: 88rpx;
}

.b--n22 {
	bottom: -88rpx;
}

.b-n23 {
	bottom: 92rpx;
}

.b--n23 {
	bottom: -92rpx;
}

.b-n24 {
	bottom: 96rpx;
}

.b--n24 {
	bottom: -96rpx;
}

.b-n25 {
	bottom: 100rpx;
}

.b--n25 {
	bottom: -100rpx;
}

.l-0 {
	left: 0rpx;
}

.l--0 {
	left: -0rpx;
}

.l-1 {
	left: 1rpx;
}

.l--1 {
	left: -1rpx;
}

.l-2 {
	left: 2rpx;
}

.l--2 {
	left: -2rpx;
}

.l-3 {
	left: 3rpx;
}

.l--3 {
	left: -3rpx;
}

.l-4 {
	left: 4rpx;
}

.l--4 {
	left: -4rpx;
}

.l-5 {
	left: 5rpx;
}

.l--5 {
	left: -5rpx;
}

.l-6 {
	left: 6rpx;
}

.l--6 {
	left: -6rpx;
}

.l-7 {
	left: 7rpx;
}

.l--7 {
	left: -7rpx;
}

.l-8 {
	left: 8rpx;
}

.l--8 {
	left: -8rpx;
}

.l-9 {
	left: 9rpx;
}

.l--9 {
	left: -9rpx;
}

.l-10 {
	left: 10rpx;
}

.l--10 {
	left: -10rpx;
}

.l-11 {
	left: 11rpx;
}

.l--11 {
	left: -11rpx;
}

.l-12 {
	left: 12rpx;
}

.l--12 {
	left: -12rpx;
}

.l-13 {
	left: 13rpx;
}

.l--13 {
	left: -13rpx;
}

.l-14 {
	left: 14rpx;
}

.l--14 {
	left: -14rpx;
}

.l-15 {
	left: 15rpx;
}

.l--15 {
	left: -15rpx;
}

.l-16 {
	left: 16rpx;
}

.l--16 {
	left: -16rpx;
}

.l-17 {
	left: 17rpx;
}

.l--17 {
	left: -17rpx;
}

.l-18 {
	left: 18rpx;
}

.l--18 {
	left: -18rpx;
}

.l-19 {
	left: 19rpx;
}

.l--19 {
	left: -19rpx;
}

.l-20 {
	left: 20rpx;
}

.l--20 {
	left: -20rpx;
}

.l-21 {
	left: 21rpx;
}

.l--21 {
	left: -21rpx;
}

.l-22 {
	left: 22rpx;
}

.l--22 {
	left: -22rpx;
}

.l-23 {
	left: 23rpx;
}

.l--23 {
	left: -23rpx;
}

.l-24 {
	left: 24rpx;
}

.l--24 {
	left: -24rpx;
}

.l-25 {
	left: 25rpx;
}

.l--25 {
	left: -25rpx;
}

.l-26 {
	left: 26rpx;
}

.l--26 {
	left: -26rpx;
}

.l-27 {
	left: 27rpx;
}

.l--27 {
	left: -27rpx;
}

.l-28 {
	left: 28rpx;
}

.l--28 {
	left: -28rpx;
}

.l-29 {
	left: 29rpx;
}

.l--29 {
	left: -29rpx;
}

.l-30 {
	left: 30rpx;
}

.l--30 {
	left: -30rpx;
}

.l-31 {
	left: 31rpx;
}

.l--31 {
	left: -31rpx;
}

.l-32 {
	left: 32rpx;
}

.l--32 {
	left: -32rpx;
}

.l-33 {
	left: 33rpx;
}

.l--33 {
	left: -33rpx;
}

.l-34 {
	left: 34rpx;
}

.l--34 {
	left: -34rpx;
}

.l-35 {
	left: 35rpx;
}

.l--35 {
	left: -35rpx;
}

.l-36 {
	left: 36rpx;
}

.l--36 {
	left: -36rpx;
}

.l-37 {
	left: 37rpx;
}

.l--37 {
	left: -37rpx;
}

.l-38 {
	left: 38rpx;
}

.l--38 {
	left: -38rpx;
}

.l-39 {
	left: 39rpx;
}

.l--39 {
	left: -39rpx;
}

.l-40 {
	left: 40rpx;
}

.l--40 {
	left: -40rpx;
}

.l-41 {
	left: 41rpx;
}

.l--41 {
	left: -41rpx;
}

.l-42 {
	left: 42rpx;
}

.l--42 {
	left: -42rpx;
}

.l-43 {
	left: 43rpx;
}

.l--43 {
	left: -43rpx;
}

.l-44 {
	left: 44rpx;
}

.l--44 {
	left: -44rpx;
}

.l-45 {
	left: 45rpx;
}

.l--45 {
	left: -45rpx;
}

.l-46 {
	left: 46rpx;
}

.l--46 {
	left: -46rpx;
}

.l-47 {
	left: 47rpx;
}

.l--47 {
	left: -47rpx;
}

.l-48 {
	left: 48rpx;
}

.l--48 {
	left: -48rpx;
}

.l-49 {
	left: 49rpx;
}

.l--49 {
	left: -49rpx;
}

.l-50 {
	left: 50rpx;
}

.l--50 {
	left: -50rpx;
}

.l-n1 {
	left: 4rpx;
}

.l--n1 {
	left: -4rpx;
}

.l-n2 {
	left: 8rpx;
}

.l--n2 {
	left: -8rpx;
}

.l-n3 {
	left: 12rpx;
}

.l--n3 {
	left: -12rpx;
}

.l-n4 {
	left: 16rpx;
}

.l--n4 {
	left: -16rpx;
}

.l-n5 {
	left: 20rpx;
}

.l--n5 {
	left: -20rpx;
}

.l-n6 {
	left: 24rpx;
}

.l--n6 {
	left: -24rpx;
}

.l-n7 {
	left: 28rpx;
}

.l--n7 {
	left: -28rpx;
}

.l-n8 {
	left: 32rpx;
}

.l--n8 {
	left: -32rpx;
}

.l-n9 {
	left: 36rpx;
}

.l--n9 {
	left: -36rpx;
}

.l-n10 {
	left: 40rpx;
}

.l--n10 {
	left: -40rpx;
}

.l-n11 {
	left: 44rpx;
}

.l--n11 {
	left: -44rpx;
}

.l-n12 {
	left: 48rpx;
}

.l--n12 {
	left: -48rpx;
}

.l-n13 {
	left: 52rpx;
}

.l--n13 {
	left: -52rpx;
}

.l-n14 {
	left: 56rpx;
}

.l--n14 {
	left: -56rpx;
}

.l-n15 {
	left: 60rpx;
}

.l--n15 {
	left: -60rpx;
}

.l-n16 {
	left: 64rpx;
}

.l--n16 {
	left: -64rpx;
}

.l-n17 {
	left: 68rpx;
}

.l--n17 {
	left: -68rpx;
}

.l-n18 {
	left: 72rpx;
}

.l--n18 {
	left: -72rpx;
}

.l-n19 {
	left: 76rpx;
}

.l--n19 {
	left: -76rpx;
}

.l-n20 {
	left: 80rpx;
}

.l--n20 {
	left: -80rpx;
}

.l-n21 {
	left: 84rpx;
}

.l--n21 {
	left: -84rpx;
}

.l-n22 {
	left: 88rpx;
}

.l--n22 {
	left: -88rpx;
}

.l-n23 {
	left: 92rpx;
}

.l--n23 {
	left: -92rpx;
}

.l-n24 {
	left: 96rpx;
}

.l--n24 {
	left: -96rpx;
}

.l-n25 {
	left: 100rpx;
}

.l--n25 {
	left: -100rpx;
}

.flex {
	display: flex;
}

.flex-col {
	flex-direction: column;
}

.flex-wrap {
	flex-flow: row wrap;
}

/* #ifndef APP-PLUS-NVUE */
.flex-shrink {
	flex-shrink: 0;
}

/* #endif */
.flex-row {
	flex-direction: row;
}

.flex-reverse {
	flex-direction: row-reverse;
}

.flex-row-top-start {
	justify-content: flex-start;
	align-items: flex-start;
	display: flex;
}

.flex-row-top-center {
	justify-content: center;
	align-items: flex-start;
	display: flex;
}

.flex-row-top-end {
	justify-content: flex-end;
	align-items: flex-start;
	display: flex;
}

.flex-row-center-start {
	justify-content: flex-start;
	align-items: center;
	display: flex;
}

.flex-row-center-center {
	justify-content: center;
	align-items: center;
	display: flex;
}

.flex-row-center-end {
	justify-content: flex-end;
	align-items: center;
	display: flex;
}

.flex-row-bottom-start {
	justify-content: flex-start;
	align-items: flex-end;
	display: flex;
}

.flex-row-bottom-center {
	justify-content: center;
	align-items: flex-end;
	display: flex;
}

.flex-row-bottom-end {
	justify-content: flex-end;
	align-items: flex-end;
	display: flex;
}

.flex-row-center-between {
	justify-content: space-between;
	align-items: center;
	display: flex;
}

.flex-col-top-start {
	justify-content: flex-start;
	align-items: flex-start;
	display: flex;
}

.flex-col-top-center {
	justify-content: flex-start;
	align-items: center;
	display: flex;
}

.flex-col-top-end {
	justify-content: flex-start;
	align-items: flex-end;
	display: flex;
}

.flex-col-center-start {
	justify-content: center;
	align-items: flex-start;
	display: flex;
}

.flex-col-center-center {
	justify-content: center;
	align-items: center;
	display: flex;
}

.flex-col-center-end {
	justify-content: center;
	align-items: flex-end;
	display: flex;
}

.flex-col-bottom-start {
	justify-content: flex-end;
	align-items: flex-start;
	display: flex;
}

.flex-col-bottom-center {
	justify-content: flex-end;
	align-items: center;
	display: flex;
}

.flex-col-bottom-end {
	justify-content: flex-end;
	align-items: flex-end;
	display: flex;
}

.flex-start {
	justify-content: flex-start;
	align-items: center;
	display: flex;
}

.flex-end {
	justify-content: flex-end;
	align-items: center;
	display: flex;
}

.flex-center {
	justify-content: center;
	align-items: center;
	display: flex;
}

.flex-between {
	justify-content: space-between;
}

.flex-col-full {
	flex-direction: column;
	align-items: stretch;
}

.flex-around {
	justify-content: space-around;
}

.flex-0 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 0;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 0;
	/* #endif */
}

.flex-1 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 1;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 1;
	/* #endif */
}

.flex-2 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 2;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 2;
	/* #endif */
}

.flex-3 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 3;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 3;
	/* #endif */
}

.flex-4 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 4;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 4;
	/* #endif */
}

.flex-5 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 5;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 5;
	/* #endif */
}

.flex-6 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 6;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 6;
	/* #endif */
}

.flex-7 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 7;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 7;
	/* #endif */
}

.flex-8 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 8;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 8;
	/* #endif */
}

.flex-9 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 9;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 9;
	/* #endif */
}

.flex-10 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 10;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 10;
	/* #endif */
}

.flex-11 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 11;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 11;
	/* #endif */
}

.flex-12 {
	/* #ifndef APP-PLUS-NVUE */
	flex-grow: 12;
	/* #endif */
	/* #ifdef APP-PLUS-NVUE */
	flex: 12;
	/* #endif */
}

.text-red {
	color: #f44336;
}

.red {
	background-color: #f44336;
}

.text-pink {
	color: #e91e63;
}

.pink {
	background-color: #e91e63;
}

.text-purple {
	color: #9c27b0;
}

.purple {
	background-color: #9c27b0;
}

.text-deep-purple {
	color: #673ab7;
}

.deep-purple {
	background-color: #673ab7;
}

.text-indigo {
	color: #3f51b5;
}

.indigo {
	background-color: #3f51b5;
}

.text-blue {
	color: #2196f3;
}

.blue {
	background-color: #2196f3;
}

.text-light-blue {
	color: #03a9f4;
}

.light-blue {
	background-color: #03a9f4;
}

.text-cyan {
	color: #00bcd4;
}

.cyan {
	background-color: #00bcd4;
}

.text-teal {
	color: #009688;
}

.teal {
	background-color: #009688;
}

.text-green {
	color: #4caf50;
}

.green {
	background-color: #4caf50;
}

.text-light-green {
	color: #8bc34a;
}

.light-green {
	background-color: #8bc34a;
}

.text-lime {
	color: #cddc39;
}

.lime {
	background-color: #cddc39;
}

.text-yellow {
	color: #ffeb3b;
}

.yellow {
	background-color: #ffeb3b;
}

.text-amber {
	color: #ffc107;
}

.amber {
	background-color: #ffc107;
}

.text-orange {
	color: #ff9800;
}

.orange {
	background-color: #ff9800;
}

.text-deep-orange {
	color: #ff5722;
}

.deep-orange {
	background-color: #ff5722;
}

.text-brown {
	color: #795548;
}

.brown {
	background-color: #795548;
}

.text-blue-grey {
	color: #607d8b;
}

.blue-grey {
	background-color: #607d8b;
}

.text-grey {
	color: #9e9e9e;
}

.grey {
	background-color: #9e9e9e;
}

.text-black {
	color: #000000;
}

.black {
	background-color: #000000;
}

.text-white {
	color: #ffffff;
}

.white {
	background-color: #ffffff;
}

.text-lighten-5 {
	color: #fafafa;
}

.lighten-5 {
	background-color: #fafafa;
}

.text-lighten-4 {
	color: #f5f5f5;
}

.lighten-4 {
	background-color: #f5f5f5;
}

.text-lighten-3 {
	color: #eeeeee;
}

.lighten-3 {
	background-color: #eeeeee;
}

.text-lighten-2 {
	color: #e0e0e0;
}

.lighten-2 {
	background-color: #e0e0e0;
}

.text-lighten-1 {
	color: #bdbdbd;
}

.lighten-1 {
	background-color: #bdbdbd;
}

.text-darken-1 {
	color: #757575;
}

.darken-1 {
	background-color: #757575;
}

.text-darken-2 {
	color: #616161;
}

.darken-2 {
	background-color: #616161;
}

.text-darken-3 {
	color: #424242;
}

.darken-3 {
	background-color: #424242;
}

.text-darken-4 {
	color: #212121;
}

.darken-4 {
	background-color: #212121;
}

.text-darken-5 {
	color: #131313;
}

.darken-5 {
	background-color: #131313;
}

.text-darken-6 {
	color: #0a0a0a;
}

.darken-6 {
	background-color: #0a0a0a;
}
