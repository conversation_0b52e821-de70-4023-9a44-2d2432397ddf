import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
function replacePlugin({ imageUrlPrefix }) {
	return {
		name: 'replace-plugin',
		transform(code, id) {
			if (id.endsWith('.vue') || id.endsWith('.nvue') || id.endsWith('.css')) {
				code = code.replace(/imageUrlPrefix/g, imageUrlPrefix);
			}
			return {
				code,
				map: null,
			};
		},
	};
}

export default defineConfig({
	// build: {
	// 	sourcemap: false,
	// 	minify: 'terser',
	// 	terserOptions: {
	// 		compress: {
	// 			drop_console: true,
	// 		},
	// 	},
	// },
	plugins: [
		replacePlugin({
			imageUrlPrefix: 'https://wx.wansao.com/statics/wsyjq/test', // 将页面中的imageUrlPrefix全部替换成真实路径
		}),
		uni()
	],
	// server: {
	// 	port : 3001,  
	// 	proxy: {  
	// 		'api': {
	// 			target: 'https://wx.wansao.com/client',  
	// 			changeOrigin: true,
	// 			rewrite: (path) => path.replace(/^\/api/, '')
	// 		},  
	// 	}  
	// }
});