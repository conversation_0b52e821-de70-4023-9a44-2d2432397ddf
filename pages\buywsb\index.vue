<template>
    <tm-app>
        <view class="container">
            <view class="topCont" :style="{ backgroundImage: `url('https://wx.wansao.com/statics/family/sendhall/dh_bg.png')` }">
                <view class="userInfo">
                    <view class="userInfo-left">
                        <image :src="avater" mode="" class="userInfo-left-avater" />
                        <view>
                            <view class="userInfo-left-name">{{name}}</view>
                            <view class="userInfo-left-wsb">我的皖嫂币<text>{{wsb}}个</text></view>
                        </view>
                    </view>
                    <navigator url="../wsb_mx/index" class="userInfo-right">
                        <image src="/static/img/dh_mx.png" mode="" class="userInfo-right-icon" />
                        <text class="userInfo-right-mx">查看我的明细</text>
                    </navigator>
                </view>
            </view>

            <view class="duihuan">
                <view class="buywsb">
                    <view class="buywsb-tit">
                        <image src="/static/img/ysqb_jb.png" mode=""/>
                        <view class="buywsb-tit-title">购买皖嫂币</view>
                    </view>

                    <view class="buywsb-choose">
                        <view 
                            v-for="(item, index) in buyWsbList" 
                            :key="index"
                            class="buywsb-choose-item"
                            :class="{ active: cur === index }"
                            @click="biClick(index, item.num, item.amount)"
                        >
                            {{item.num}}币
                        </view>
                        <view 
                            class="buywsb-choose-item"
                            :class="{ active: cur === 5 }"
                        >
                            <input 
                                type="number" 
                                placeholder="其他" 
                                class="custom-input"
                                @input="otherBiSumInput"
                                @focus="otherBiSumFocus"
                            />
                        </view>
                    </view>

                    <view class="buywsb-intro">
                        <view class="buywsb-intro-tit">说明：</view>
                        <view class="buywsb-intro-des">
                            <view>1、皖嫂币可以为家政工作人员兑换优先派单权、仅限在皖嫂公司内部使用。</view>
                            <view>2、一经购买，不支持退换</view>
                            <view>3、最终解释权归皖嫂所有。</view>
                        </view>
                    </view>

                    <view class="buywsb-btn" @click="buyBtn">
                        <text v-if="amount">￥{{amount}}</text> 立即购买
                    </view>
                </view>

                <view class="banquan">安徽皖嫂家政服务有限责任公司</view>
            </view>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'

// 分享功能
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// 响应式数据
const wsb = ref(0)
const avater = ref('')
const name = ref('')
const cur = ref(-1)
const bisum = ref(0)
const buyWsbList = ref([])
const price = ref(0)
const amount = ref(0)

// 获取用户信息
const getuserInfo = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/getUserWxInfo',
            type: 'POST'
        })
        if (res.code === 200) {
            avater.value = res.data.wxInfo.avatarUrl
            name.value = res.data.wxInfo.nickname
            wsb.value = res.data.total
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
    }
}

// 获取页面配置信息
const getData = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/discount',
            type: 'POST'
        })
        if (res.code === 200) {
            buyWsbList.value = res.data.list
            price.value = res.data.price
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取配置失败:', error)
    }
}

// 点击选择币数
const biClick = (index: number, num: number, amountValue: number) => {
    if (cur.value === index) return
    cur.value = index
    bisum.value = num
    amount.value = amountValue
}

// 输入其他币数
const otherBiSumInput = (e: any) => {
    bisum.value = e.detail.value
    getAmount()
}

// 聚焦其他币数输入框
const otherBiSumFocus = (e: any) => {
    cur.value = 5
    bisum.value = e.detail.value
    getAmount()
}

// 计算金额
const getAmount = () => {
    const inputNumber = parseFloat(bisum.value)
    const list = buyWsbList.value
    const priceValue = price.value

    if (Number.isInteger(inputNumber) && inputNumber > 0) {
        let calculatedAmount = 0

        for (const item of list) {
            if (item.num === inputNumber) {
                calculatedAmount = item.amount
                break
            }
        }

        if (calculatedAmount === 0) {
            calculatedAmount = Number((inputNumber * priceValue).toFixed(2))
        }

        bisum.value = inputNumber
        amount.value = calculatedAmount
    } else {
        uni.showToast({
            title: '请输入正整数',
            icon: 'none'
        })
        amount.value = 0
    }
}

// 购买按钮点击
const buyBtn = async () => {
    const num = bisum.value

    if (cur.value === -1) {
        uni.showToast({
            title: '请选择购买数量选项或者输入购买数量',
            icon: 'none'
        })
        return
    }

    if (Number.isInteger(num) && num > 0) {
        try {
            const res = await api.request.ajax({
                url: 'https://wx.wansao.com/api/Sendhall/doPay',
                type: 'POST',
                data: {
                    num: bisum.value,
                    amount: amount.value
                }
            })
            if (res.code === 200) {
                uni.requestPayment({
                    timeStamp: res.data.pay_params.timeStamp,
                    nonceStr: res.data.pay_params.nonceStr,
                    package: res.data.pay_params.package,
                    signType: 'MD5',
                    paySign: res.data.pay_params.paySign,
                    success() {
                        uni.showModal({
                            title: '',
                            content: '支付成功！',
                            showCancel: false,
                            success(res) {
                                if (res.confirm) {
                                    uni.navigateTo({
                                        url: '../ysqiangdan/index'
                                    })
                                }
                            }
                        })
                    },
                    fail(err) {
                        console.error('用户支付扣款失败', err)
                    }
                })
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
        } catch (error) {
            console.error('支付失败:', error)
        }
    } else {
        uni.showToast({
            title: '请输入正整数',
            icon: 'none'
        })
    }
}

// 生命周期钩子
onLoad(() => {

    getuserInfo()
    getData()
})

onShow(() => {
    getuserInfo()
    setTimeout(() => {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#E33631'
        })
    }, 100);
})
</script>

<style lang="scss" scoped>
page {
    background-color: #f4f6fa;
    font-size: 26rpx;
    color: #6f6e6e;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.topCont {
    width: 750rpx;
    height: 437rpx;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.userInfo {
    width: 750rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60rpx;

    &-left {
        display: flex;
        align-items: center;
        margin-left: 60rpx;

        &-avater {
            width: 130rpx;
            height: 130rpx;
            border-radius: 50%;
            border: 1rpx solid #f0d1d3;
            margin-right: 30rpx;
        }

        &-name {
            font-size: 40rpx;
            color: #fff;
        }

        &-wsb {
            font-size: 30rpx;
            color: #fff;

            text {
                font-size: 40rpx;
                margin-left: 5rpx;
            }
        }
    }

    &-right {
        width: 240rpx;
        height: 72rpx;
        line-height: 72rpx;
        background: #c11312;
        border-radius: 30rpx 0 0 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &-mx {
            color: #fff;
            font-size: 28rpx;
        }

        &-icon {
            width: 25rpx;
            height: 27rpx;
            margin-right: 15rpx;
        }
    }
}

.duihuan {
    position: relative;
    top: -190rpx;
}

.buywsb {
    width: 690rpx;
    background: #fff;
    border-radius: 20rpx;
    position: relative;
    box-shadow: 0 3rpx 16rpx #ddd;
    margin-bottom: 30rpx;
    padding: 60rpx 50rpx 15rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    &-tit {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        width: 100%;

        image {
            width: 26rpx;
            height: 28rpx;
        }

        &-title {
            font-size: 28rpx;
            color: #766d6d;
            margin-left: 10rpx;
        }
    }

    &-choose {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        &-item {
            width: 186rpx;
            height: 122rpx;
            line-height: 122rpx;
            border: 1px solid #bfbfbf;
            text-align: center;
            margin-bottom: 35rpx;
            border-radius: 10rpx;
            color: #606266;
            font-size: 30rpx;
            position: relative;

            .custom-input {
                width: 186rpx;
                height: 122rpx;
                line-height: 122rpx;
                outline: none;
                text-align: center;
            }

            &.active {
                border: 1px solid #df2121;
                position: relative;
                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 0;
                    height: 0;
                    border-style: solid;
                    border-width: 0 0 40rpx 40rpx;
                    border-color: transparent transparent #df2121 transparent;
                }

                &::before {
                    content: '✔';
                    width: 20rpx;
                    height: 20rpx;
                    padding-bottom: 4rpx;
                    position: absolute;
                    right: 2rpx;
                    bottom: 0;
                    font-size: 20rpx;
                    font-weight: bold;
                    color: #fff;
                    z-index: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
    }

    &-intro {
        &-tit {
            font-size: 30rpx;
            margin-bottom: 10rpx;
        }

        &-des {
            font-size: 28rpx;

            view {
                margin-bottom: 10rpx;
                color: #909399;
            }
        }
    }

    &-btn {
        width: 440rpx;
        height: 70rpx;
        line-height: 70rpx;
        background: #df2121;
        text-align: center;
        font-size: 30rpx;
        color: #fff;
        border-radius: 30rpx;
        margin: 30rpx 0;
    }
}

.banquan {
    color: #c6c1c1;
    text-align: center;
    font-size: 28rpx;
}
</style> 