/*   
*   功能：应用外唤起地图并标注地点  
*   特点：uni.openLocation()的简易替代品，唤起指定地图的某个标点。  
*   优点：不依赖uniapi，无须集成sdk，无须声明位置权限，兼容多个地图。  
*   缺点：非应用内置地图，依赖文档等等等。  
*   日期：2021年12月15日  
#   用法  
    import mapTool from 'xxx.js'  
    mapTool.navTo(point, map)  
#   提示  
    1.未加入Google Map，未使用WGS84坐标系，需要唤起后直接导航 或 地址逆解析，请自行解决。  
    2.（重要）使用腾讯地图scheme需要开发者Key，请自行注册。  
    3.（重要）使用查询地图是否安装的方法mapsExist()，iOS9以后需要添加白名单才可查询。即在manifest.json文件plus->distribute->apple->urlschemewhitelist节点下添加（如urlschemewhitelist:["xxx"]），名称请参考下方MapsInfo对象各属性的i_wlname。  
    4.Document:  
    <https://lbsyun.baidu.com/index.php?title=uri>  
    <https://lbs.amap.com/api/amap-mobile/summary>  
    <https://lbs.qq.com/webApi/uriV1/uriGuide/uriOverview>  
    <https://developer.apple.com/library/ios/featuredarticles/iPhoneURLScheme_Reference/MapLinks/MapLinks.html>  
    <https://developers.google.com/maps/documentation/ios/urlscheme>  
*/  

// 腾讯地图开发者Key  
const QQMapKey = "XXXXX-XXXXX-XXXXX-XXXXX-XXXXX-XXXXX"  

// 1. 添加 Platform 类型
const Platform: string = plus.os.name;

const MapsInfo: Record<string, MapInfo> = {  
    baidu: {  
        url: 'http://map.baidu.com',  
        CN: '百度地图',  
        EN: 'bdmap',  
        w_uri: 'http://api.map.baidu.com',  
        a_intent: 'baidumap://',  
        a_pname: 'com.baidu.BaiduMap', //package name  
        i_scheme: 'baidumap://', // action  
        i_wlname: 'baidumap' // white list name  
    },  
    gaode: {  
        url: 'https://m.amap.com',  
        CN: '高德地图',  
        EN: 'amap',  
        w_uri: 'https://uri.amap.com',  
        a_intent: 'androidamap://',  
        a_pname: 'com.autonavi.minimap', //package name  
        i_scheme: 'iosamap://', // action  
        i_wlname: 'iosamap' // white list name  
    },  
    tengxun: {  
        url: 'https://map.qq.com',  
        CN: '腾讯地图',  
        EN: 'qqmap',  
        w_uri: 'https://apis.map.qq.com',  
        a_intent: 'qqmap://',  
        a_pname: 'com.tencent.map', //package name  
        i_scheme: 'qqmap://', // action  
        i_wlname: 'qqmap' // white list name  
    },  
    pingguo: { // 白名单需要增加maps，才可检测到系统地图  
        url: 'http://maps.apple.com',  
        CN: '系统地图',  
        EN: 'maps',  
        w_uri: 'http://maps.apple.com',  
        a_intent: 'androidamap://', // 安卓高德地图，理论上用不到  
        a_pname: 'com.autonavi.minimap', // 安卓高德地图，理论上用不到  
        i_scheme: 'maps://', // action  
        i_wlname: 'maps' // white list name  
    }  
}  
const DefaultMap = MapsInfo.tengxun // 据自身业务采用【腾讯地图】作为默认地图，腾讯地图scheme需要开发者Key  
const DefaultPoint = { // 默认标注，【找不到所在位置】意为lat、lng缺失；【未知位置】意为lbl缺失；  
    lat: 22.517007, // gcj02  
    lng: 113.392532, // gcj02  
    lbl: '找不到所在位置', // label  
    dtl: '未知地址' // detail  
}  

// 10. 添加 DefaultService 类型
interface ServiceConfig {
    web: string;
    ios: string;
    android: string;
}

const DefaultService: ServiceConfig = {
    web: 'web.xxx.app',
    ios: 'ios.xxx.app',
    android: 'android.xxx.app'
};

// 首先定义必要的接口
interface Point {
    lat: number;
    lng: number;
    lbl: string;
    dtl: string;
}

interface MapInfo {
    url: string;
    CN: string;
    EN: string;
    w_uri: string;
    a_intent: string;
    a_pname: string;
    i_scheme: string;
    i_wlname: string;
}

// 为 plus 添加类型声明
declare const plus: {
    os: {
        name: string;
    };
    runtime: {
        isApplicationExist(args: { pname: string; action: string }): boolean;
        launchApplication(args: { pname?: string; action?: string }, callback: (e: Error) => void): void;
        openURL(url: string, callback: (e: Error) => void): void;
    };
};

// 修改坐标转换函数
const gcj2bd = function(lat: number, lng: number): Point {
    const point: Point = {
        lat: 0,
        lng: 0,
        lbl: '',
        dtl: ''
    };
    const x_pi = 3.14159265358979324 * 3000.0 / 180.0;
    const x = Number(lng);
    const y = Number(lat);
    const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
    const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
    point.lng = z * Math.cos(theta) + 0.0065;
    point.lat = z * Math.sin(theta) + 0.006;
    return point;
}

// BD-09(百度坐标系) To GCJ-02(腾讯、高德等火星坐���系)  
const bd2gcj = function(lat: number, lng: number): Point {  
    const point: Point = {  
        lat: 0,  
        lng: 0,  
        lbl: '',  
        dtl: ''  
    };  
    const x_pi = 3.14159265358979324 * 3000.0 / 180.0;  
    const x = lng - 0.0065;  
    const y = lat - 0.006;  
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);  
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);  
    point.lng = z * Math.cos(theta);  
    point.lat = z * Math.sin(theta);  
    return point;  
}  

// URL参数 To JSON对象  
const query2json = function(query: string): Record<string, string> {  
    const param: Record<string, string> = {};  
    query.replace(/([^?&]+)=([^?&]+)/g, function(s: string, v: string, k: string): string {  
        param[v] = decodeURIComponent(k);  
        return k + '=' + v;  
    });  
    return param;  
}  

// 深拷贝  
function _isArray(arr: any): boolean {  
    return Object.prototype.toString.call(arr) === '[object Array]';  
}  
function _deepClone<T>(obj: T): T {  
    if([null, undefined, NaN, false].includes(obj as any)) return obj;  
    if(typeof obj !== "object" && typeof obj !== 'function') {  
        return obj;  
    }  
    const o: any = _isArray(obj) ? [] : {};  
    for(const i in obj as any) {  
        if(Object.prototype.hasOwnProperty.call(obj, i)){  
            o[i] = typeof (obj as any)[i] === "object" ? _deepClone((obj as any)[i]) : (obj as any)[i];  
        }  
    }  
    return o as T;  
}  

// 修改 mapsExist 函数的返回值类型
const mapsExist = function(): MapInfo[] {  
    const res: MapInfo[] = [];  
    if (plus.runtime.isApplicationExist({  
            pname: MapsInfo.baidu.a_pname,  
            action: MapsInfo.baidu.i_scheme  
        })) {  
        console.log("[已安装百度地图]");  
        res.push(MapsInfo.baidu)  
    }  
    if (plus.runtime.isApplicationExist({  
            pname: MapsInfo.gaode.a_pname,  
            action: MapsInfo.gaode.i_scheme  
        })) {  
        console.log("[已安装高德地图]");  
        res.push(MapsInfo.gaode)  
    }  
    if (plus.runtime.isApplicationExist({  
            pname: MapsInfo.tengxun.a_pname,  
            action: MapsInfo.tengxun.i_scheme  
        })) {  
        console.log("[已安装腾讯地图]");  
        res.push(MapsInfo.tengxun)  
    }  
    if (Platform == 'iOS' && plus.runtime.isApplicationExist({  
            pname: MapsInfo.pingguo.a_pname,  
            action: MapsInfo.pingguo.i_scheme  
        })) {  
        console.log("[已安装系统地图]");  
        res.push(MapsInfo.pingguo)  
    }  
    return res  
}  

// 仅仅是打开地图应用（不荐）  
const openApp = function(map: MapInfo): void {  
    if (Platform === "Android") {  
        plus.runtime.launchApplication({  
            pname: map.a_pname,  
        }, function(e: Error) {  
            alert("Open system default browser failed: " + e.message);  
        });  
    } else if (Platform === "iOS") {  
        plus.runtime.launchApplication({  
            action: map.i_scheme  
        }, function(e: Error) {  
            alert("Open system default browser failed: " + e.message);  
        });  
    } else {  
        alert("Operating system is not supported");  
    }  
}  

// 坐标参数处理  
const argHandle = function(point?: Partial<Point>): Point {  
    if (!point) {  
        return DefaultPoint;  
    }  
    const pot = _deepClone(point);  
    if (!pot.lat || !pot.lng) {  
        return DefaultPoint;  
    }  
    if (!pot.lbl) {  
        pot.lbl = '未知位置';  
    }  
    if (!pot.dtl) {  
        pot.dtl = '未知地址';  
    }  
    return pot as Point;  
}  

// 修改 json2query 函数
const json2query = function(json: Record<string, any>): string {
    const param: string[] = [];
    Object.keys(json).forEach(e => {
        param.push(e + '=' + encodeURIComponent(json[e]));
    });
    return param.join('&');
}

// 修改 openByApp 函数
const openByApp = function(map: MapInfo, point: Point): Promise<boolean> {
    return new Promise((resolve) => {
        let pot = argHandle(point)
        let url = ''
        if (map.EN == 'bdmap') {  
            // 腾讯坐标系转换为百度坐标  
            let p = gcj2bd(pot.lat, pot.lng)  
            pot = {  
                ...pot,  
                ...p  
            }  

            if (Platform == 'Android') {  
                url =  
                    `bdapp://map/marker?location=${pot.lat},${pot.lng}&title=${pot.lbl}&content=${pot.dtl}&src=${DefaultService.android}`  
            } else if (Platform == 'iOS') {  
                url =  
                    `baidumap://map/marker?location=${pot.lat},${pot.lng}&title=${pot.lbl}&content=${pot.dtl}&src=${DefaultService.ios}`  
            }  
        } else if (map.EN == 'amap') {  
            if (Platform == 'Android') {  
                url =  
                    `androidamap://viewMap?lat=${pot.lat}&lon=${pot.lng}&poiname=${pot.lbl}&sourceApplication=${DefaultService.android}&dev=0`  
            } else if (Platform == 'iOS') {  
                url =  
                    `iosamap://viewMap?lat=${pot.lat}&lon=${pot.lng}&poiname=${pot.lbl}&sourceApplication=${DefaultService.ios}&dev=0`  
            }  
        } else if (map.EN == 'qqmap') {  
            if (Platform == 'Android') {  
                url =  
                    `qqmap://map/marker?marker=coord:${pot.lat},${pot.lng};title:${pot.lbl};addr:${pot.dtl}&referer=${QQMapKey}`  
            } else if (Platform == 'iOS') {  
                url =  
                    `qqmap://map/marker?marker=coord:${pot.lat},${pot.lng};title:${pot.lbl};addr:${pot.dtl}&referer=${QQMapKey}`  
            }  
        } else if (map.EN == 'maps') {  
            if (Platform == 'Android') {  
                url =  
                    `androidamap://viewMap?lat=${pot.lat}&lon=${pot.lng}&poiname=${pot.lbl}&sourceApplication=${DefaultService.android}&dev=0`  
            } else if (Platform == 'iOS') {  
                // Unlike some schemes, map URLs do not start with a "maps" scheme identifier. Instead, map links are specified as regular http links and are opened either in Safari or the Maps app on the target platform.  
                url = `http://maps.apple.com?ll=${pot.lat},${pot.lng}&q=${pot.lbl}`  
            }  
        }  

        if (url != '') {  
            plus.runtime.openURL(encodeURI(url), async function(e) {  
                console.error(e)  
                let result = await openByBrowser(map, point)  
                resolve(result)  
            });  
            resolve(true)  
        } else {  
            console.error('应用内唤起失败')  
            resolve(false)  
        }  

    })  
}  

// 浏览器唤起地图应用，并标注位置      C2B2C   对应导航path请查阅api文档  
const openByBrowser = function(map: MapInfo, point: Point): Promise<boolean> {  
    return new Promise((resolve) => {  
        let pot = argHandle(point)  
        let url = ''  
        if (map.EN == 'bdmap') {  
            // 腾讯坐标系转换为百度坐标  
            let p = gcj2bd(pot.lat, pot.lng)  
            pot = {  
                ...pot,  
                ...p  
            }  
            url =  
                `http://api.map.baidu.com/marker?location=${pot.lat},${pot.lng}&title=${pot.lbl}&content=${pot.dtl}&src=${DefaultService.web}&output=html`  
        } else if (map.EN == 'amap') {  
            url =  
                `https://uri.amap.com/marker?position=${pot.lng},${pot.lat}&name=${pot.lbl}&src=${DefaultService.web}&callnative=1`  
        } else if (map.EN == 'qqmap') {  
            url =  
                `https://apis.map.qq.com/uri/v1/marker?marker=coord:${pot.lat},${pot.lng};title:${pot.lbl};addr:${pot.dtl}&referer=${DefaultService.web}`  
        } else if (map.EN == 'maps') {  
            url = `http://maps.apple.com?ll=${pot.lat},${pot.lng}&q=${pot.lbl}`  
        }  

        if (url != '') {  
            plus.runtime.openURL(encodeURI(url), function(e) {  
                console.error(e)  
                resolve(false)  
            });  
            resolve(true)  
        } else {  
            console.error('浏览器唤起失败')  
            resolve(false)  
        }  

    })  
}  

// 修改 navTo 函数
export const navTo = async function(point: Point, map?: string): Promise<boolean> {
    let result = false;
    const res: MapInfo[] = mapsExist();
    
    if (res.length > 0) {
        const index = res.findIndex(e => e.EN === map);
        
        if (index !== -1) {
            result = await openByApp(res[index], point);
        } else if (res.findIndex(e => e.EN === DefaultMap.EN) !== -1) {
            result = await openByApp(DefaultMap, point);
        } else {
            result = await openByApp(res[0], point);
        }
    } else {
        const arr = Object.values(MapsInfo);
        const index = arr.findIndex(e => e.EN === map);
        
        if (index !== -1) {
            result = await openByBrowser(arr[index], point);
        } else {
            result = await openByBrowser(DefaultMap, point);
        }
    }
    return result;
}  

