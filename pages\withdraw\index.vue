<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle || '提现'" :scrollTop="scrollTop" :showBack="true"/>
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="resume_card">
				<image class="bg-image" src="/static/img/resume_cardbg.png" mode="aspectFill"></image>
				<view class="content">
					<view class="top">
						<view class="avatar_area">
							<tm-image class="avatar" :width="130" :height="130"
								:src="apiData.photo || '/static/img/banner4.png'"></tm-image>
						</view>
						<view class="user_info">
							<view class="name_wrap">
								<tm-text class="name" :font-size="30" color="#606060"
									:label="apiData.name"></tm-text>
								<tm-text class="age" :font-size="30" color="#606060"
									:label="apiData.age" v-if="apiData.age"></tm-text>
							</view>
							<view class="extra_info" v-if="apiData.workYear || apiData.area">
								<tm-text class="work_year" :font-size="24" color="#858585"
									:label="apiData.workYear + '年经验'" v-if="apiData.workYear"></tm-text>
								<tm-text class="area" :font-size="24" color="#858585"
									:label="apiData.area" v-if="apiData.area"></tm-text>
							</view>
						</view>
					</view>
					<view class="amount">
						<view class="amount_wrap">
							<view class="amount_info">
								<tm-text class="amount_num" :font-size="48" color="#EA001A"
									:label="'¥ ' + apiData.amountTotal"></tm-text>
								<tm-text class="amount_detail" :font-size="20" color="#A4A4A4" label="明细 >"
									@click="goLink('/pages/amount_details/index')"></tm-text>
							</view>
							<view class="withdraw_btn" @click="showWin = true">立即提现</view>
						</view>
					</view>
				</view>
			</view>

			<view class="mod_card">
				<view class="title">银行卡信息</view>
				<template v-if="bankData?.wageInfo && Object.keys(bankData.wageInfo).length">
					<view class="info">
						<view class="left">{{bankData?.wageInfo?.banklist?.name}}</view>
						<view class="right">{{bankData?.wageInfo?.banklist?.value}}</view>
					</view>
					<view class="info">
						<view class="left">{{bankData?.wageInfo?.cardholder?.name}}</view>
						<view class="right">{{bankData?.wageInfo?.cardholder?.value}}</view>
					</view>
					<view class="info">
						<view class="left">{{bankData?.wageInfo?.bank_number?.name}}</view>
						<view class="right">{{bankData?.wageInfo?.bank_number?.value}}</view>
					</view>
					<view class="info">
						<view class="left">{{bankData?.wageInfo?.relation?.name}}</view>
						<view class="right">{{bankData?.wageInfo?.relation?.value}}</view>
					</view>
					<view class="info" v-if="bankData?.wageInfo?.remark?.value">
						<view class="left">{{bankData?.wageInfo?.remark?.name}}</view>
						<view class="right">{{bankData?.wageInfo?.remark?.value}}</view>
					</view>
				</template>
				<view class="no_data" v-else>
					<tm-text :font-size="28" color="#999" label="暂无银行卡信息"></tm-text>
				</view>
			</view>
			<!-- <view class="mod_card">
				<view class="title">提现记录</view>
				<view class="info">
					<view class="left">2018-9-13</view>
					<view class="right">申请提现150元，已发放</view>
				</view>
				<view class="info">
					<view class="left">2018-9-13</view>
					<view class="right">申请提现150元，已发放</view>
				</view>
				<view class="info">
					<view class="left">2018-9-13</view>
					<view class="right">申请提现150元，已发放</view>
				</view>
			</view> -->
			<tm-overlay v-model:show="showWin" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
				<view class="popup" @click.stop="">
					<view class="popup_content">
						<view class="p1">您的提现申请已成功提交</view>
						<view class="p1">将由会计审核通过后发放</view>
						<view class="button" @click="showWin = false">确定</view>
					</view>
					<view class="close" @click="showWin = false"></view>
				</view>
			</tm-overlay>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const { NavigationBarTitle } = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})
const apiData = ref({
	name: '',
	age: '',
	area: '',
	workYear: 0,
	photo: '',
	evaluateCount: 0,
	jobsCount: 0,
	rate: '0%',
	amountTotal: 0,
	recommend: {
		peopleNum: 0,
		peopleAmount: 0
	}
})

const getData = async () => {
	const res = await api.request.ajax({
		url: '/money/index',
		type: 'POST',
	})
	if (res.code === 1) {
		apiData.value = res.data
	}
}

const bankData = ref({
	wageInfo: {
		banklist: { name: '', value: '' },
		bank_number: { name: '', value: '' },
		cardholder: { name: '', value: '' },
		relation: { name: '', value: '' },
		remark: { name: '', value: '' }
	}
})

const getBankData = async () => {
	const res = await api.request.ajax({
		url: '/money/withdraw',
		type: 'POST',
	})
	if (res.code === 1) {
		bankData.value = res.data
	}
}

onLoad(() => {
	getData()
	getBankData()
})

const showWin = ref(false)
</script>

<style lang="scss">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;


	.resume_card {
		width: 690rpx;
		min-height: 250rpx;
		position: relative;
		z-index: 1;
		box-shadow: 0rpx 0rpx 10rpx 0rpx #e6e6e6;
		border-radius: 20rpx;
		overflow: hidden;
		
		.bg-image {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
		}
		
		.content {
			position: relative;
			z-index: 2;
			width: 100%;
			height: 100%;
		}

		.top {
			display: flex;
			padding: 56rpx 20rpx 0;

			.avatar_area {
				width: 140rpx;
				height: 140rpx;
				background-color: #fff;
				border-radius: 50%;
				box-shadow: 0rpx -2rpx 7rpx 0rpx rgba(133, 43, 40, 0.4);
				display: flex;
				justify-content: center;
				align-items: center;

				.avatar {
					border-radius: 50%;
					overflow: hidden;
				}
			}

			.user_info {
				margin-left: 20rpx;
				
				.name_wrap {
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					width: 280rpx;

					.name {
						font-weight: bold;
					}

					.age {
						font-weight: bold;
						margin-left: 10rpx;
					}
				}

				.extra_info {
					display: flex;
					align-items: center;
					margin-top: 12rpx;

					.work_year {
						margin-right: 10rpx;
					}
				}
			}
		}

		.stats {
			margin-top: 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 605rpx;
			border-top: 1rpx solid #E6E6E6;
			padding: 42rpx 46rpx;

			.stat_item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.num {
					font-weight: bold;
				}
			}
		}

		.amount {
			position: absolute;
			top: 56rpx;
			right: 30rpx;

			.amount_wrap {
				display: flex;
				flex-direction: column;
				align-items: center;

				.amount_info {
					display: flex;
					align-items: center;

					.amount_num {
						font-weight: bold;
					}

					.amount_detail {
						margin-left: 20rpx;
					}
				}

				.withdraw_btn {
					width: 196rpx;
					height: 61rpx;
					background: linear-gradient(90deg, #FB243C, #F14460);
					border-radius: 30rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}
		}
	}
}

.popup {
	width: 574rpx;
	height: 371rpx;
	background: #FFEEEE;
	border-radius: 20px;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	.popup_content {
		width: 545rpx;
		height: 343rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(251, 86, 90, 0.35);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.p1 {
			width: 400rpx;
			font-weight: bold;
			font-size: 36rpx;
			color: #333333;
			line-height: 70rpx;
		}

		.button {
			margin-top: 40rpx;
			width: 289rpx;
			height: 87rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
			border-radius: 44rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 30rpx;
		}
	}
	.close{
		width: 75rpx;
		height: 75rpx;
		background: #FFFFFF;
		border-radius: 50%;
		border: 4rpx solid #F8999B;
		position: absolute;
		top: -30rpx;
		right: -30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		&:before{
			content: '';
			width: 50rpx;
			height: 8rpx;
			border-radius: 4rpx;
			background-color: #F8999B;
			transform: rotate(45deg);
			position: absolute;
		}
		&:after{
			content: '';
			width: 50rpx;
			height: 8rpx;
			border-radius: 4rpx;
			background-color: #F8999B;
			transform: rotate(-45deg);
			position: absolute;
		}
	}
}

.mod_card {
  .no_data {
    padding: 40rpx 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>