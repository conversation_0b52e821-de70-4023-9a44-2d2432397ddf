<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle || '查看名片'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
			<view class="banner-text">
				<text class="rank-text">已有{{ viewsTotal }}人查看您的名片</text>
			</view>
			<view class="business_card">
				<image src="/static/img/cardbg.png" class="card-bg" mode="widthFix"></image>
				<view class="card-content">
					<view class="avatar-wrapper">
						<view class="avatar-inner">
							<image :src="userInfo.photo"></image>
						</view>
					</view>
					<view class="tag-list">
						<view class="tag" v-for="item in userInfo.certPic" :key="item.tid">
							<image class="tag-icon" :src="item.pic"></image>
							<text class="tag-text">{{ item.name }}</text>
						</view>
					</view>
					<view class="user-info">
						<view class="name-age">
							<text class="name">{{ userInfo.name }}</text>
							<text class="age" v-if="userInfo.age">{{ userInfo.age }}</text>
						</view>
						<view class="experience-area" v-if="userInfo.workYear || userInfo.area">
							<text class="experience" v-if="userInfo.workYear">{{ userInfo.workYear }}年经验</text>
							<text class="area-text" v-if="userInfo.area">{{ userInfo.area }}</text>
						</view>
						<view v-if="userInfo?.jobName?.[0]">
							<view class="job-item" v-for="item in userInfo.jobName" :key="item">
								<text class="job-name">{{ item }}</text>
							</view>
						</view>
					</view>
					<image class="qr-code" :src="userInfo?.resumeCard?.qrLink"></image>
					<view class="scan-text">长按查看简历</view>
					<view class="contact">
						<text class="contact-text">服务热线电话</text>
						<image class="phone" src="/static/img/96538.png"></image>
					</view>
					<text class="address">合肥市长江中路57号 省妇女儿童活动中心四楼</text>
				</view>
			</view>
			<view class="button save-btn" @click="saveCard" v-if="is_complete === 1">点击保存到手机相册，可随时分享给别人</view>
			<view class="button disabled-btn" v-else>因基础信息不完整，暂不能保存分享</view>
		</view>
		<canvas id="cardCanvas" type="2d" style="width: 690rpx; height: 1185rpx; position: fixed; left: -9999rpx;"></canvas>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage,onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const { NavigationBarTitle } = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})
const userInfo = computed(() => store.userInfo)
const is_complete = ref(false)
const viewsTotal = ref(0)
const getResumeIndex = async () => {
	const res = await api.request.ajax({
		url: '/Center/resumeIndex',
		type: 'POST',
	})
	if (res.code === 1) {
		is_complete.value = res.data.is_complete
		viewsTotal.value = res.data.viewsTotal
	}
}
onLoad(() => {
	getResumeIndex()
})

// Add saveCard function
const saveCard = async () => {
	try {
		// 获取canvas实例和context
		const query = uni.createSelectorQuery()
		const canvas = await new Promise(resolve => {
			query.select('#cardCanvas')
				.fields({ node: true, size: true })
				.exec((res) => {
					resolve(res[0].node)
				})
		})

		const ctx = canvas.getContext('2d')

		// 初始化画布大小
		const systemInfo = uni.getSystemInfoSync()
		const scale = systemInfo.windowWidth / 750 // Convert rpx to px
		const dpr = systemInfo.pixelRatio
		canvas.width = 690 * scale * dpr
		canvas.height = 1185 * scale * dpr
		ctx.scale(dpr, dpr)

		// 预加载所有图片资源
		const loadImage = async (src) => {
			const image = canvas.createImage()
			await new Promise((resolve, reject) => {
				image.onload = resolve
				image.onerror = reject
				image.src = src
			})
			return image
		}

		const [
			preloadedAvatar,
			preloadedQr,
			preloadedPhone,
			preloadedBg,
			...certImages
		] = await Promise.all([
			userInfo.value.photo ? loadImage(userInfo.value.photo) : null,
			loadImage(userInfo.value?.resumeCard?.qrLink || '/static/img/area_bg1.png'),
			loadImage('/static/img/96538.png'),
			loadImage('/static/img/cardbg.png'),
			...(userInfo.value.certPic || []).map(item => loadImage(item.pic))
		])

		// 清空画布
		ctx.clearRect(0, 0, canvas.width, canvas.height)

		const cardWidth = 690 * scale
		const cardHeight = 1100 * scale
		const cardRadius = 26 * scale

		// 绘制卡片背景和阴影
		ctx.save()
		ctx.beginPath()
		ctx.moveTo(cardRadius, 100 * scale)
		ctx.arcTo(cardWidth, 100 * scale, cardWidth, cardHeight, cardRadius)
		ctx.arcTo(cardWidth, cardHeight, 0, cardHeight, cardRadius)
		ctx.arcTo(0, cardHeight, 0, 100 * scale, cardRadius)
		ctx.arcTo(0, 100 * scale, cardWidth, 100 * scale, cardRadius)
		ctx.closePath()

		// 绘制阴影和填充
		ctx.shadowOffsetX = 0
		ctx.shadowOffsetY = 0
		ctx.shadowBlur = 12 * scale
		ctx.shadowColor = 'rgba(227,227,227,0.87)'
		ctx.fillStyle = '#FFFFFF'
		ctx.fill()
		ctx.restore()

		// 绘制头像
		if (preloadedAvatar) {
			const avatarCenterX = cardWidth / 2
			const avatarCenterY = 100 * scale
			const avatarBgRadius = 100 * scale
			const avatarRadius = 85 * scale

			// 绘制头像白色背景圈
			ctx.save()
			ctx.beginPath()
			ctx.arc(avatarCenterX, avatarCenterY, avatarBgRadius, 0, 2 * Math.PI)
			ctx.fillStyle = '#FFFFFF'
			ctx.shadowOffsetX = 0
			ctx.shadowOffsetY = 0
			ctx.shadowBlur = 12 * scale
			ctx.shadowColor = 'rgba(227,227,227,0.87)'
			ctx.fill()
			ctx.restore()

			// 绘制头像
			ctx.save()
			ctx.beginPath()
			ctx.arc(avatarCenterX, avatarCenterY, avatarRadius, 0, 2 * Math.PI)
			ctx.clip()
			ctx.drawImage(preloadedAvatar,
				avatarCenterX - avatarRadius,
				avatarCenterY - avatarRadius,
				avatarRadius * 2,
				avatarRadius * 2
			)
			ctx.restore()
		}

		// 绘制认证标签
		if (userInfo.value.certPic) {
			const tagStartY = 230 * scale
			const tagWidth = 138 * scale
			const tagHeight = 58 * scale
			const tagSpacing = 20 * scale
			const tagRadius = 29 * scale
			const totalWidth = tagWidth * userInfo.value.certPic.length + tagSpacing * (userInfo.value.certPic.length - 1)
			const tagsStartX = (cardWidth - totalWidth) / 2

			userInfo.value.certPic.forEach((item, index) => {
				const tagX = tagsStartX + index * (tagWidth + tagSpacing)

				// 绘制标签背景
				ctx.save()
				ctx.beginPath()
				ctx.moveTo(tagX + tagRadius, tagStartY)
				ctx.arcTo(tagX + tagWidth, tagStartY, tagX + tagWidth, tagStartY + tagHeight, tagRadius)
				ctx.arcTo(tagX + tagWidth, tagStartY + tagHeight, tagX, tagStartY + tagHeight, tagRadius)
				ctx.arcTo(tagX, tagStartY + tagHeight, tagX, tagStartY, tagRadius)
				ctx.arcTo(tagX, tagStartY, tagX + tagWidth, tagStartY, tagRadius)
				ctx.closePath()
				ctx.fillStyle = '#FFFFFF'
				ctx.shadowOffsetX = 0
				ctx.shadowOffsetY = 0
				ctx.shadowBlur = 12 * scale
				ctx.shadowColor = 'rgba(227,227,227,0.87)'
				ctx.fill()
				ctx.restore()

				// 绘制标签图标和文字
				ctx.drawImage(certImages[index], tagX, tagStartY, 58 * scale, 58 * scale)
				ctx.font = `${24 * scale}px sans-serif`
				ctx.fillStyle = '#606060'
				ctx.textAlign = 'left'
				ctx.fillText(item.name, tagX + 68 * scale, tagStartY + 38 * scale)
			})
		}

		// 绘制用户信息
		const centerX = cardWidth / 2
		const nameY = 370 * scale
		ctx.textAlign = 'center'

		// 姓名和年龄
		ctx.font = `${36 * scale}px sans-serif`
		ctx.fillStyle = '#606060'
		let nameWidth = ctx.measureText(userInfo.value.name || '').width
		let ageWidth = 0
		let ageText = ''
		if (userInfo.value.age) {
			ageText = userInfo.value.age.toString()
			ageWidth = ctx.measureText(ageText).width + 20 * scale
		}

		const totalWidth = nameWidth + ageWidth
		const startX = centerX - totalWidth / 2

		ctx.textAlign = 'left'
		ctx.fillText(userInfo.value.name || '', startX, nameY)
		if (userInfo.value.age) {
			ctx.fillText(ageText, startX + nameWidth + 20 * scale, nameY)
		}

		// 工作经验和地区
		if (userInfo.value.workYear || userInfo.value.area) {
			ctx.font = `${26 * scale}px sans-serif`
			ctx.fillStyle = '#858585'
			const expY = 430 * scale

			let expText = userInfo.value.workYear ? `${userInfo.value.workYear}年经验` : ''
			let areaText = userInfo.value.area || ''
			let expWidth = expText ? ctx.measureText(expText).width : 0
			let areaWidth = areaText ? ctx.measureText(areaText).width : 0
			let expAreaGap = 10 * scale

			const expAreaTotalWidth = expWidth + (expText && areaText ? expAreaGap : 0) + areaWidth
			const expAreaStartX = centerX - expAreaTotalWidth / 2

			if (expText) {
				ctx.fillText(expText, expAreaStartX, expY)
			}
			if (areaText) {
				ctx.fillText(areaText, expAreaStartX + expWidth + expAreaGap, expY)
			}
		}

		// 绘制职位名称
		if (userInfo.value?.jobName?.[0]) {
			const jobY = 490 * scale
			userInfo.value.jobName.forEach((job, index) => {
				const currentY = jobY + index * 40 * scale
				ctx.font = `${26 * scale}px sans-serif`
				ctx.fillStyle = '#606060'
				ctx.textAlign = 'center'
				ctx.fillText(job, centerX, currentY)
			})
		}

		// 调整二维码位置
		const qrSize = 234 * scale
		const qrX = centerX - qrSize / 2
		ctx.drawImage(preloadedQr, qrX, 540 * scale, qrSize, qrSize)

		// 调整扫码提示位置
		ctx.fillStyle = '#000000'
		ctx.font = `${22 * scale}px sans-serif`
		ctx.textAlign = 'center'
		ctx.fillText('长按查看简历', centerX, 840 * scale)

		// 调整联系方式位置
		ctx.font = `${22 * scale}px sans-serif`
		ctx.fillStyle = '#606060'
		const contactText = '服务热线电话'
		const phoneWidth = 153 * scale
		const contactTextWidth = ctx.measureText(contactText).width
		const contactGap = 5 * scale
		const contactTotalWidth = contactTextWidth + contactGap + phoneWidth
		const contactStartX = centerX - contactTotalWidth / 2

		ctx.textAlign = 'left'
		ctx.fillText(contactText, contactStartX, 920 * scale)
		ctx.drawImage(preloadedPhone,
			contactStartX + contactTextWidth + contactGap,
			880 * scale,
			phoneWidth,
			57 * scale
		)

		// 调整地址位置
		ctx.font = `${26 * scale}px sans-serif`
		ctx.textAlign = 'center'
		ctx.fillText('合肥市长江中路57号 省妇女儿童活动中心四楼', centerX, 1000 * scale)

		// 绘制底部背景图
		const bgHeight = 238 * scale
		ctx.drawImage(preloadedBg,
			0,
			cardHeight - bgHeight,
			cardWidth,
			bgHeight
		)

		// 保存图片
		const tempFilePath = await new Promise((resolve, reject) => {
			uni.canvasToTempFilePath({
				canvas,
				success: res => resolve(res.tempFilePath),
				fail: reject
			})
		})

		await new Promise((resolve, reject) => {
			uni.saveImageToPhotosAlbum({
				filePath: tempFilePath,
				success: () => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
					resolve(null)
				},
				fail: reject
			})
		})

	} catch (error) {
		console.error('Save card error:', error)
		uni.showToast({
			title: '保存失败',
			icon: 'error'
		})
	}
}
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.banner-text {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;

	.rank-text {
		font-size: 30rpx;
		color: #fff;
		text-align: center;
	}
}


.button {
	margin-top: 22rpx;
	width: 690rpx;
	height: 90rpx;
	border-radius: 45rpx;
	color: #fff;
	font-size: 30rpx;
	font-weight: 500;
	display: flex;
	justify-content: center;
	align-items: center;
}

.save-btn {
	background: linear-gradient(-15deg, #F31630, #FF6136);
	box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
}

.disabled-btn {
	background: linear-gradient(-31deg, #7681F6, #574DF2);
}
</style>