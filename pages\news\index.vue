<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'热点新闻'" :scrollTop="scrollTop" :showBack="true" />
			<view class="swipe">
				<tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
					color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goLink(bannerList[i].link)"></tm-carousel>
			</view>
			<view class="area">
				<ShareTreasure 
					:list="newsList" 
					:subtitle="subtitle"
					:current-tab="currentTab"
					:loading="loading"
					:has-more="hasMore"
					@tab-change="handleTabChange"
					@load-more="loadMore"
					@detail="handleDetail"
				/>
			</view>
		</view>
	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, watch, getCurrentInstance, onMounted } from "vue"
	import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
	import tmCarousel from '@/tmui/components/tm-carousel/tm-carousel2.vue'
	import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
	import { snb } from '@/components/customNavigationBar/snb'
	import { useStore } from '@/until/mainpinia';
	import { share } from '@/tmui/tool/lib/share'
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	import { useNewsList } from '@/composables/useNewsList'
	import ShareTreasure from '@/components/ShareTreasure/ShareTreasure.vue'

	//分享功能
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const saveType = ref(0)
	const { 
		newsList,
		subtitle,
		bannerList,
		currentTab,
		loading,
		hasMore,
		getNewsList,
		handleTabChange,
		loadMore,
		handleDetail
	} = useNewsList()

	onLoad((e) => {
		getNewsList().then(() => {
			if(e.type){
				saveType.value = Number(e.type)
				currentTab.value = saveType.value
			}
		})
	})

	const { NavigationBarTitle } = snb()
	// 页面数据
	const store = useStore()
	const scrollTop = ref(0)
	onPageScroll((e) => {
		scrollTop.value = e.scrollTop
	})

</script>

<style lang="less" scoped>
	.main {
		width: 750rpx;
		overflow-x: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		.area{
			margin-top: 300rpx;
			width: 750rpx;
			background-color: #fff;
			border-radius: 20rpx 20rpx 0 0;
			position: relative;
			z-index: 2;
			padding-bottom: 100rpx;
		}
	}
</style>