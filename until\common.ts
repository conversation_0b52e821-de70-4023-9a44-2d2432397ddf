export function throttle<T extends (...args: any[]) => any>(fn: T, wait: number = 1000): (...args: Parameters<T>) => ReturnType<T> | void {
    let pre = Date.now();
    let firstFlag = true;

    return function(this: any, ...args: Parameters<T>): ReturnType<T> | void {
        const now = Date.now();

        if ((now - pre >= wait) || firstFlag) {
            firstFlag = false;
            pre = now;
            return fn.apply(this, args);
        }
    };
}
