<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'简历宝'" :scrollTop="scrollTop" />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
			<view class="resume_cardbg">
				<image src="/static/img/resume_cardbg.png" class="resume_cardbg_mainImg"></image>
				<view class="resume_cardbg_content">
					<view class="p1"><text class="p1_text">简历宝</text></view>
					<view class="p2">管理个人资料、证照<br>下载名片，邀请客户评价</view>
				</view>
				<view class="resume_cardbg_content">
					<view class="resume_cardbg_button" @click="goLink('/pages/resume/index')">我的简历</view>
					<view class="resume_cardbg_button resume_cardbg_button2" @click="goLink('/pages/business/index')">我的名片海报</view>
				</view>
			</view>
			<view class="mt-n12">
				<tm-carousel autoplay :round="0" :width="721" :height="197" :indicatorDots="false" imgmodel="widthFix"
					color="#FCD9DB" :list="adList" rangKey="src" @click="item=>goLink(item.url||'/pages/news/index?type=1')"></tm-carousel>
			</view>
			<view class="area2">
				<view v-for="(item,index) in cardList" :key="index">
					<view class="resume_card" @click="goLink(item.url)" v-if="item.show">
						<image class="bg-image" :src="item.bgImg" mode="aspectFill"></image>
						<view class="card_content">
							<view class="p1">{{item.title}}</view>
							<view class="p2">{{item.desc}}</view>
						</view>
					</view>
				</view>
			</view>
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import {snb} from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const {NavigationBarTitle} = snb()
// 页面数据
const store = useStore()
const adList = computed(() => store.setting.adList)
const scrollTop = ref(0)
// 简历卡片数据
const cardList = ref([])
const is_complete = ref(0)
const resumeScore = ref(0)

const getResumeIndex = async () => {
	const res = await api.request.ajax({
		url: '/Center/resumeIndex',
		type: 'POST',
	})
	if (res.code === 1) {
		cardList.value = res.data.btnList
		is_complete.value = res.data.is_complete
		resumeScore.value = res.data.resumeScore
	}
}
onLoad(()=>{
	getResumeIndex()
})

onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.resume_cardbg {
		width: 690rpx;
		height: 313rpx;
		position: relative;
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		.resume_cardbg_mainImg{
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}
		.resume_cardbg_content{
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			position: relative;
			z-index: 2;
			.p1 {
				position: relative;
				.p1_text{
					font-weight: bold;
					font-size: 61rpx;
					color: #333;
					position: relative;
					z-index: 2;
				}
				&::after{
					width: 197rpx;
					height: 14rpx;
					background: rgba(242, 62, 70, 0.45);
					border-radius: 7rpx;
					content: '';
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					bottom: 4rpx;
				}
			}

			.p2 {
				margin-top: 50rpx;
				width: 270rpx;
				font-size: 24rpx;
				color: #9C9C9C;
				line-height: 36rpx;
			}
			.resume_cardbg_button {
				width: 262rpx;
				height: 88rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
				border-radius: 44rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 60rpx;
			}
			.resume_cardbg_button2{
				margin-top: 30rpx;
				background: linear-gradient(-31deg, #7681F6, #574DF2);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(118, 129, 246, 0.41);
			}
		}
	}
	.area2 {
		margin-top: 40rpx;
		width: 688rpx;
	}
}
</style>