<template>
	<view class="remark-list">
		<view class="remark-item" v-for="(item, index) in list" :key="index">
			<view class="remark-content">
				<template v-if="item.type === 'text'">
					{{ item.content }}
				</template>
				<template v-else>
					<view class="duration">{{ item.content }}</view>
					<view class="play-icon" :class="{'playing': currentPlayingIndex === index}" @click="playVoice(index)"></view>
				</template>
			</view>
			<view class="remark-time">{{ item.time }}</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue'

interface RemarkItem {
	content: string;
	time: string;
	type: 'text' | 'voice';
	tempFilePath?: string;
	duration?: number;
}

const props = defineProps<{
	list: RemarkItem[];
}>()

const currentPlayingIndex = ref(-1)
const innerAudioContext = ref<any>(null)

// 播放录音
const playVoice = (index: number) => {
	const item = props.list[index]
	if (!item.tempFilePath) return
	
	// 如果正在播放，先停止
	if (currentPlayingIndex.value === index) {
		innerAudioContext.value?.stop()
		currentPlayingIndex.value = -1
		return
	}
	
	// 如果有其他正在播放，先停止
	if (currentPlayingIndex.value !== -1) {
		innerAudioContext.value?.stop()
	}
	
	// 创建新的音频上下文
	innerAudioContext.value = uni.createInnerAudioContext()
	innerAudioContext.value.autoplay = true
	innerAudioContext.value.src = item.tempFilePath
	
	// 监听播放结束
	innerAudioContext.value.onEnded(() => {
		currentPlayingIndex.value = -1
	})
	
	currentPlayingIndex.value = index
}

// 组件销毁时清理音频上下文
onUnmounted(() => {
	if (innerAudioContext.value) {
		innerAudioContext.value.destroy()
	}
})
</script>

<style lang="less">
.remark-list {
	margin-top: 20rpx;
	padding: 0 14rpx 0 22rpx;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	.remark-item {
		margin-top: 34rpx;
		// max-width: 80%;
		.remark-content{
			background-color: #F0F0F0;
			min-height: 90rpx;
			padding: 16rpx 32rpx;
			border-radius: 16rpx;
			font-size: 28rpx;
			line-height: 40rpx;
			display: flex;
			align-items: center;
			.play-icon {
				width: 64rpx;
				height: 64rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				border-radius: 50%;
				margin-left: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				cursor: pointer;
				flex-shrink: 0;
				
				&.playing {
					display: flex;
					justify-content: center;
					
					&::after {
						content: '';
						width: 6rpx;
						height: 32rpx;
						background-color: #fff;
						margin: 0 4rpx;
					}
					
					&::before {
						content: '';
						width: 6rpx;
						height: 32rpx;
						background-color: #fff;
						margin: 0 4rpx;
					}
				}
				
				&:not(.playing)::after {
					content: '';
					width: 0;
					height: 0;
					border-top: 14rpx solid transparent;
					border-bottom: 14rpx solid transparent;
					border-left: 20rpx solid #fff;
					position: absolute;
				}
			}
		}
		.remark-time {
			margin-top: 8rpx;
			font-size: 18rpx;
			color: #C6C6C6;
		}
	}
}
</style> 