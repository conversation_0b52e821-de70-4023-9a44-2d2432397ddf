<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="档期管理"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="皖嫂派单老师将根据您真实档期为您派单"></tm-text>
				</view>
			</view>
			<view class="area3">
				<view class="charts">
					<view class="list">
						<view class="line" v-for="(item,index) in scheduleData" :key="index">
							<view class="bar">
								<view class="container">
									<tm-progress :width="304" :height="22" color="#fb243c" v-model:percent="item.percent"></tm-progress>
								</view>
							</view>
							<text class="month">{{item.month}}月</text>
							<text class="day">{{item.allDay}}天</text>
						</view>
					</view>
					<text class="tip">注意：标红的为该月安排的天数</text>
				</view>
			</view>
			<view class="card_list">
				<view class="card" v-for="item in itemUser" :key="item.id">
					<view class="card-header">
						<view class="tag tag1" v-if="item.type===0">皖嫂</view>
						<view class="tag tag2" v-if="item.type===1">自签</view>
					</view>
					<view class="card-content">
						<view class="period">
							<text class="label">上户周期：</text>
							<text class="value">{{item.date}}</text>
						</view>
						<view class="employer" @click="call(item.phone)">
							<tm-icon color="red" :font-size="30" name="/static/img/phone.svg"></tm-icon>
							<view class="name">
								<text class="name_text1">雇主</text>
								<text class="name_text2">{{item.name}}</text>
							</view>
						</view>
					</view>
					<view class="card-footer" v-if="item.navigationSwitch">
						<view class="address">
							<text class="label">上户地址：</text>
							<text class="value">{{item.address}}</text>
						</view>
						<view class="employer" @click="navi(item)">
							<tm-icon color="red" :font-size="30" name="tmicon-paperplane-fill"></tm-icon>
							<view class="name">
								<text class="name_text1">点击</text>
								<text class="name_text2">导航</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="float_area">
				<view class="button1" @click="goLink('/pages/clock/index')">上下户打卡</view>
				<view class="button2" @click="goLink('/pages/ysqiangdan/index')"><text>用皖嫂币</text><text>优先派单</text></view>
				<view class="button3" @click="goLink('/pages/info_schedule_add/index')">添加档期/意向客户</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { navTo } from '@/until/map'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})
const call = (tel) => uni.$tm.u.callPhone(tel)
const navi = item =>{
	// #ifndef APP-PLUS
		uni.openLocation({
			latitude:Number(item.latitude),
			longitude:Number(item.longitude),
			name:item.name,
			address:item.address
		})
	// #endif
	// #ifdef APP-PLUS
		let point = {
			lat: Number(item.latitude), // gcj02  
			lng: Number(item.longitude), // gcj02  
			lbl: item.name, // label  
			dtl: item.address // detail  
		}
		navTo(point,'amap')
	// #endif
}
const scheduleData = ref([])
const itemUser = ref([])
const getData = async () => {
	const res = await api.request.ajax({
		url: '/Center/manageList',
		type: 'POST',
		data: {
			page: 1
		}
	})
	if (res.code === 1) {
		scheduleData.value = res.data.scheduleData
		itemUser.value = res.data.itemUser
	}
}
onShow(()=>{
	getData()
})
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.card_list{
		.card{
			margin-top: 20rpx;
			width: 690rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
			border-radius: 26rpx;
			padding: 0;
			position: relative;
			overflow: hidden;
			
			.card-header{
				padding: 20rpx 30rpx;
				position: relative;
				
				.tag{
					width: 200rpx;
					height: 60rpx;
					border-radius: 30rpx;
					font-size: 30rpx;
					color: #fff;
					display: flex;
					align-items: center;
					justify-content: center;
					font-weight: 500;
				}
				.tag1{
					background: #FF3333;
				}
				.tag2{
					background: #1E90FF;
				}
			}
			
			.card-content, .card-footer{
				padding: 20rpx 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-top: 1rpx solid #F3F3F3;
				
				.period, .address{
					flex: 1;
					.label{
						font-size: 26rpx;
						color: #606060;
					}
					.value{
						font-size: 26rpx;
						color: #606060;
						word-break: break-all;
						letter-spacing: -2rpx;
					}
				}
				
				.employer {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 10rpx 15rpx;
					background: rgba(251, 36, 60, 0.05);
					border-radius: 12rpx;
					transition: all 0.3s;
					min-width: 140rpx;
					
					&:active {
						transform: scale(0.95);
						background: rgba(251, 36, 60, 0.1);
					}
					
					.name {
						display: flex;
						flex-direction: column;
						align-items: center;
						margin-left: 10rpx;
						min-width: 80rpx;
						.name_text1{
							font-size: 22rpx;
							color: #606060;
							text-align: center;
							line-height: 1.3;
						}
						.name_text2{
							font-size: 22rpx;
							color: #606060;
							text-align: center;
							line-height: 1.3;
							word-break: keep-all;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}
	.area3{
		margin-top: 40rpx;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;
		.charts{
			width: 635rpx;
		}
	}

	.float_area{
		width: 750rpx;
		height: 163rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -5rpx 18rpx 0rpx rgba(181,181,181,0.14);
		position: fixed;
		bottom: 0;
		padding: 19rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		color: #fff;
		font-size: 30rpx;
		.button1{
			width: 186rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #58D0FD, #4A87F8);
			border-radius: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			
		}
		.button2{
			width: 198rpx;
			height: 90rpx;
			background: linear-gradient(-31deg, #FFA146, #FF7B1A);
			border-radius: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;
		}
		.button3{
			width: 310rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			border-radius: 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
