<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="工作照"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="资料更新后，经审核无误会展示出来！"></tm-text>
				</view>
			</view>
			<view class="area">
				<view class="fulled">
					<tm-image-group>
						<view class="flex-row-center-start flex-wrap fulled">
							<view class="flex-col flex-col-center-center flex-shrink mt-n11 witem" v-for="item in photoList" :key="item.id">
								<tm-image
								:width="140" 
								:height="140" 
								:round="7" 
								preview
								:src="item.thumbnail"
								model="aspectFill" 
								class="flex-shrink"
								></tm-image>
								<view class="status" :class="{'wait': item.date === '待审核', 'reject': item.date === '审核不通过'}">
									{{item.date}}
								</view>
								<view class="delete" @click="handleDeletePhoto(item.id)">删除</view>
							</view>
						</view>
					</tm-image-group>
				</view>
			</view>
			<view class="flex-center mt-n18 fixed b-n25">
				<view class="button2" @click="chooseImage">添加照片</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue"
import { onLoad } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { useIdCardUpload } from '@/until/useIdCardUpload'

//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
const photoList = ref([])
const page = ref(1)
const token = computed(() => store.token)
const cardUpload = useIdCardUpload()
const { deletePhoto } = cardUpload

// 获取工作照列表
const getWorkPhoto = async () => {
	const res = await api.request.ajax({
		url: '/Center/workPhoto',
		type: 'POST',
		data: {
			page: page.value
		}
	})
	if(res.code === 1) {
		photoList.value = res.data.resumePhoto.list
	}
}

// 选择图片
const chooseImage = async () => {
	uni.chooseImage({
		count: 9,
		success: async (res) => {
			try {
				uni.showLoading({
					title: '上传中...'
				})
				
				const files = res.tempFilePaths.map(url => ({
					url,
					uid: Date.now() + Math.random().toString(36).slice(2),
					status: 0
				}))

				const processedFiles = await cardUpload.chooesefileAfter(files)
				const results = await cardUpload.uploadImages(processedFiles, {
					token:token.value,
					hid: store?.userInfo?.hid,
					type: '6'  // 工作照类型
				})

				if(results.length > 0) {
					await getWorkPhoto()
					uni.showToast({
						title: '上传成功',
						icon: 'success'
					})
				}
			} catch(e) {
				console.error('Upload failed:', e)
				uni.showToast({
					title: '上传失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		}
	})
}

// 删除照片
const handleDeletePhoto = async (id: string) => {
	try {
		uni.showModal({
			title: '提示',
			content: '确定要删除这张照片吗？',
			success: async (res) => {
				if(res.confirm) {
					try {
						const result = await deletePhoto(id)
						
						if(result.code === 1) {
							await getWorkPhoto()
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						} else {
							uni.showToast({
								title: result.msg || '删除失败',
								icon: 'none'
							})
						}
					} catch(e) {
						console.error('Delete failed:', e)
						uni.showToast({
							title: '删除失败',
							icon: 'none'
						})
					}
				}
			}
		})
	} catch(e) {
		console.error('Delete failed:', e)
		uni.showToast({
			title: '删除失败', 
			icon: 'none'
		})
	}
}

onLoad(() => {
	getWorkPhoto()
})
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.area{
		margin-top: 45rpx;
		padding: 53rpx;
		width: 690rpx;
		height: 750rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding:0 40rpx 40rpx 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow-y: auto;
		.witem{
			width: 25%;
		}
		.delete{
			margin-top: 24rpx;
			width: 120rpx;
			height: 46rpx;
			border-radius: 23rpx;
			border: 1rpx solid #ED2134;
			font-size: 24rpx;
			color: #ED2134;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.button1{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
	.button2{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #4A87F8, #58CFFD);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(48,139,227,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
	.status {
		margin-top: 12rpx;
		font-size: 24rpx;
		color: #333;
		
		&.wait {
			color: #FF9900;
		}
		
		&.reject {
			color: #ED2134; 
		}
	}
}
</style>