import { useStore } from '@/until/mainpinia';
interface LinkData {
    navtype?: number;
    app_navtype?: number;
    appid?: string;
    [key: string]: string | number | boolean | undefined;
	is_auth?:string | number;
	is_menu?:string | number;
}
export const goLink = (url: string, data?: LinkData) => {
    if (!url) {
		if(data?.is_menu){
			uni.showModal({
				title:'敬请期待',
				showCancel:false
			});
		}
		return 
	}
    
    // 创建一个新的数据对象，避免直接修改传入的参数
    let finalData: LinkData = data ? { ...data } : {};
    
    // 处理URL中的查询参数
    if (url.includes('?')) {
        try {
            const [baseUrl, queryString] = url.split('?');
            url = baseUrl;
            
            // 解析查询字符串时不进行解码
            const params = queryString.split('&');
            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key && value) {
                    // 直接赋值，不进行解码
                    finalData[key] = value;
                }
            });
        } catch (error) {
            console.error('URL解析错误:', error);
            return;
        }
    }

    let navtype = 1; // 设置默认值为1
    // #ifdef APP-PLUS
    navtype = finalData?.app_navtype || finalData?.navtype || 1;
    // #endif
    // #ifndef APP-PLUS
    navtype = finalData?.navtype || 1;
    // #endif
    
    const queryParams = finalData ? flattenObjectToURLParams(finalData) : '';

	const next = ()=>{
		switch (navtype) {
		    case 1:
		        uni.navigateTo({
		            url: `${url}${queryParams ? '?' + queryParams : ''}`,
		            fail: () => {
		                uni.switchTab({ url });
		            }
		        });
		        break;
		    case 2:
				// #ifdef APP-PLUS
					plus.share.getServices(
						res => {
							let sweixin = null;
							for (let i in res) {
								if (res[i].id == 'weixin') {
									sweixin = res[i];
								}
							}
							//唤醒微信小程序
							if (sweixin) {
								sweixin.launchMiniProgram({
									id: 'gh_0febc35e3641',
									type: 0,
									path: 'pages/index/index'
								});
							} else {
								uni.showModal({
									title: '跳转失败',
									content: '您的手机上未安装微信',
									showCancel:false,
								});
							}
						}
					);
				// #endif
				// #ifndef APP-PLUS
				uni.navigateToMiniProgram({
				    appId: finalData?.appid || '',
				    url: url
				});
				// #endif
		        break;
		    case 3:
		        const finalUrl = `${url}${queryParams ? '?' + queryParams : ''}`;
		        const finalUrl2 = finalUrl.replace('/index','/index2')
		        // #ifdef APP-PLUS
		        uni.navigateTo({ url: finalUrl2 });
		        // #endif
		        // #ifndef APP-PLUS
		        uni.navigateTo({ url: finalUrl });
		        // #endif
		        break;
		    case 4:
		        uni.showModal({
		            content: '敬请期待',
		            showCancel: false
		        });
		        break;
		    default:
		        break;
		}
	}
	const checkPhoneList = [
		'/pages/resume/index',
		'/pages/nannyVideo/index',
		'/pages/classroomVideo/index'
		// '/pages/train/index',
		// '/pages/order/index'
	]
	
	
	if(finalData?.is_auth==1){
		checkPhone(()=>{
			next()
		})
	}else{
		next()
	}
}

export const checkPhone = (fn?:Function)=>{
	const store = useStore()
	// #ifdef MP-WEIXIN
	if(!store.userInfo.phone){
		uni.showModal({
			title:'提示',
			content:'微信授权您的联系方式，将为您提供更贴心的服务',
			success: function (res) {
				let routeList = getCurrentPages()
				let route = '/' + routeList[routeList.length-1].route
				if (res.confirm) {
					uni.navigateTo({
						url:'/pages/login/phone',
						success: function(res) {
						  // 通过eventChannel向被打开页面传送数据
						  res.eventChannel.emit('frompath', { data: route })
						}
					})
				} else if (res.cancel) {
					if(fn){
						fn()
					}
				}
			}
		})
	}else{
		if(fn){
			fn()
		}
	}
	// #endif
	// #ifdef APP-PLUS
	if(fn){
		fn()
	}
	// #endif

}

export const goPage = (list : { link : string }[], index : number) => {
	goLink(list[index].link);
};

export function flattenObjectToURLParams(obj: Record<string, any>): string {
    const params: string[] = [];

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            // 直接使用原始值，不进行编码
            params.push(`${key}=${value}`);
        }
    }

    return params.join('&');
}