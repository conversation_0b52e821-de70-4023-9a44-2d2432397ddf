<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar label="皖嫂一家亲" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
					color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goLink(bannerList[i].link)"></tm-carousel>
			</view>
			<view class="button_area">
				<view class="button-list">
					<view class="item" :class="menu?.length%4=== 0 ? 'item2' : ''" @click="goLink(item.link,{is_menu:1})"
						v-for="(item,index) in menu" :key="index">
						<tm-image class="rounded overflow" :width="90" :height="90" :src="item.src"></tm-image>
						<tm-text _class="nowrap mt-10" :font-size="24" color="#333" :label="item.title"></tm-text>
					</view>
				</view>
			</view>
			<view class="mt-n12">
				<tm-carousel autoplay :round="0" :width="721" :height="197" :indicatorDots="false" imgmodel="widthFix"
					color="#FCD9DB" :list="adList" rangKey="src" @click="item=>goLink(item.url||'/pages/news/index?type=1')"></tm-carousel>
			</view>
			<view class="qddt" @click="goLink(bookingHall.link)" v-if="bookingHall?.list.length>0">
				<image class="bg-image" src="/static/img/area_bg1.png" mode="aspectFill" />
				<view class="content">
					<view class="stit">
						<view class="title_wrap">
							<text class="title">{{bookingHall.title}}</text>
							<text class="subtitle">好单分享，推荐上工也有奖励哦！</text>
						</view>
					</view>
					<view class="card" v-for="item in bookingHall.list" :key="item.id">
						<view class="top">
							<text class="name">{{item.name}}</text>
							<text class="location" v-if="item.area">{{item.area}}</text>
						</view>
						<view class="bottom">
							<view class="left">
								<text class="desc" v-if="item.qddt">需求类型：{{ item.qddt }}</text>
								<text class="desc" v-if="item.zjStr&&!item.qddt">上工时段：{{item.zjStr}}</text>
								<text class="desc" v-if="item.service&&!item.qddt">需求类型：{{item.service}}</text>
							</view>
							<text class="button">我要接单</text>
						</view>
					</view>
				</view>
			</view>
			<view class="area2" v-if="memberInfo?.switch">
				<view class="stit2">我的简历和档期</view>
				<view class="resume_card" v-for="(item, index) in memberInfo.resume_btn" :key="item.title" @click="goLink(item.url)">
					<image class="bg-image" :src="item.bgImg" mode="aspectFill" />
					<view class="card_content">
						<view class="p1">{{item.title}}</view>
						<view class="p2">{{item.desc}}</view>
					</view>
				</view>
			</view>
			<view class="area3" v-if="memberInfo?.switch">
				<view class="charts">
					<view class="list">
						<view class="line" v-for="(item,index) in memberInfo.schedule" :key="index">
							<view class="bar">
								<view class="container">
									<tm-progress :width="304" :height="22" color="#fb243c"
										v-model:percent="item.percent"></tm-progress>
								</view>
							</view>
							<text class="month">{{item.month}}月</text>
							<text class="day">{{item.days}}天</text>
						</view>
					</view>
					<text class="tip">注意：标红的为该月安排的天数</text>
				</view>
			</view>
			<view class="button1" v-if="memberInfo?.switch" @click="goLink(memberInfo?.schedule_btn?.url)">{{memberInfo?.schedule_btn?.title}}
			</view>
			<ShareTreasure 
				:list="newsList" 
				:subtitle="subtitle"
				:current-tab="currentTab"
				:loading="loading"
				:has-more="hasMore"
				@tab-change="handleTabChange"
				@load-more="goLink('/pages/news/index?type='+currentTab)"
				@detail="handleDetail"
				v-if="newsListSwitch"
			/>
			<tabber></tabber>
		</view>

	</tm-app>
</template>
<script lang="ts" setup>
	import { ref, computed, watch, getCurrentInstance, onMounted } from "vue"
	import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
	import tabber from '@/components/tabber/tabber.vue'
	import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
	import { snb } from '@/components/customNavigationBar/snb'
	import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
	import { useStore } from '@/until/mainpinia';
	import { throttle } from '@/until/common'
	import { share } from '@/tmui/tool/lib/share'
	import { goLink } from '@/until/index'
	import * as api from '@/api/index.js'
	import ShareTreasure from '@/components/ShareTreasure/ShareTreasure.vue'
	import { useNewsList } from '@/composables/useNewsList'
	//分享功能
	const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
	const tmstore = useTmpiniaStore();
	onShareAppMessage();
	onShareTimeline()
	onLoad(() => {
		setTimeout(() => {
			setShareApp(tmstore.tmStore.wxshareConfig_miniMp)
			setShareTime(tmstore.tmStore.wxshareConfig_miniMp)
		}, 500)
	})
	const { NavigationBarTitle } = snb()
	console.log(NavigationBarTitle);
	
	// 页面数据
	const store = useStore()
	const scrollTop = ref(0)
	onPageScroll((e) => {
		scrollTop.value = e.scrollTop
	})
	const bannerList = computed(() => store.setting.bannerList)
	const adList = computed(() => store.setting.adList)
	const menu = computed(() => store.setting.btnList)
	const bookingHall = computed(() => store.setting.bookingHall)
	const newsListSwitch = computed(() => store.setting?.newsList?.switch)
	const memberInfo = computed(() => {
		const info = store.setting.memberInfo
		if (!info?.resume_btn) return info
		
		return {
			...info,
			resume_btn: info.resume_btn.map(item => ({
				...item,
				desc: item.desc?.replace(/\\n/g, '\n')
			}))
		}
	})

	// 使用新闻列表composable
	const { 
		newsList,
		subtitle,
		currentTab,
		loading,
		hasMore,
		getNewsList,
		handleTabChange,
		loadMore,
		handleDetail
	} = useNewsList()

	// 初始化
	onMounted(() => {
		getNewsList()
	})
</script>
<style lang="less" scoped>
	.main {
		width: 100%;
		overflow-x: hidden;
		padding-bottom: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}


	.button_area {
		margin: 300rpx auto 0;
		position: relative;
		z-index: 2;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 20rpx 20rpx 20rpx 0rpx #EEF0F2;
		border-radius: 20rpx 26rpx 26rpx 20rpx;
		padding-bottom: 40rpx;

		.button-list {
			display: flex;
			flex-direction: row;
			// justify-content: center;
			flex-wrap: wrap;
			padding: 0 24rpx;

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;
				margin: 38rpx 0 0;
				width: 20%;
				position: relative;

				.text {
					margin-top: 18rpx;
				}
			}
			.item2{
				width: 25%;
			}
		}
	}



	.area2 {
		width: 688rpx;
		margin: 50rpx auto 0;

		.stit2 {
			width: 100%;
			position: relative;
			display: flex;
			align-items: center;
			padding: 30rpx 30rpx;
			font-weight: bold;
			font-size: 32rpx;
			color: #333333;

			&::before {
				content: '';
				width: 8rpx;
				height: 30rpx;
				background: linear-gradient(-31deg, #FF6136, #F31630);
				border-radius: 4rpx;
				margin-right: 18rpx;
			}
		}

	}

	.area3 {
		margin: 50rpx auto 0;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227, 227, 227, 0.87);
		border-radius: 26rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 40rpx 0;

		.charts {
			width: 635rpx;
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;

			.tip {
				margin-top: 20rpx;
				font-size: 22rpx;
				color: #a0a0a0;
			}

			.list {
				width: 100%;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: space-between;

				.line {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 40rpx;

					.bar {
						height: 304rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;

						.container {
							width: 304rpx;
							transform: rotate(270deg);
						}
					}

					.day {
						margin-top: 6rpx;
						font-size: 22rpx;
						line-height: 30rpx;
						font-weight: 400;
						color: #888481;
						white-space: nowrap;
					}

					.month {
						margin-top: 6rpx;
						line-height: 30rpx;
						font-size: 22rpx;
						color: #6B6363;
						white-space: nowrap;
					}
				}
			}
		}
	}

	.button1 {
		margin: 20rpx auto ;
		width: 690rpx;
		height: 80rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
		border-radius: 40rpx;
		font-weight: bold;
		font-size: 30rpx;
		color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
	}

</style>