.blur {
	-webkit-backdrop-filter: blur(5px);
	backdrop-filter: blur(5px);
	background-color: rgba(0, 0, 0, 0.3);
}

.overflow {
	overflow: hidden;
}

.overflow-x {
	overflow-x: hidden;
	overflow-y: auto;
}

.overflow-y {
	overflow-x: auto;
	overflow-y: hidden;
}

.relative {
	position: relative !important;
}

.absolute {
	position: absolute !important;
}

.fixed {
	position: fixed !important;
}

.sticky {
	position: sticky !important;
}

.fulled-height {
	display: flex;
	align-items: stretch;
}

.clear {
	clear: both;
}

.fulled {
	width: 100%;
	display: block;
}

.fulled-height {
	height: 100%;
	display: block;
}

.gray-100 {
	filter: grayscale(100%);
}

.gray {
	filter: grayscale(25%);
}

.d-inline-block {
	display: inline-block !important;
}

.d-block {
	display: block;
}

.vertical-align-top {
	vertical-align: top;
}

.vertical-align-middle {
	vertical-align: middle;
}

.vertical-align-bottom {
	vertical-align: bottom;
}

.wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.nowrap {
	white-space: nowrap;
}

.vertical-align-top {
	vertical-align: top;
}

.vertical-align-middle {
	vertical-align: middle;
}

.vertical-align-bottom {
	vertical-align: bottom;
}

.zIndex-0 {
	z-index: 0;
}

.zIndex-n0 {
	z-index: 0;
}

.zIndex-1 {
	z-index: 1;
}

.zIndex-n1 {
	z-index: 4;
}

.zIndex-2 {
	z-index: 2;
}

.zIndex-n2 {
	z-index: 8;
}

.zIndex-3 {
	z-index: 3;
}

.zIndex-n3 {
	z-index: 12;
}

.zIndex-4 {
	z-index: 4;
}

.zIndex-n4 {
	z-index: 16;
}

.zIndex-5 {
	z-index: 5;
}

.zIndex-n5 {
	z-index: 20;
}

.zIndex-6 {
	z-index: 6;
}

.zIndex-n6 {
	z-index: 24;
}

.zIndex-7 {
	z-index: 7;
}

.zIndex-n7 {
	z-index: 28;
}

.zIndex-8 {
	z-index: 8;
}

.zIndex-n8 {
	z-index: 32;
}

.zIndex-9 {
	z-index: 9;
}

.zIndex-n9 {
	z-index: 36;
}

.zIndex-10 {
	z-index: 10;
}

.zIndex-n10 {
	z-index: 40;
}

.zIndex-11 {
	z-index: 11;
}

.zIndex-n11 {
	z-index: 44;
}

.zIndex-12 {
	z-index: 12;
}

.zIndex-n12 {
	z-index: 48;
}

.zIndex-13 {
	z-index: 13;
}

.zIndex-n13 {
	z-index: 52;
}

.zIndex-14 {
	z-index: 14;
}

.zIndex-n14 {
	z-index: 56;
}

.zIndex-15 {
	z-index: 15;
}

.zIndex-n15 {
	z-index: 60;
}

.zIndex-16 {
	z-index: 16;
}

.zIndex-n16 {
	z-index: 64;
}

.zIndex-17 {
	z-index: 17;
}

.zIndex-n17 {
	z-index: 68;
}

.zIndex-18 {
	z-index: 18;
}

.zIndex-n18 {
	z-index: 72;
}

.zIndex-19 {
	z-index: 19;
}

.zIndex-n19 {
	z-index: 76;
}

.zIndex-20 {
	z-index: 20;
}

.zIndex-n20 {
	z-index: 80;
}

.zIndex-21 {
	z-index: 21;
}

.zIndex-n21 {
	z-index: 84;
}

.zIndex-22 {
	z-index: 22;
}

.zIndex-n22 {
	z-index: 88;
}

.zIndex-23 {
	z-index: 23;
}

.zIndex-n23 {
	z-index: 92;
}

.zIndex-24 {
	z-index: 24;
}

.zIndex-n24 {
	z-index: 96;
}

.zIndex-25 {
	z-index: 25;
}

.zIndex-n25 {
	z-index: 100;
}

.zIndex-26 {
	z-index: 26;
}

.zIndex-n26 {
	z-index: 104;
}

.text-overflow {
	width: 100%;
	display: block;
	white-space: nowrap;
	overflow: hidden;
}

.text-overflow-1 {
	width: 100%;
	display: -webkit-box;
	white-space: inherit;
	overflow: hidden;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.text-overflow-2 {
	width: 100%;
	display: -webkit-box;
	white-space: inherit;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.text-overflow-3 {
	width: 100%;
	display: -webkit-box;
	white-space: inherit;
	overflow: hidden;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

.text-overflow-4 {
	width: 100%;
	display: -webkit-box;
	white-space: inherit;
	overflow: hidden;
	-webkit-line-clamp: 4;
	-webkit-box-orient: vertical;
}

/* #ifdef APP-PLUS-NVUE */
.text-overflow {
	text-overflow: ellipsis;
}

.text-overflow-1 {
	text-overflow: ellipsis;
	lines: 1;
}

.text-overflow-2 {
	text-overflow: ellipsis;
	lines: 2;
}

.text-overflow-3 {
	text-overflow: ellipsis;
	lines: 3;
}

.text-overflow-4 {
	text-overflow: ellipsis;
	lines: 4;
}

.wrap {
	word-wrap: break-word;
}

.text-delete {
	text-decoration: line-through;
}

.text-underline {
	text-decoration: underline;
}

/* #ifdef H5*/
.text-size-xxs {
	font-size: 12px;
}
.text-size-xxs span {
	font-size: 12px;
}

/* #ifndef H5*/
.text-size-xxs {
	font-size: 12px;
}

/* #ifdef H5*/
.text-size-xs {
	font-size: 13px;
}
.text-size-xs span {
	font-size: 13px;
}

/* #ifndef H5*/
.text-size-xs {
	font-size: 13px;
}

/* #ifdef H5*/
.text-size-s {
	font-size: 13px;
}
.text-size-s span {
	font-size: 13px;
}

/* #ifndef H5*/
.text-size-s {
	font-size: 13px;
}

/* #ifdef H5*/
.text-size-m {
	font-size: 14px;
}
.text-size-m span {
	font-size: 14px;
}

/* #ifndef H5*/
.text-size-m {
	font-size: 14px;
}

/* #ifdef H5*/
.text-size-n {
	font-size: 16px;
}
.text-size-n span {
	font-size: 16px;
}

/* #ifndef H5*/
.text-size-n {
	font-size: 16px;
}

/* #ifdef H5*/
.text-size-g {
	font-size: 18px;
}
.text-size-g span {
	font-size: 18px;
}

/* #ifndef H5*/
.text-size-g {
	font-size: 18px;
}

/* #ifdef H5*/
.text-size-lg {
	font-size: 20px;
}
.text-size-lg span {
	font-size: 20px;
}

/* #ifndef H5*/
.text-size-lg {
	font-size: 20px;
}

/* #ifdef H5*/
.text-size-xl {
	font-size: 24px;
}
.text-size-xl span {
	font-size: 24px;
}

/* #ifndef H5*/
.text-size-xl {
	font-size: 24px;
}

.text-weight-s {
	font-weight: 100;
}

.text-weight-n {
	font-weight: 400;
}

.text-weight-b {
	font-weight: 700;
}

.text-align-left {
	text-align: left;
}

.text-align-right {
	text-align: right;
}

.text-align-center {
	text-align: center;
}

.round-0 {
	border-radius: 0px !important;
}

.round-1 {
	border-radius: 1px !important;
}

.round-2 {
	border-radius: 2px !important;
}

.round-3 {
	border-radius: 3px !important;
}

.round-4 {
	border-radius: 4px !important;
}

.round-5 {
	border-radius: 5px !important;
}

.round-6 {
	border-radius: 6px !important;
}

.round-7 {
	border-radius: 7px !important;
}

.round-8 {
	border-radius: 8px !important;
}

.round-9 {
	border-radius: 9px !important;
}

.round-10 {
	border-radius: 10px !important;
}

.round-11 {
	border-radius: 11px !important;
}

.round-12 {
	border-radius: 12px !important;
}

.round-13 {
	border-radius: 13px !important;
}

.round-14 {
	border-radius: 14px !important;
}

.round-15 {
	border-radius: 15px !important;
}

.round-16 {
	border-radius: 16px !important;
}

.round-17 {
	border-radius: 17px !important;
}

.round-18 {
	border-radius: 18px !important;
}

.round-19 {
	border-radius: 19px !important;
}

.round-20 {
	border-radius: 20px !important;
}

.round-21 {
	border-radius: 21px !important;
}

.round-22 {
	border-radius: 22px !important;
}

.round-23 {
	border-radius: 23px !important;
}

.round-24 {
	border-radius: 24px !important;
}

.round-25 {
	border-radius: 25px !important;
}

.round-26 {
	border-radius: 26px !important;
}

.round-tl-0 {
	border-top-left-radius: 0px !important;
}

.round-tl-1 {
	border-top-left-radius: 1px !important;
}

.round-tl-2 {
	border-top-left-radius: 2px !important;
}

.round-tl-3 {
	border-top-left-radius: 3px !important;
}

.round-tl-4 {
	border-top-left-radius: 4px !important;
}

.round-tl-5 {
	border-top-left-radius: 5px !important;
}

.round-tl-6 {
	border-top-left-radius: 6px !important;
}

.round-tl-7 {
	border-top-left-radius: 7px !important;
}

.round-tl-8 {
	border-top-left-radius: 8px !important;
}

.round-tl-9 {
	border-top-left-radius: 9px !important;
}

.round-tl-10 {
	border-top-left-radius: 10px !important;
}

.round-tl-11 {
	border-top-left-radius: 11px !important;
}

.round-tl-12 {
	border-top-left-radius: 12px !important;
}

.round-tl-13 {
	border-top-left-radius: 13px !important;
}

.round-tl-14 {
	border-top-left-radius: 14px !important;
}

.round-tl-15 {
	border-top-left-radius: 15px !important;
}

.round-tl-16 {
	border-top-left-radius: 16px !important;
}

.round-tl-17 {
	border-top-left-radius: 17px !important;
}

.round-tl-18 {
	border-top-left-radius: 18px !important;
}

.round-tl-19 {
	border-top-left-radius: 19px !important;
}

.round-tl-20 {
	border-top-left-radius: 20px !important;
}

.round-tl-21 {
	border-top-left-radius: 21px !important;
}

.round-tl-22 {
	border-top-left-radius: 22px !important;
}

.round-tl-23 {
	border-top-left-radius: 23px !important;
}

.round-tl-24 {
	border-top-left-radius: 24px !important;
}

.round-tl-25 {
	border-top-left-radius: 25px !important;
}

.round-tl-26 {
	border-top-left-radius: 26px !important;
}

.round-tl-27 {
	border-top-left-radius: 27px !important;
}

.round-tl-28 {
	border-top-left-radius: 28px !important;
}

.round-tl-29 {
	border-top-left-radius: 29px !important;
}

.round-tl-30 {
	border-top-left-radius: 30px !important;
}

.round-tl-31 {
	border-top-left-radius: 31px !important;
}

.round-tl-32 {
	border-top-left-radius: 32px !important;
}

.round-tl-33 {
	border-top-left-radius: 33px !important;
}

.round-tl-34 {
	border-top-left-radius: 34px !important;
}

.round-tl-35 {
	border-top-left-radius: 35px !important;
}

.round-tl-36 {
	border-top-left-radius: 36px !important;
}

.round-tl-37 {
	border-top-left-radius: 37px !important;
}

.round-tl-38 {
	border-top-left-radius: 38px !important;
}

.round-tl-39 {
	border-top-left-radius: 39px !important;
}

.round-tl-40 {
	border-top-left-radius: 40px !important;
}

.round-tl-41 {
	border-top-left-radius: 41px !important;
}

.round-tl-42 {
	border-top-left-radius: 42px !important;
}

.round-tl-43 {
	border-top-left-radius: 43px !important;
}

.round-tl-44 {
	border-top-left-radius: 44px !important;
}

.round-tl-45 {
	border-top-left-radius: 45px !important;
}

.round-tl-46 {
	border-top-left-radius: 46px !important;
}

.round-tl-47 {
	border-top-left-radius: 47px !important;
}

.round-tl-48 {
	border-top-left-radius: 48px !important;
}

.round-tl-49 {
	border-top-left-radius: 49px !important;
}

.round-tr-0 {
	border-top-right-radius: 0px !important;
}

.round-tr-1 {
	border-top-right-radius: 1px !important;
}

.round-tr-2 {
	border-top-right-radius: 2px !important;
}

.round-tr-3 {
	border-top-right-radius: 3px !important;
}

.round-tr-4 {
	border-top-right-radius: 4px !important;
}

.round-tr-5 {
	border-top-right-radius: 5px !important;
}

.round-tr-6 {
	border-top-right-radius: 6px !important;
}

.round-tr-7 {
	border-top-right-radius: 7px !important;
}

.round-tr-8 {
	border-top-right-radius: 8px !important;
}

.round-tr-9 {
	border-top-right-radius: 9px !important;
}

.round-tr-10 {
	border-top-right-radius: 10px !important;
}

.round-tr-11 {
	border-top-right-radius: 11px !important;
}

.round-tr-12 {
	border-top-right-radius: 12px !important;
}

.round-tr-13 {
	border-top-right-radius: 13px !important;
}

.round-tr-14 {
	border-top-right-radius: 14px !important;
}

.round-tr-15 {
	border-top-right-radius: 15px !important;
}

.round-tr-16 {
	border-top-right-radius: 16px !important;
}

.round-tr-17 {
	border-top-right-radius: 17px !important;
}

.round-tr-18 {
	border-top-right-radius: 18px !important;
}

.round-tr-19 {
	border-top-right-radius: 19px !important;
}

.round-tr-20 {
	border-top-right-radius: 20px !important;
}

.round-tr-21 {
	border-top-right-radius: 21px !important;
}

.round-tr-22 {
	border-top-right-radius: 22px !important;
}

.round-tr-23 {
	border-top-right-radius: 23px !important;
}

.round-tr-24 {
	border-top-right-radius: 24px !important;
}

.round-tr-25 {
	border-top-right-radius: 25px !important;
}

.round-tr-26 {
	border-top-right-radius: 26px !important;
}

.round-tr-27 {
	border-top-right-radius: 27px !important;
}

.round-tr-28 {
	border-top-right-radius: 28px !important;
}

.round-tr-29 {
	border-top-right-radius: 29px !important;
}

.round-tr-30 {
	border-top-right-radius: 30px !important;
}

.round-tr-31 {
	border-top-right-radius: 31px !important;
}

.round-tr-32 {
	border-top-right-radius: 32px !important;
}

.round-tr-33 {
	border-top-right-radius: 33px !important;
}

.round-tr-34 {
	border-top-right-radius: 34px !important;
}

.round-tr-35 {
	border-top-right-radius: 35px !important;
}

.round-tr-36 {
	border-top-right-radius: 36px !important;
}

.round-tr-37 {
	border-top-right-radius: 37px !important;
}

.round-tr-38 {
	border-top-right-radius: 38px !important;
}

.round-tr-39 {
	border-top-right-radius: 39px !important;
}

.round-tr-40 {
	border-top-right-radius: 40px !important;
}

.round-tr-41 {
	border-top-right-radius: 41px !important;
}

.round-tr-42 {
	border-top-right-radius: 42px !important;
}

.round-tr-43 {
	border-top-right-radius: 43px !important;
}

.round-tr-44 {
	border-top-right-radius: 44px !important;
}

.round-tr-45 {
	border-top-right-radius: 45px !important;
}

.round-tr-46 {
	border-top-right-radius: 46px !important;
}

.round-tr-47 {
	border-top-right-radius: 47px !important;
}

.round-tr-48 {
	border-top-right-radius: 48px !important;
}

.round-tr-49 {
	border-top-right-radius: 49px !important;
}

.round-bl-0 {
	border-bottom-left-radius: 0px !important;
}

.round-bl-1 {
	border-bottom-left-radius: 1px !important;
}

.round-bl-2 {
	border-bottom-left-radius: 2px !important;
}

.round-bl-3 {
	border-bottom-left-radius: 3px !important;
}

.round-bl-4 {
	border-bottom-left-radius: 4px !important;
}

.round-bl-5 {
	border-bottom-left-radius: 5px !important;
}

.round-bl-6 {
	border-bottom-left-radius: 6px !important;
}

.round-bl-7 {
	border-bottom-left-radius: 7px !important;
}

.round-bl-8 {
	border-bottom-left-radius: 8px !important;
}

.round-bl-9 {
	border-bottom-left-radius: 9px !important;
}

.round-bl-10 {
	border-bottom-left-radius: 10px !important;
}

.round-bl-11 {
	border-bottom-left-radius: 11px !important;
}

.round-bl-12 {
	border-bottom-left-radius: 12px !important;
}

.round-bl-13 {
	border-bottom-left-radius: 13px !important;
}

.round-bl-14 {
	border-bottom-left-radius: 14px !important;
}

.round-bl-15 {
	border-bottom-left-radius: 15px !important;
}

.round-bl-16 {
	border-bottom-left-radius: 16px !important;
}

.round-bl-17 {
	border-bottom-left-radius: 17px !important;
}

.round-bl-18 {
	border-bottom-left-radius: 18px !important;
}

.round-bl-19 {
	border-bottom-left-radius: 19px !important;
}

.round-bl-20 {
	border-bottom-left-radius: 20px !important;
}

.round-bl-21 {
	border-bottom-left-radius: 21px !important;
}

.round-bl-22 {
	border-bottom-left-radius: 22px !important;
}

.round-bl-23 {
	border-bottom-left-radius: 23px !important;
}

.round-bl-24 {
	border-bottom-left-radius: 24px !important;
}

.round-bl-25 {
	border-bottom-left-radius: 25px !important;
}

.round-bl-26 {
	border-bottom-left-radius: 26px !important;
}

.round-bl-27 {
	border-bottom-left-radius: 27px !important;
}

.round-bl-28 {
	border-bottom-left-radius: 28px !important;
}

.round-bl-29 {
	border-bottom-left-radius: 29px !important;
}

.round-bl-30 {
	border-bottom-left-radius: 30px !important;
}

.round-bl-31 {
	border-bottom-left-radius: 31px !important;
}

.round-bl-32 {
	border-bottom-left-radius: 32px !important;
}

.round-bl-33 {
	border-bottom-left-radius: 33px !important;
}

.round-bl-34 {
	border-bottom-left-radius: 34px !important;
}

.round-bl-35 {
	border-bottom-left-radius: 35px !important;
}

.round-bl-36 {
	border-bottom-left-radius: 36px !important;
}

.round-bl-37 {
	border-bottom-left-radius: 37px !important;
}

.round-bl-38 {
	border-bottom-left-radius: 38px !important;
}

.round-bl-39 {
	border-bottom-left-radius: 39px !important;
}

.round-bl-40 {
	border-bottom-left-radius: 40px !important;
}

.round-bl-41 {
	border-bottom-left-radius: 41px !important;
}

.round-bl-42 {
	border-bottom-left-radius: 42px !important;
}

.round-bl-43 {
	border-bottom-left-radius: 43px !important;
}

.round-bl-44 {
	border-bottom-left-radius: 44px !important;
}

.round-bl-45 {
	border-bottom-left-radius: 45px !important;
}

.round-bl-46 {
	border-bottom-left-radius: 46px !important;
}

.round-bl-47 {
	border-bottom-left-radius: 47px !important;
}

.round-bl-48 {
	border-bottom-left-radius: 48px !important;
}

.round-bl-49 {
	border-bottom-left-radius: 49px !important;
}

.round-br-0 {
	border-bottom-right-radius: 0px !important;
}

.round-br-1 {
	border-bottom-right-radius: 1px !important;
}

.round-br-2 {
	border-bottom-right-radius: 2px !important;
}

.round-br-3 {
	border-bottom-right-radius: 3px !important;
}

.round-br-4 {
	border-bottom-right-radius: 4px !important;
}

.round-br-5 {
	border-bottom-right-radius: 5px !important;
}

.round-br-6 {
	border-bottom-right-radius: 6px !important;
}

.round-br-7 {
	border-bottom-right-radius: 7px !important;
}

.round-br-8 {
	border-bottom-right-radius: 8px !important;
}

.round-br-9 {
	border-bottom-right-radius: 9px !important;
}

.round-br-10 {
	border-bottom-right-radius: 10px !important;
}

.round-br-11 {
	border-bottom-right-radius: 11px !important;
}

.round-br-12 {
	border-bottom-right-radius: 12px !important;
}

.round-br-13 {
	border-bottom-right-radius: 13px !important;
}

.round-br-14 {
	border-bottom-right-radius: 14px !important;
}

.round-br-15 {
	border-bottom-right-radius: 15px !important;
}

.round-br-16 {
	border-bottom-right-radius: 16px !important;
}

.round-br-17 {
	border-bottom-right-radius: 17px !important;
}

.round-br-18 {
	border-bottom-right-radius: 18px !important;
}

.round-br-19 {
	border-bottom-right-radius: 19px !important;
}

.round-br-20 {
	border-bottom-right-radius: 20px !important;
}

.round-br-21 {
	border-bottom-right-radius: 21px !important;
}

.round-br-22 {
	border-bottom-right-radius: 22px !important;
}

.round-br-23 {
	border-bottom-right-radius: 23px !important;
}

.round-br-24 {
	border-bottom-right-radius: 24px !important;
}

.round-br-25 {
	border-bottom-right-radius: 25px !important;
}

.round-br-26 {
	border-bottom-right-radius: 26px !important;
}

.round-br-27 {
	border-bottom-right-radius: 27px !important;
}

.round-br-28 {
	border-bottom-right-radius: 28px !important;
}

.round-br-29 {
	border-bottom-right-radius: 29px !important;
}

.round-br-30 {
	border-bottom-right-radius: 30px !important;
}

.round-br-31 {
	border-bottom-right-radius: 31px !important;
}

.round-br-32 {
	border-bottom-right-radius: 32px !important;
}

.round-br-33 {
	border-bottom-right-radius: 33px !important;
}

.round-br-34 {
	border-bottom-right-radius: 34px !important;
}

.round-br-35 {
	border-bottom-right-radius: 35px !important;
}

.round-br-36 {
	border-bottom-right-radius: 36px !important;
}

.round-br-37 {
	border-bottom-right-radius: 37px !important;
}

.round-br-38 {
	border-bottom-right-radius: 38px !important;
}

.round-br-39 {
	border-bottom-right-radius: 39px !important;
}

.round-br-40 {
	border-bottom-right-radius: 40px !important;
}

.round-br-41 {
	border-bottom-right-radius: 41px !important;
}

.round-br-42 {
	border-bottom-right-radius: 42px !important;
}

.round-br-43 {
	border-bottom-right-radius: 43px !important;
}

.round-br-44 {
	border-bottom-right-radius: 44px !important;
}

.round-br-45 {
	border-bottom-right-radius: 45px !important;
}

.round-br-46 {
	border-bottom-right-radius: 46px !important;
}

.round-br-47 {
	border-bottom-right-radius: 47px !important;
}

.round-br-48 {
	border-bottom-right-radius: 48px !important;
}

.round-br-49 {
	border-bottom-right-radius: 49px !important;
}

.round-a-0 {
	border-radius: 0px !important;
}

.round-a-1 {
	border-radius: 1px !important;
}

.round-a-2 {
	border-radius: 2px !important;
}

.round-a-3 {
	border-radius: 3px !important;
}

.round-a-4 {
	border-radius: 4px !important;
}

.round-a-5 {
	border-radius: 5px !important;
}

.round-a-6 {
	border-radius: 6px !important;
}

.round-a-7 {
	border-radius: 7px !important;
}

.round-a-8 {
	border-radius: 8px !important;
}

.round-a-9 {
	border-radius: 9px !important;
}

.round-a-10 {
	border-radius: 10px !important;
}

.round-a-11 {
	border-radius: 11px !important;
}

.round-a-12 {
	border-radius: 12px !important;
}

.round-a-13 {
	border-radius: 13px !important;
}

.round-a-14 {
	border-radius: 14px !important;
}

.round-a-15 {
	border-radius: 15px !important;
}

.round-a-16 {
	border-radius: 16px !important;
}

.round-a-17 {
	border-radius: 17px !important;
}

.round-a-18 {
	border-radius: 18px !important;
}

.round-a-19 {
	border-radius: 19px !important;
}

.round-a-20 {
	border-radius: 20px !important;
}

.round-a-21 {
	border-radius: 21px !important;
}

.round-a-22 {
	border-radius: 22px !important;
}

.round-a-23 {
	border-radius: 23px !important;
}

.round-a-24 {
	border-radius: 24px !important;
}

.round-a-25 {
	border-radius: 25px !important;
}

.round-a-26 {
	border-radius: 26px !important;
}

.round-a-27 {
	border-radius: 27px !important;
}

.round-a-28 {
	border-radius: 28px !important;
}

.round-a-29 {
	border-radius: 29px !important;
}

.round-a-30 {
	border-radius: 30px !important;
}

.round-a-31 {
	border-radius: 31px !important;
}

.round-a-32 {
	border-radius: 32px !important;
}

.round-a-33 {
	border-radius: 33px !important;
}

.round-a-34 {
	border-radius: 34px !important;
}

.round-a-35 {
	border-radius: 35px !important;
}

.round-a-36 {
	border-radius: 36px !important;
}

.round-a-37 {
	border-radius: 37px !important;
}

.round-a-38 {
	border-radius: 38px !important;
}

.round-a-39 {
	border-radius: 39px !important;
}

.round-a-40 {
	border-radius: 40px !important;
}

.round-a-41 {
	border-radius: 41px !important;
}

.round-a-42 {
	border-radius: 42px !important;
}

.round-a-43 {
	border-radius: 43px !important;
}

.round-a-44 {
	border-radius: 44px !important;
}

.round-a-45 {
	border-radius: 45px !important;
}

.round-a-46 {
	border-radius: 46px !important;
}

.round-a-47 {
	border-radius: 47px !important;
}

.round-a-48 {
	border-radius: 48px !important;
}

.round-a-49 {
	border-radius: 49px !important;
}

.round-t-0 {
	border-top-left-radius: 0px !important;
	border-top-right-radius: 0px !important;
}

.round-t-1 {
	border-top-left-radius: 1px !important;
	border-top-right-radius: 1px !important;
}

.round-t-2 {
	border-top-left-radius: 2px !important;
	border-top-right-radius: 2px !important;
}

.round-t-3 {
	border-top-left-radius: 3px !important;
	border-top-right-radius: 3px !important;
}

.round-t-4 {
	border-top-left-radius: 4px !important;
	border-top-right-radius: 4px !important;
}

.round-t-5 {
	border-top-left-radius: 5px !important;
	border-top-right-radius: 5px !important;
}

.round-t-6 {
	border-top-left-radius: 6px !important;
	border-top-right-radius: 6px !important;
}

.round-t-7 {
	border-top-left-radius: 7px !important;
	border-top-right-radius: 7px !important;
}

.round-t-8 {
	border-top-left-radius: 8px !important;
	border-top-right-radius: 8px !important;
}

.round-t-9 {
	border-top-left-radius: 9px !important;
	border-top-right-radius: 9px !important;
}

.round-t-10 {
	border-top-left-radius: 10px !important;
	border-top-right-radius: 10px !important;
}

.round-t-11 {
	border-top-left-radius: 11px !important;
	border-top-right-radius: 11px !important;
}

.round-t-12 {
	border-top-left-radius: 12px !important;
	border-top-right-radius: 12px !important;
}

.round-t-13 {
	border-top-left-radius: 13px !important;
	border-top-right-radius: 13px !important;
}

.round-t-14 {
	border-top-left-radius: 14px !important;
	border-top-right-radius: 14px !important;
}

.round-t-15 {
	border-top-left-radius: 15px !important;
	border-top-right-radius: 15px !important;
}

.round-t-16 {
	border-top-left-radius: 16px !important;
	border-top-right-radius: 16px !important;
}

.round-t-17 {
	border-top-left-radius: 17px !important;
	border-top-right-radius: 17px !important;
}

.round-t-18 {
	border-top-left-radius: 18px !important;
	border-top-right-radius: 18px !important;
}

.round-t-19 {
	border-top-left-radius: 19px !important;
	border-top-right-radius: 19px !important;
}

.round-t-20 {
	border-top-left-radius: 20px !important;
	border-top-right-radius: 20px !important;
}

.round-t-21 {
	border-top-left-radius: 21px !important;
	border-top-right-radius: 21px !important;
}

.round-t-22 {
	border-top-left-radius: 22px !important;
	border-top-right-radius: 22px !important;
}

.round-t-23 {
	border-top-left-radius: 23px !important;
	border-top-right-radius: 23px !important;
}

.round-t-24 {
	border-top-left-radius: 24px !important;
	border-top-right-radius: 24px !important;
}

.round-t-25 {
	border-top-left-radius: 25px !important;
	border-top-right-radius: 25px !important;
}

.round-t-26 {
	border-top-left-radius: 26px !important;
	border-top-right-radius: 26px !important;
}

.round-t-27 {
	border-top-left-radius: 27px !important;
	border-top-right-radius: 27px !important;
}

.round-t-28 {
	border-top-left-radius: 28px !important;
	border-top-right-radius: 28px !important;
}

.round-t-29 {
	border-top-left-radius: 29px !important;
	border-top-right-radius: 29px !important;
}

.round-t-30 {
	border-top-left-radius: 30px !important;
	border-top-right-radius: 30px !important;
}

.round-t-31 {
	border-top-left-radius: 31px !important;
	border-top-right-radius: 31px !important;
}

.round-t-32 {
	border-top-left-radius: 32px !important;
	border-top-right-radius: 32px !important;
}

.round-t-33 {
	border-top-left-radius: 33px !important;
	border-top-right-radius: 33px !important;
}

.round-t-34 {
	border-top-left-radius: 34px !important;
	border-top-right-radius: 34px !important;
}

.round-t-35 {
	border-top-left-radius: 35px !important;
	border-top-right-radius: 35px !important;
}

.round-t-36 {
	border-top-left-radius: 36px !important;
	border-top-right-radius: 36px !important;
}

.round-t-37 {
	border-top-left-radius: 37px !important;
	border-top-right-radius: 37px !important;
}

.round-t-38 {
	border-top-left-radius: 38px !important;
	border-top-right-radius: 38px !important;
}

.round-t-39 {
	border-top-left-radius: 39px !important;
	border-top-right-radius: 39px !important;
}

.round-t-40 {
	border-top-left-radius: 40px !important;
	border-top-right-radius: 40px !important;
}

.round-t-41 {
	border-top-left-radius: 41px !important;
	border-top-right-radius: 41px !important;
}

.round-t-42 {
	border-top-left-radius: 42px !important;
	border-top-right-radius: 42px !important;
}

.round-t-43 {
	border-top-left-radius: 43px !important;
	border-top-right-radius: 43px !important;
}

.round-t-44 {
	border-top-left-radius: 44px !important;
	border-top-right-radius: 44px !important;
}

.round-t-45 {
	border-top-left-radius: 45px !important;
	border-top-right-radius: 45px !important;
}

.round-t-46 {
	border-top-left-radius: 46px !important;
	border-top-right-radius: 46px !important;
}

.round-t-47 {
	border-top-left-radius: 47px !important;
	border-top-right-radius: 47px !important;
}

.round-t-48 {
	border-top-left-radius: 48px !important;
	border-top-right-radius: 48px !important;
}

.round-t-49 {
	border-top-left-radius: 49px !important;
	border-top-right-radius: 49px !important;
}

.round-b-0 {
	border-bottom-left-radius: 0px !important;
	border-bottom-right-radius: 0px !important;
}

.round-b-1 {
	border-bottom-left-radius: 1px !important;
	border-bottom-right-radius: 1px !important;
}

.round-b-2 {
	border-bottom-left-radius: 2px !important;
	border-bottom-right-radius: 2px !important;
}

.round-b-3 {
	border-bottom-left-radius: 3px !important;
	border-bottom-right-radius: 3px !important;
}

.round-b-4 {
	border-bottom-left-radius: 4px !important;
	border-bottom-right-radius: 4px !important;
}

.round-b-5 {
	border-bottom-left-radius: 5px !important;
	border-bottom-right-radius: 5px !important;
}

.round-b-6 {
	border-bottom-left-radius: 6px !important;
	border-bottom-right-radius: 6px !important;
}

.round-b-7 {
	border-bottom-left-radius: 7px !important;
	border-bottom-right-radius: 7px !important;
}

.round-b-8 {
	border-bottom-left-radius: 8px !important;
	border-bottom-right-radius: 8px !important;
}

.round-b-9 {
	border-bottom-left-radius: 9px !important;
	border-bottom-right-radius: 9px !important;
}

.round-b-10 {
	border-bottom-left-radius: 10px !important;
	border-bottom-right-radius: 10px !important;
}

.round-b-11 {
	border-bottom-left-radius: 11px !important;
	border-bottom-right-radius: 11px !important;
}

.round-b-12 {
	border-bottom-left-radius: 12px !important;
	border-bottom-right-radius: 12px !important;
}

.round-b-13 {
	border-bottom-left-radius: 13px !important;
	border-bottom-right-radius: 13px !important;
}

.round-b-14 {
	border-bottom-left-radius: 14px !important;
	border-bottom-right-radius: 14px !important;
}

.round-b-15 {
	border-bottom-left-radius: 15px !important;
	border-bottom-right-radius: 15px !important;
}

.round-b-16 {
	border-bottom-left-radius: 16px !important;
	border-bottom-right-radius: 16px !important;
}

.round-b-17 {
	border-bottom-left-radius: 17px !important;
	border-bottom-right-radius: 17px !important;
}

.round-b-18 {
	border-bottom-left-radius: 18px !important;
	border-bottom-right-radius: 18px !important;
}

.round-b-19 {
	border-bottom-left-radius: 19px !important;
	border-bottom-right-radius: 19px !important;
}

.round-b-20 {
	border-bottom-left-radius: 20px !important;
	border-bottom-right-radius: 20px !important;
}

.round-b-21 {
	border-bottom-left-radius: 21px !important;
	border-bottom-right-radius: 21px !important;
}

.round-b-22 {
	border-bottom-left-radius: 22px !important;
	border-bottom-right-radius: 22px !important;
}

.round-b-23 {
	border-bottom-left-radius: 23px !important;
	border-bottom-right-radius: 23px !important;
}

.round-b-24 {
	border-bottom-left-radius: 24px !important;
	border-bottom-right-radius: 24px !important;
}

.round-b-25 {
	border-bottom-left-radius: 25px !important;
	border-bottom-right-radius: 25px !important;
}

.round-b-26 {
	border-bottom-left-radius: 26px !important;
	border-bottom-right-radius: 26px !important;
}

.round-b-27 {
	border-bottom-left-radius: 27px !important;
	border-bottom-right-radius: 27px !important;
}

.round-b-28 {
	border-bottom-left-radius: 28px !important;
	border-bottom-right-radius: 28px !important;
}

.round-b-29 {
	border-bottom-left-radius: 29px !important;
	border-bottom-right-radius: 29px !important;
}

.round-b-30 {
	border-bottom-left-radius: 30px !important;
	border-bottom-right-radius: 30px !important;
}

.round-b-31 {
	border-bottom-left-radius: 31px !important;
	border-bottom-right-radius: 31px !important;
}

.round-b-32 {
	border-bottom-left-radius: 32px !important;
	border-bottom-right-radius: 32px !important;
}

.round-b-33 {
	border-bottom-left-radius: 33px !important;
	border-bottom-right-radius: 33px !important;
}

.round-b-34 {
	border-bottom-left-radius: 34px !important;
	border-bottom-right-radius: 34px !important;
}

.round-b-35 {
	border-bottom-left-radius: 35px !important;
	border-bottom-right-radius: 35px !important;
}

.round-b-36 {
	border-bottom-left-radius: 36px !important;
	border-bottom-right-radius: 36px !important;
}

.round-b-37 {
	border-bottom-left-radius: 37px !important;
	border-bottom-right-radius: 37px !important;
}

.round-b-38 {
	border-bottom-left-radius: 38px !important;
	border-bottom-right-radius: 38px !important;
}

.round-b-39 {
	border-bottom-left-radius: 39px !important;
	border-bottom-right-radius: 39px !important;
}

.round-b-40 {
	border-bottom-left-radius: 40px !important;
	border-bottom-right-radius: 40px !important;
}

.round-b-41 {
	border-bottom-left-radius: 41px !important;
	border-bottom-right-radius: 41px !important;
}

.round-b-42 {
	border-bottom-left-radius: 42px !important;
	border-bottom-right-radius: 42px !important;
}

.round-b-43 {
	border-bottom-left-radius: 43px !important;
	border-bottom-right-radius: 43px !important;
}

.round-b-44 {
	border-bottom-left-radius: 44px !important;
	border-bottom-right-radius: 44px !important;
}

.round-b-45 {
	border-bottom-left-radius: 45px !important;
	border-bottom-right-radius: 45px !important;
}

.round-b-46 {
	border-bottom-left-radius: 46px !important;
	border-bottom-right-radius: 46px !important;
}

.round-b-47 {
	border-bottom-left-radius: 47px !important;
	border-bottom-right-radius: 47px !important;
}

.round-b-48 {
	border-bottom-left-radius: 48px !important;
	border-bottom-right-radius: 48px !important;
}

.round-b-49 {
	border-bottom-left-radius: 49px !important;
	border-bottom-right-radius: 49px !important;
}

.round-l-0 {
	border-top-left-radius: 0px !important;
	border-bottom-left-radius: 0px !important;
}

.round-l-1 {
	border-top-left-radius: 1px !important;
	border-bottom-left-radius: 1px !important;
}

.round-l-2 {
	border-top-left-radius: 2px !important;
	border-bottom-left-radius: 2px !important;
}

.round-l-3 {
	border-top-left-radius: 3px !important;
	border-bottom-left-radius: 3px !important;
}

.round-l-4 {
	border-top-left-radius: 4px !important;
	border-bottom-left-radius: 4px !important;
}

.round-l-5 {
	border-top-left-radius: 5px !important;
	border-bottom-left-radius: 5px !important;
}

.round-l-6 {
	border-top-left-radius: 6px !important;
	border-bottom-left-radius: 6px !important;
}

.round-l-7 {
	border-top-left-radius: 7px !important;
	border-bottom-left-radius: 7px !important;
}

.round-l-8 {
	border-top-left-radius: 8px !important;
	border-bottom-left-radius: 8px !important;
}

.round-l-9 {
	border-top-left-radius: 9px !important;
	border-bottom-left-radius: 9px !important;
}

.round-l-10 {
	border-top-left-radius: 10px !important;
	border-bottom-left-radius: 10px !important;
}

.round-l-11 {
	border-top-left-radius: 11px !important;
	border-bottom-left-radius: 11px !important;
}

.round-l-12 {
	border-top-left-radius: 12px !important;
	border-bottom-left-radius: 12px !important;
}

.round-l-13 {
	border-top-left-radius: 13px !important;
	border-bottom-left-radius: 13px !important;
}

.round-l-14 {
	border-top-left-radius: 14px !important;
	border-bottom-left-radius: 14px !important;
}

.round-l-15 {
	border-top-left-radius: 15px !important;
	border-bottom-left-radius: 15px !important;
}

.round-l-16 {
	border-top-left-radius: 16px !important;
	border-bottom-left-radius: 16px !important;
}

.round-l-17 {
	border-top-left-radius: 17px !important;
	border-bottom-left-radius: 17px !important;
}

.round-l-18 {
	border-top-left-radius: 18px !important;
	border-bottom-left-radius: 18px !important;
}

.round-l-19 {
	border-top-left-radius: 19px !important;
	border-bottom-left-radius: 19px !important;
}

.round-l-20 {
	border-top-left-radius: 20px !important;
	border-bottom-left-radius: 20px !important;
}

.round-l-21 {
	border-top-left-radius: 21px !important;
	border-bottom-left-radius: 21px !important;
}

.round-l-22 {
	border-top-left-radius: 22px !important;
	border-bottom-left-radius: 22px !important;
}

.round-l-23 {
	border-top-left-radius: 23px !important;
	border-bottom-left-radius: 23px !important;
}

.round-l-24 {
	border-top-left-radius: 24px !important;
	border-bottom-left-radius: 24px !important;
}

.round-l-25 {
	border-top-left-radius: 25px !important;
	border-bottom-left-radius: 25px !important;
}

.round-l-26 {
	border-top-left-radius: 26px !important;
	border-bottom-left-radius: 26px !important;
}

.round-l-27 {
	border-top-left-radius: 27px !important;
	border-bottom-left-radius: 27px !important;
}

.round-l-28 {
	border-top-left-radius: 28px !important;
	border-bottom-left-radius: 28px !important;
}

.round-l-29 {
	border-top-left-radius: 29px !important;
	border-bottom-left-radius: 29px !important;
}

.round-l-30 {
	border-top-left-radius: 30px !important;
	border-bottom-left-radius: 30px !important;
}

.round-l-31 {
	border-top-left-radius: 31px !important;
	border-bottom-left-radius: 31px !important;
}

.round-l-32 {
	border-top-left-radius: 32px !important;
	border-bottom-left-radius: 32px !important;
}

.round-l-33 {
	border-top-left-radius: 33px !important;
	border-bottom-left-radius: 33px !important;
}

.round-l-34 {
	border-top-left-radius: 34px !important;
	border-bottom-left-radius: 34px !important;
}

.round-l-35 {
	border-top-left-radius: 35px !important;
	border-bottom-left-radius: 35px !important;
}

.round-l-36 {
	border-top-left-radius: 36px !important;
	border-bottom-left-radius: 36px !important;
}

.round-l-37 {
	border-top-left-radius: 37px !important;
	border-bottom-left-radius: 37px !important;
}

.round-l-38 {
	border-top-left-radius: 38px !important;
	border-bottom-left-radius: 38px !important;
}

.round-l-39 {
	border-top-left-radius: 39px !important;
	border-bottom-left-radius: 39px !important;
}

.round-l-40 {
	border-top-left-radius: 40px !important;
	border-bottom-left-radius: 40px !important;
}

.round-l-41 {
	border-top-left-radius: 41px !important;
	border-bottom-left-radius: 41px !important;
}

.round-l-42 {
	border-top-left-radius: 42px !important;
	border-bottom-left-radius: 42px !important;
}

.round-l-43 {
	border-top-left-radius: 43px !important;
	border-bottom-left-radius: 43px !important;
}

.round-l-44 {
	border-top-left-radius: 44px !important;
	border-bottom-left-radius: 44px !important;
}

.round-l-45 {
	border-top-left-radius: 45px !important;
	border-bottom-left-radius: 45px !important;
}

.round-l-46 {
	border-top-left-radius: 46px !important;
	border-bottom-left-radius: 46px !important;
}

.round-l-47 {
	border-top-left-radius: 47px !important;
	border-bottom-left-radius: 47px !important;
}

.round-l-48 {
	border-top-left-radius: 48px !important;
	border-bottom-left-radius: 48px !important;
}

.round-l-49 {
	border-top-left-radius: 49px !important;
	border-bottom-left-radius: 49px !important;
}

.round-r-0 {
	border-top-right-radius: 0px !important;
	border-bottom-right-radius: 0px !important;
}

.round-r-1 {
	border-top-right-radius: 1px !important;
	border-bottom-right-radius: 1px !important;
}

.round-r-2 {
	border-top-right-radius: 2px !important;
	border-bottom-right-radius: 2px !important;
}

.round-r-3 {
	border-top-right-radius: 3px !important;
	border-bottom-right-radius: 3px !important;
}

.round-r-4 {
	border-top-right-radius: 4px !important;
	border-bottom-right-radius: 4px !important;
}

.round-r-5 {
	border-top-right-radius: 5px !important;
	border-bottom-right-radius: 5px !important;
}

.round-r-6 {
	border-top-right-radius: 6px !important;
	border-bottom-right-radius: 6px !important;
}

.round-r-7 {
	border-top-right-radius: 7px !important;
	border-bottom-right-radius: 7px !important;
}

.round-r-8 {
	border-top-right-radius: 8px !important;
	border-bottom-right-radius: 8px !important;
}

.round-r-9 {
	border-top-right-radius: 9px !important;
	border-bottom-right-radius: 9px !important;
}

.round-r-10 {
	border-top-right-radius: 10px !important;
	border-bottom-right-radius: 10px !important;
}

.round-r-11 {
	border-top-right-radius: 11px !important;
	border-bottom-right-radius: 11px !important;
}

.round-r-12 {
	border-top-right-radius: 12px !important;
	border-bottom-right-radius: 12px !important;
}

.round-r-13 {
	border-top-right-radius: 13px !important;
	border-bottom-right-radius: 13px !important;
}

.round-r-14 {
	border-top-right-radius: 14px !important;
	border-bottom-right-radius: 14px !important;
}

.round-r-15 {
	border-top-right-radius: 15px !important;
	border-bottom-right-radius: 15px !important;
}

.round-r-16 {
	border-top-right-radius: 16px !important;
	border-bottom-right-radius: 16px !important;
}

.round-r-17 {
	border-top-right-radius: 17px !important;
	border-bottom-right-radius: 17px !important;
}

.round-r-18 {
	border-top-right-radius: 18px !important;
	border-bottom-right-radius: 18px !important;
}

.round-r-19 {
	border-top-right-radius: 19px !important;
	border-bottom-right-radius: 19px !important;
}

.round-r-20 {
	border-top-right-radius: 20px !important;
	border-bottom-right-radius: 20px !important;
}

.round-r-21 {
	border-top-right-radius: 21px !important;
	border-bottom-right-radius: 21px !important;
}

.round-r-22 {
	border-top-right-radius: 22px !important;
	border-bottom-right-radius: 22px !important;
}

.round-r-23 {
	border-top-right-radius: 23px !important;
	border-bottom-right-radius: 23px !important;
}

.round-r-24 {
	border-top-right-radius: 24px !important;
	border-bottom-right-radius: 24px !important;
}

.round-r-25 {
	border-top-right-radius: 25px !important;
	border-bottom-right-radius: 25px !important;
}

.round-r-26 {
	border-top-right-radius: 26px !important;
	border-bottom-right-radius: 26px !important;
}

.round-r-27 {
	border-top-right-radius: 27px !important;
	border-bottom-right-radius: 27px !important;
}

.round-r-28 {
	border-top-right-radius: 28px !important;
	border-bottom-right-radius: 28px !important;
}

.round-r-29 {
	border-top-right-radius: 29px !important;
	border-bottom-right-radius: 29px !important;
}

.round-r-30 {
	border-top-right-radius: 30px !important;
	border-bottom-right-radius: 30px !important;
}

.round-r-31 {
	border-top-right-radius: 31px !important;
	border-bottom-right-radius: 31px !important;
}

.round-r-32 {
	border-top-right-radius: 32px !important;
	border-bottom-right-radius: 32px !important;
}

.round-r-33 {
	border-top-right-radius: 33px !important;
	border-bottom-right-radius: 33px !important;
}

.round-r-34 {
	border-top-right-radius: 34px !important;
	border-bottom-right-radius: 34px !important;
}

.round-r-35 {
	border-top-right-radius: 35px !important;
	border-bottom-right-radius: 35px !important;
}

.round-r-36 {
	border-top-right-radius: 36px !important;
	border-bottom-right-radius: 36px !important;
}

.round-r-37 {
	border-top-right-radius: 37px !important;
	border-bottom-right-radius: 37px !important;
}

.round-r-38 {
	border-top-right-radius: 38px !important;
	border-bottom-right-radius: 38px !important;
}

.round-r-39 {
	border-top-right-radius: 39px !important;
	border-bottom-right-radius: 39px !important;
}

.round-r-40 {
	border-top-right-radius: 40px !important;
	border-bottom-right-radius: 40px !important;
}

.round-r-41 {
	border-top-right-radius: 41px !important;
	border-bottom-right-radius: 41px !important;
}

.round-r-42 {
	border-top-right-radius: 42px !important;
	border-bottom-right-radius: 42px !important;
}

.round-r-43 {
	border-top-right-radius: 43px !important;
	border-bottom-right-radius: 43px !important;
}

.round-r-44 {
	border-top-right-radius: 44px !important;
	border-bottom-right-radius: 44px !important;
}

.round-r-45 {
	border-top-right-radius: 45px !important;
	border-bottom-right-radius: 45px !important;
}

.round-r-46 {
	border-top-right-radius: 46px !important;
	border-bottom-right-radius: 46px !important;
}

.round-r-47 {
	border-top-right-radius: 47px !important;
	border-bottom-right-radius: 47px !important;
}

.round-r-48 {
	border-top-right-radius: 48px !important;
	border-bottom-right-radius: 48px !important;
}

.round-r-49 {
	border-top-right-radius: 49px !important;
	border-bottom-right-radius: 49px !important;
}

.rounded {
	border-radius: 50% !important;
}

.opacity-0 {
	opacity: 0;
}

.opacity-1 {
	opacity: 0.1;
}

.opacity-2 {
	opacity: 0.2;
}

.opacity-3 {
	opacity: 0.3;
}

.opacity-4 {
	opacity: 0.4;
}

.opacity-5 {
	opacity: 0.5;
}

.opacity-6 {
	opacity: 0.6;
}

.opacity-7 {
	opacity: 0.7;
}

.opacity-8 {
	opacity: 0.8;
}

.opacity-9 {
	opacity: 0.9;
}

.opacity-10 {
	opacity: 1;
}

.shadow {
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.shadow-0 {
	box-shadow: 0 0px 5px rgba(0, 0, 0, 0.08);
}

.shadow-1 {
	box-shadow: 0 1px 6px rgba(0, 0, 0, 0.08);
}

.shadow-2 {
	box-shadow: 0 2px 7px rgba(0, 0, 0, 0.08);
}

.shadow-3 {
	box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
}

.shadow-4 {
	box-shadow: 0 4px 9px rgba(0, 0, 0, 0.08);
}

.shadow-5 {
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.08);
}

.shadow-6 {
	box-shadow: 0 6px 11px rgba(0, 0, 0, 0.08);
}

.shadow-7 {
	box-shadow: 0 7px 12px rgba(0, 0, 0, 0.08);
}

.shadow-8 {
	box-shadow: 0 8px 13px rgba(0, 0, 0, 0.08);
}

.shadow-9 {
	box-shadow: 0 9px 14px rgba(0, 0, 0, 0.08);
}

.shadow-10 {
	box-shadow: 0 10px 15px rgba(0, 0, 0, 0.08);
}

.shadow-11 {
	box-shadow: 0 11px 16px rgba(0, 0, 0, 0.08);
}

.shadow-12 {
	box-shadow: 0 12px 17px rgba(0, 0, 0, 0.08);
}

.shadow-13 {
	box-shadow: 0 13px 18px rgba(0, 0, 0, 0.08);
}

.shadow-14 {
	box-shadow: 0 14px 19px rgba(0, 0, 0, 0.08);
}

.shadow-15 {
	box-shadow: 0 15px 20px rgba(0, 0, 0, 0.08);
}

.shadow-16 {
	box-shadow: 0 16px 21px rgba(0, 0, 0, 0.08);
}

.shadow-17 {
	box-shadow: 0 17px 22px rgba(0, 0, 0, 0.08);
}

.shadow-18 {
	box-shadow: 0 18px 23px rgba(0, 0, 0, 0.08);
}

.shadow-19 {
	box-shadow: 0 19px 24px rgba(0, 0, 0, 0.08);
}

.shadow-20 {
	box-shadow: 0 20px 25px rgba(0, 0, 0, 0.08);
}

.shadow-21 {
	box-shadow: 0 21px 26px rgba(0, 0, 0, 0.08);
}

.shadow-22 {
	box-shadow: 0 22px 27px rgba(0, 0, 0, 0.08);
}

.shadow-23 {
	box-shadow: 0 23px 28px rgba(0, 0, 0, 0.08);
}

.shadow-24 {
	box-shadow: 0 24px 29px rgba(0, 0, 0, 0.08);
}

.shadow-25 {
	box-shadow: 0 25px 30px rgba(0, 0, 0, 0.08);
}

.border-0 {
	border: solid 0px #f5f5f5 !important;
}

.border-0-bk {
	border: solid 0px #282828 !important;
}

.border {
	border: solid 1px #f5f5f5 !important;
}

.border-bk {
	border: solid 1px #282828 !important;
}

.border-1 {
	border: solid 1px #f5f5f5 !important;
}

.border-1-bk {
	border: solid 1px #f5f5f5 !important;
}

.border-2 {
	border: solid 2px #f5f5f5 !important;
}

.border-2-bk {
	border: solid 2px #f5f5f5 !important;
}

.border-3 {
	border: solid 3px #f5f5f5 !important;
}

.border-3-bk {
	border: solid 3px #f5f5f5 !important;
}

.border-4 {
	border: solid 4px #f5f5f5 !important;
}

.border-4-bk {
	border: solid 4px #f5f5f5 !important;
}

.border-5 {
	border: solid 5px #f5f5f5 !important;
}

.border-5-bk {
	border: solid 5px #f5f5f5 !important;
}

.border-l-1 {
	border-left: solid 1px whitesmoke !important;
}

.border-l-1-bk {
	border-left: solid 1px #282828 !important;
}

.border-l-2 {
	border-left: solid 2px whitesmoke !important;
}

.border-l-2-bk {
	border-left: solid 2px #282828 !important;
}

.border-l-3 {
	border-left: solid 3px whitesmoke !important;
}

.border-l-3-bk {
	border-left: solid 3px #282828 !important;
}

.border-l-4 {
	border-left: solid 4px whitesmoke !important;
}

.border-l-4-bk {
	border-left: solid 4px #282828 !important;
}

.border-l-5 {
	border-left: solid 5px whitesmoke !important;
}

.border-l-5-bk {
	border-left: solid 5px #282828 !important;
}

.border-r-1 {
	border-right: solid 1px whitesmoke !important;
}

.border-r-1-bk {
	border-right: solid 1px #282828 !important;
}

.border-r-2 {
	border-right: solid 2px whitesmoke !important;
}

.border-r-2-bk {
	border-right: solid 2px #282828 !important;
}

.border-r-3 {
	border-right: solid 3px whitesmoke !important;
}

.border-r-3-bk {
	border-right: solid 3px #282828 !important;
}

.border-r-4 {
	border-right: solid 4px whitesmoke !important;
}

.border-r-4-bk {
	border-right: solid 4px #282828 !important;
}

.border-r-5 {
	border-right: solid 5px whitesmoke !important;
}

.border-r-5-bk {
	border-right: solid 5px #282828 !important;
}

.border-t-1 {
	border-top: solid 1px whitesmoke !important;
}

.border-t-1-bk {
	border-top: solid 1px #282828 !important;
}

.border-t-2 {
	border-top: solid 2px whitesmoke !important;
}

.border-t-2-bk {
	border-top: solid 2px #282828 !important;
}

.border-t-3 {
	border-top: solid 3px whitesmoke !important;
}

.border-t-3-bk {
	border-top: solid 3px #282828 !important;
}

.border-t-4 {
	border-top: solid 4px whitesmoke !important;
}

.border-t-4-bk {
	border-top: solid 4px #282828 !important;
}

.border-t-5 {
	border-top: solid 5px whitesmoke !important;
}

.border-t-5-bk {
	border-top: solid 5px #282828 !important;
}

.border-b-1 {
	border-bottom: solid 1px whitesmoke !important;
}

.border-b-1-bk {
	border-bottom: solid 1px #282828 !important;
}

.border-b-2 {
	border-bottom: solid 2px whitesmoke !important;
}

.border-b-2-bk {
	border-bottom: solid 2px #282828 !important;
}

.border-b-3 {
	border-bottom: solid 3px whitesmoke !important;
}

.border-b-3-bk {
	border-bottom: solid 3px #282828 !important;
}

.border-b-4 {
	border-bottom: solid 4px whitesmoke !important;
}

.border-b-4-bk {
	border-bottom: solid 4px #282828 !important;
}

.border-b-5 {
	border-bottom: solid 5px whitesmoke !important;
}

.border-b-5-bk {
	border-bottom: solid 5px #282828 !important;
}

.border-a-1 {
	border: solid 1px whitesmoke !important;
}

.border-a-1-bk {
	border: solid 1px #282828 !important;
}

.border-a-2 {
	border: solid 2px whitesmoke !important;
}

.border-a-2-bk {
	border: solid 2px #282828 !important;
}

.border-a-3 {
	border: solid 3px whitesmoke !important;
}

.border-a-3-bk {
	border: solid 3px #282828 !important;
}

.border-a-4 {
	border: solid 4px whitesmoke !important;
}

.border-a-4-bk {
	border: solid 4px #282828 !important;
}

.border-a-5 {
	border: solid 5px whitesmoke !important;
}

.border-a-5-bk {
	border: solid 5px #282828 !important;
}

.pa-0 {
	padding: 0px;
}

.pa-1 {
	padding: 1px;
}

.pa-2 {
	padding: 2px;
}

.pa-3 {
	padding: 3px;
}

.pa-4 {
	padding: 4px;
}

.pa-5 {
	padding: 5px;
}

.pa-6 {
	padding: 6px;
}

.pa-7 {
	padding: 7px;
}

.pa-8 {
	padding: 8px;
}

.pa-9 {
	padding: 9px;
}

.pa-10 {
	padding: 10px;
}

.pa-11 {
	padding: 11px;
}

.pa-12 {
	padding: 12px;
}

.pa-13 {
	padding: 13px;
}

.pa-14 {
	padding: 14px;
}

.pa-15 {
	padding: 15px;
}

.pa-16 {
	padding: 16px;
}

.pa-17 {
	padding: 17px;
}

.pa-18 {
	padding: 18px;
}

.pa-19 {
	padding: 19px;
}

.pa-20 {
	padding: 20px;
}

.pa-21 {
	padding: 21px;
}

.pa-22 {
	padding: 22px;
}

.pa-23 {
	padding: 23px;
}

.pa-24 {
	padding: 24px;
}

.pa-25 {
	padding: 25px;
}

.pa-26 {
	padding: 26px;
}

.pa-27 {
	padding: 27px;
}

.pa-28 {
	padding: 28px;
}

.pa-29 {
	padding: 29px;
}

.pa-30 {
	padding: 30px;
}

.pa-31 {
	padding: 31px;
}

.pa-32 {
	padding: 32px;
}

.pa-33 {
	padding: 33px;
}

.pa-34 {
	padding: 34px;
}

.pa-35 {
	padding: 35px;
}

.pa-36 {
	padding: 36px;
}

.pa-37 {
	padding: 37px;
}

.pa-38 {
	padding: 38px;
}

.pa-39 {
	padding: 39px;
}

.pa-40 {
	padding: 40px;
}

.pa-41 {
	padding: 41px;
}

.pa-42 {
	padding: 42px;
}

.pa-43 {
	padding: 43px;
}

.pa-44 {
	padding: 44px;
}

.pa-45 {
	padding: 45px;
}

.pa-46 {
	padding: 46px;
}

.pa-47 {
	padding: 47px;
}

.pa-48 {
	padding: 48px;
}

.pa-49 {
	padding: 49px;
}

.pa-50 {
	padding: 50px;
}

.pa-n1 {
	padding: 4px;
}

.pa-n2 {
	padding: 8px;
}

.pa-n3 {
	padding: 12px;
}

.pa-n4 {
	padding: 16px;
}

.pa-n5 {
	padding: 20px;
}

.pa-n6 {
	padding: 24px;
}

.pa-n7 {
	padding: 28px;
}

.pa-n8 {
	padding: 32px;
}

.pa-n9 {
	padding: 36px;
}

.pa-n10 {
	padding: 40px;
}

.pa-n11 {
	padding: 44px;
}

.pa-n12 {
	padding: 48px;
}

.pa-n13 {
	padding: 52px;
}

.pa-n14 {
	padding: 56px;
}

.pa-n15 {
	padding: 60px;
}

.pa-n16 {
	padding: 64px;
}

.pa-n17 {
	padding: 68px;
}

.pa-n18 {
	padding: 72px;
}

.pa-n19 {
	padding: 76px;
}

.pa-n20 {
	padding: 80px;
}

.pa-n21 {
	padding: 84px;
}

.pa-n22 {
	padding: 88px;
}

.pa-n23 {
	padding: 92px;
}

.pa-n24 {
	padding: 96px;
}

.pa-n25 {
	padding: 100px;
}

.pt-0 {
	padding-top: 0px;
}

.pt-1 {
	padding-top: 1px;
}

.pt-2 {
	padding-top: 2px;
}

.pt-3 {
	padding-top: 3px;
}

.pt-4 {
	padding-top: 4px;
}

.pt-5 {
	padding-top: 5px;
}

.pt-6 {
	padding-top: 6px;
}

.pt-7 {
	padding-top: 7px;
}

.pt-8 {
	padding-top: 8px;
}

.pt-9 {
	padding-top: 9px;
}

.pt-10 {
	padding-top: 10px;
}

.pt-11 {
	padding-top: 11px;
}

.pt-12 {
	padding-top: 12px;
}

.pt-13 {
	padding-top: 13px;
}

.pt-14 {
	padding-top: 14px;
}

.pt-15 {
	padding-top: 15px;
}

.pt-16 {
	padding-top: 16px;
}

.pt-17 {
	padding-top: 17px;
}

.pt-18 {
	padding-top: 18px;
}

.pt-19 {
	padding-top: 19px;
}

.pt-20 {
	padding-top: 20px;
}

.pt-21 {
	padding-top: 21px;
}

.pt-22 {
	padding-top: 22px;
}

.pt-23 {
	padding-top: 23px;
}

.pt-24 {
	padding-top: 24px;
}

.pt-25 {
	padding-top: 25px;
}

.pt-26 {
	padding-top: 26px;
}

.pt-27 {
	padding-top: 27px;
}

.pt-28 {
	padding-top: 28px;
}

.pt-29 {
	padding-top: 29px;
}

.pt-30 {
	padding-top: 30px;
}

.pt-31 {
	padding-top: 31px;
}

.pt-32 {
	padding-top: 32px;
}

.pt-33 {
	padding-top: 33px;
}

.pt-34 {
	padding-top: 34px;
}

.pt-35 {
	padding-top: 35px;
}

.pt-36 {
	padding-top: 36px;
}

.pt-37 {
	padding-top: 37px;
}

.pt-38 {
	padding-top: 38px;
}

.pt-39 {
	padding-top: 39px;
}

.pt-40 {
	padding-top: 40px;
}

.pt-41 {
	padding-top: 41px;
}

.pt-42 {
	padding-top: 42px;
}

.pt-43 {
	padding-top: 43px;
}

.pt-44 {
	padding-top: 44px;
}

.pt-45 {
	padding-top: 45px;
}

.pt-46 {
	padding-top: 46px;
}

.pt-47 {
	padding-top: 47px;
}

.pt-48 {
	padding-top: 48px;
}

.pt-49 {
	padding-top: 49px;
}

.pt-50 {
	padding-top: 50px;
}

.pt-n1 {
	padding-top: 4px;
}

.pt-n2 {
	padding-top: 8px;
}

.pt-n3 {
	padding-top: 12px;
}

.pt-n4 {
	padding-top: 16px;
}

.pt-n5 {
	padding-top: 20px;
}

.pt-n6 {
	padding-top: 24px;
}

.pt-n7 {
	padding-top: 28px;
}

.pt-n8 {
	padding-top: 32px;
}

.pt-n9 {
	padding-top: 36px;
}

.pt-n10 {
	padding-top: 40px;
}

.pt-n11 {
	padding-top: 44px;
}

.pt-n12 {
	padding-top: 48px;
}

.pt-n13 {
	padding-top: 52px;
}

.pt-n14 {
	padding-top: 56px;
}

.pt-n15 {
	padding-top: 60px;
}

.pt-n16 {
	padding-top: 64px;
}

.pt-n17 {
	padding-top: 68px;
}

.pt-n18 {
	padding-top: 72px;
}

.pt-n19 {
	padding-top: 76px;
}

.pt-n20 {
	padding-top: 80px;
}

.pt-n21 {
	padding-top: 84px;
}

.pt-n22 {
	padding-top: 88px;
}

.pt-n23 {
	padding-top: 92px;
}

.pt-n24 {
	padding-top: 96px;
}

.pt-n25 {
	padding-top: 100px;
}

.pr-0 {
	padding-right: 0px;
}

.pr-1 {
	padding-right: 1px;
}

.pr-2 {
	padding-right: 2px;
}

.pr-3 {
	padding-right: 3px;
}

.pr-4 {
	padding-right: 4px;
}

.pr-5 {
	padding-right: 5px;
}

.pr-6 {
	padding-right: 6px;
}

.pr-7 {
	padding-right: 7px;
}

.pr-8 {
	padding-right: 8px;
}

.pr-9 {
	padding-right: 9px;
}

.pr-10 {
	padding-right: 10px;
}

.pr-11 {
	padding-right: 11px;
}

.pr-12 {
	padding-right: 12px;
}

.pr-13 {
	padding-right: 13px;
}

.pr-14 {
	padding-right: 14px;
}

.pr-15 {
	padding-right: 15px;
}

.pr-16 {
	padding-right: 16px;
}

.pr-17 {
	padding-right: 17px;
}

.pr-18 {
	padding-right: 18px;
}

.pr-19 {
	padding-right: 19px;
}

.pr-20 {
	padding-right: 20px;
}

.pr-21 {
	padding-right: 21px;
}

.pr-22 {
	padding-right: 22px;
}

.pr-23 {
	padding-right: 23px;
}

.pr-24 {
	padding-right: 24px;
}

.pr-25 {
	padding-right: 25px;
}

.pr-26 {
	padding-right: 26px;
}

.pr-27 {
	padding-right: 27px;
}

.pr-28 {
	padding-right: 28px;
}

.pr-29 {
	padding-right: 29px;
}

.pr-30 {
	padding-right: 30px;
}

.pr-31 {
	padding-right: 31px;
}

.pr-32 {
	padding-right: 32px;
}

.pr-33 {
	padding-right: 33px;
}

.pr-34 {
	padding-right: 34px;
}

.pr-35 {
	padding-right: 35px;
}

.pr-36 {
	padding-right: 36px;
}

.pr-37 {
	padding-right: 37px;
}

.pr-38 {
	padding-right: 38px;
}

.pr-39 {
	padding-right: 39px;
}

.pr-40 {
	padding-right: 40px;
}

.pr-41 {
	padding-right: 41px;
}

.pr-42 {
	padding-right: 42px;
}

.pr-43 {
	padding-right: 43px;
}

.pr-44 {
	padding-right: 44px;
}

.pr-45 {
	padding-right: 45px;
}

.pr-46 {
	padding-right: 46px;
}

.pr-47 {
	padding-right: 47px;
}

.pr-48 {
	padding-right: 48px;
}

.pr-49 {
	padding-right: 49px;
}

.pr-50 {
	padding-right: 50px;
}

.pr-n1 {
	padding-right: 4px;
}

.pr-n2 {
	padding-right: 8px;
}

.pr-n3 {
	padding-right: 12px;
}

.pr-n4 {
	padding-right: 16px;
}

.pr-n5 {
	padding-right: 20px;
}

.pr-n6 {
	padding-right: 24px;
}

.pr-n7 {
	padding-right: 28px;
}

.pr-n8 {
	padding-right: 32px;
}

.pr-n9 {
	padding-right: 36px;
}

.pr-n10 {
	padding-right: 40px;
}

.pr-n11 {
	padding-right: 44px;
}

.pr-n12 {
	padding-right: 48px;
}

.pr-n13 {
	padding-right: 52px;
}

.pr-n14 {
	padding-right: 56px;
}

.pr-n15 {
	padding-right: 60px;
}

.pr-n16 {
	padding-right: 64px;
}

.pr-n17 {
	padding-right: 68px;
}

.pr-n18 {
	padding-right: 72px;
}

.pr-n19 {
	padding-right: 76px;
}

.pr-n20 {
	padding-right: 80px;
}

.pr-n21 {
	padding-right: 84px;
}

.pr-n22 {
	padding-right: 88px;
}

.pr-n23 {
	padding-right: 92px;
}

.pr-n24 {
	padding-right: 96px;
}

.pr-n25 {
	padding-right: 100px;
}

.pb-0 {
	padding-bottom: 0px;
}

.pb-1 {
	padding-bottom: 1px;
}

.pb-2 {
	padding-bottom: 2px;
}

.pb-3 {
	padding-bottom: 3px;
}

.pb-4 {
	padding-bottom: 4px;
}

.pb-5 {
	padding-bottom: 5px;
}

.pb-6 {
	padding-bottom: 6px;
}

.pb-7 {
	padding-bottom: 7px;
}

.pb-8 {
	padding-bottom: 8px;
}

.pb-9 {
	padding-bottom: 9px;
}

.pb-10 {
	padding-bottom: 10px;
}

.pb-11 {
	padding-bottom: 11px;
}

.pb-12 {
	padding-bottom: 12px;
}

.pb-13 {
	padding-bottom: 13px;
}

.pb-14 {
	padding-bottom: 14px;
}

.pb-15 {
	padding-bottom: 15px;
}

.pb-16 {
	padding-bottom: 16px;
}

.pb-17 {
	padding-bottom: 17px;
}

.pb-18 {
	padding-bottom: 18px;
}

.pb-19 {
	padding-bottom: 19px;
}

.pb-20 {
	padding-bottom: 20px;
}

.pb-21 {
	padding-bottom: 21px;
}

.pb-22 {
	padding-bottom: 22px;
}

.pb-23 {
	padding-bottom: 23px;
}

.pb-24 {
	padding-bottom: 24px;
}

.pb-25 {
	padding-bottom: 25px;
}

.pb-26 {
	padding-bottom: 26px;
}

.pb-27 {
	padding-bottom: 27px;
}

.pb-28 {
	padding-bottom: 28px;
}

.pb-29 {
	padding-bottom: 29px;
}

.pb-30 {
	padding-bottom: 30px;
}

.pb-31 {
	padding-bottom: 31px;
}

.pb-32 {
	padding-bottom: 32px;
}

.pb-33 {
	padding-bottom: 33px;
}

.pb-34 {
	padding-bottom: 34px;
}

.pb-35 {
	padding-bottom: 35px;
}

.pb-36 {
	padding-bottom: 36px;
}

.pb-37 {
	padding-bottom: 37px;
}

.pb-38 {
	padding-bottom: 38px;
}

.pb-39 {
	padding-bottom: 39px;
}

.pb-40 {
	padding-bottom: 40px;
}

.pb-41 {
	padding-bottom: 41px;
}

.pb-42 {
	padding-bottom: 42px;
}

.pb-43 {
	padding-bottom: 43px;
}

.pb-44 {
	padding-bottom: 44px;
}

.pb-45 {
	padding-bottom: 45px;
}

.pb-46 {
	padding-bottom: 46px;
}

.pb-47 {
	padding-bottom: 47px;
}

.pb-48 {
	padding-bottom: 48px;
}

.pb-49 {
	padding-bottom: 49px;
}

.pb-50 {
	padding-bottom: 50px;
}

.pb-n1 {
	padding-bottom: 4px;
}

.pb-n2 {
	padding-bottom: 8px;
}

.pb-n3 {
	padding-bottom: 12px;
}

.pb-n4 {
	padding-bottom: 16px;
}

.pb-n5 {
	padding-bottom: 20px;
}

.pb-n6 {
	padding-bottom: 24px;
}

.pb-n7 {
	padding-bottom: 28px;
}

.pb-n8 {
	padding-bottom: 32px;
}

.pb-n9 {
	padding-bottom: 36px;
}

.pb-n10 {
	padding-bottom: 40px;
}

.pb-n11 {
	padding-bottom: 44px;
}

.pb-n12 {
	padding-bottom: 48px;
}

.pb-n13 {
	padding-bottom: 52px;
}

.pb-n14 {
	padding-bottom: 56px;
}

.pb-n15 {
	padding-bottom: 60px;
}

.pb-n16 {
	padding-bottom: 64px;
}

.pb-n17 {
	padding-bottom: 68px;
}

.pb-n18 {
	padding-bottom: 72px;
}

.pb-n19 {
	padding-bottom: 76px;
}

.pb-n20 {
	padding-bottom: 80px;
}

.pb-n21 {
	padding-bottom: 84px;
}

.pb-n22 {
	padding-bottom: 88px;
}

.pb-n23 {
	padding-bottom: 92px;
}

.pb-n24 {
	padding-bottom: 96px;
}

.pb-n25 {
	padding-bottom: 100px;
}

.pl-0 {
	padding-left: 0px;
}

.pl-1 {
	padding-left: 1px;
}

.pl-2 {
	padding-left: 2px;
}

.pl-3 {
	padding-left: 3px;
}

.pl-4 {
	padding-left: 4px;
}

.pl-5 {
	padding-left: 5px;
}

.pl-6 {
	padding-left: 6px;
}

.pl-7 {
	padding-left: 7px;
}

.pl-8 {
	padding-left: 8px;
}

.pl-9 {
	padding-left: 9px;
}

.pl-10 {
	padding-left: 10px;
}

.pl-11 {
	padding-left: 11px;
}

.pl-12 {
	padding-left: 12px;
}

.pl-13 {
	padding-left: 13px;
}

.pl-14 {
	padding-left: 14px;
}

.pl-15 {
	padding-left: 15px;
}

.pl-16 {
	padding-left: 16px;
}

.pl-17 {
	padding-left: 17px;
}

.pl-18 {
	padding-left: 18px;
}

.pl-19 {
	padding-left: 19px;
}

.pl-20 {
	padding-left: 20px;
}

.pl-21 {
	padding-left: 21px;
}

.pl-22 {
	padding-left: 22px;
}

.pl-23 {
	padding-left: 23px;
}

.pl-24 {
	padding-left: 24px;
}

.pl-25 {
	padding-left: 25px;
}

.pl-26 {
	padding-left: 26px;
}

.pl-27 {
	padding-left: 27px;
}

.pl-28 {
	padding-left: 28px;
}

.pl-29 {
	padding-left: 29px;
}

.pl-30 {
	padding-left: 30px;
}

.pl-31 {
	padding-left: 31px;
}

.pl-32 {
	padding-left: 32px;
}

.pl-33 {
	padding-left: 33px;
}

.pl-34 {
	padding-left: 34px;
}

.pl-35 {
	padding-left: 35px;
}

.pl-36 {
	padding-left: 36px;
}

.pl-37 {
	padding-left: 37px;
}

.pl-38 {
	padding-left: 38px;
}

.pl-39 {
	padding-left: 39px;
}

.pl-40 {
	padding-left: 40px;
}

.pl-41 {
	padding-left: 41px;
}

.pl-42 {
	padding-left: 42px;
}

.pl-43 {
	padding-left: 43px;
}

.pl-44 {
	padding-left: 44px;
}

.pl-45 {
	padding-left: 45px;
}

.pl-46 {
	padding-left: 46px;
}

.pl-47 {
	padding-left: 47px;
}

.pl-48 {
	padding-left: 48px;
}

.pl-49 {
	padding-left: 49px;
}

.pl-50 {
	padding-left: 50px;
}

.pl-n1 {
	padding-left: 4px;
}

.pl-n2 {
	padding-left: 8px;
}

.pl-n3 {
	padding-left: 12px;
}

.pl-n4 {
	padding-left: 16px;
}

.pl-n5 {
	padding-left: 20px;
}

.pl-n6 {
	padding-left: 24px;
}

.pl-n7 {
	padding-left: 28px;
}

.pl-n8 {
	padding-left: 32px;
}

.pl-n9 {
	padding-left: 36px;
}

.pl-n10 {
	padding-left: 40px;
}

.pl-n11 {
	padding-left: 44px;
}

.pl-n12 {
	padding-left: 48px;
}

.pl-n13 {
	padding-left: 52px;
}

.pl-n14 {
	padding-left: 56px;
}

.pl-n15 {
	padding-left: 60px;
}

.pl-n16 {
	padding-left: 64px;
}

.pl-n17 {
	padding-left: 68px;
}

.pl-n18 {
	padding-left: 72px;
}

.pl-n19 {
	padding-left: 76px;
}

.pl-n20 {
	padding-left: 80px;
}

.pl-n21 {
	padding-left: 84px;
}

.pl-n22 {
	padding-left: 88px;
}

.pl-n23 {
	padding-left: 92px;
}

.pl-n24 {
	padding-left: 96px;
}

.pl-n25 {
	padding-left: 100px;
}

.px-0 {
	padding-left: 0px;
	padding-right: 0px;
}

.px-1 {
	padding-left: 1px;
	padding-right: 1px;
}

.px-2 {
	padding-left: 2px;
	padding-right: 2px;
}

.px-3 {
	padding-left: 3px;
	padding-right: 3px;
}

.px-4 {
	padding-left: 4px;
	padding-right: 4px;
}

.px-5 {
	padding-left: 5px;
	padding-right: 5px;
}

.px-6 {
	padding-left: 6px;
	padding-right: 6px;
}

.px-7 {
	padding-left: 7px;
	padding-right: 7px;
}

.px-8 {
	padding-left: 8px;
	padding-right: 8px;
}

.px-9 {
	padding-left: 9px;
	padding-right: 9px;
}

.px-10 {
	padding-left: 10px;
	padding-right: 10px;
}

.px-11 {
	padding-left: 11px;
	padding-right: 11px;
}

.px-12 {
	padding-left: 12px;
	padding-right: 12px;
}

.px-13 {
	padding-left: 13px;
	padding-right: 13px;
}

.px-14 {
	padding-left: 14px;
	padding-right: 14px;
}

.px-15 {
	padding-left: 15px;
	padding-right: 15px;
}

.px-16 {
	padding-left: 16px;
	padding-right: 16px;
}

.px-17 {
	padding-left: 17px;
	padding-right: 17px;
}

.px-18 {
	padding-left: 18px;
	padding-right: 18px;
}

.px-19 {
	padding-left: 19px;
	padding-right: 19px;
}

.px-20 {
	padding-left: 20px;
	padding-right: 20px;
}

.px-21 {
	padding-left: 21px;
	padding-right: 21px;
}

.px-22 {
	padding-left: 22px;
	padding-right: 22px;
}

.px-23 {
	padding-left: 23px;
	padding-right: 23px;
}

.px-24 {
	padding-left: 24px;
	padding-right: 24px;
}

.px-25 {
	padding-left: 25px;
	padding-right: 25px;
}

.px-26 {
	padding-left: 26px;
	padding-right: 26px;
}

.px-27 {
	padding-left: 27px;
	padding-right: 27px;
}

.px-28 {
	padding-left: 28px;
	padding-right: 28px;
}

.px-29 {
	padding-left: 29px;
	padding-right: 29px;
}

.px-30 {
	padding-left: 30px;
	padding-right: 30px;
}

.px-31 {
	padding-left: 31px;
	padding-right: 31px;
}

.px-32 {
	padding-left: 32px;
	padding-right: 32px;
}

.px-33 {
	padding-left: 33px;
	padding-right: 33px;
}

.px-34 {
	padding-left: 34px;
	padding-right: 34px;
}

.px-35 {
	padding-left: 35px;
	padding-right: 35px;
}

.px-36 {
	padding-left: 36px;
	padding-right: 36px;
}

.px-37 {
	padding-left: 37px;
	padding-right: 37px;
}

.px-38 {
	padding-left: 38px;
	padding-right: 38px;
}

.px-39 {
	padding-left: 39px;
	padding-right: 39px;
}

.px-40 {
	padding-left: 40px;
	padding-right: 40px;
}

.px-41 {
	padding-left: 41px;
	padding-right: 41px;
}

.px-42 {
	padding-left: 42px;
	padding-right: 42px;
}

.px-43 {
	padding-left: 43px;
	padding-right: 43px;
}

.px-44 {
	padding-left: 44px;
	padding-right: 44px;
}

.px-45 {
	padding-left: 45px;
	padding-right: 45px;
}

.px-46 {
	padding-left: 46px;
	padding-right: 46px;
}

.px-47 {
	padding-left: 47px;
	padding-right: 47px;
}

.px-48 {
	padding-left: 48px;
	padding-right: 48px;
}

.px-49 {
	padding-left: 49px;
	padding-right: 49px;
}

.px-50 {
	padding-left: 50px;
	padding-right: 50px;
}

.px-n1 {
	padding-left: 4px;
	padding-right: 4rpx;
}

.px-n2 {
	padding-left: 8px;
	padding-right: 8rpx;
}

.px-n3 {
	padding-left: 12px;
	padding-right: 12rpx;
}

.px-n4 {
	padding-left: 16px;
	padding-right: 16rpx;
}

.px-n5 {
	padding-left: 20px;
	padding-right: 20rpx;
}

.px-n6 {
	padding-left: 24px;
	padding-right: 24rpx;
}

.px-n7 {
	padding-left: 28px;
	padding-right: 28rpx;
}

.px-n8 {
	padding-left: 32px;
	padding-right: 32rpx;
}

.px-n9 {
	padding-left: 36px;
	padding-right: 36rpx;
}

.px-n10 {
	padding-left: 40px;
	padding-right: 40rpx;
}

.px-n11 {
	padding-left: 44px;
	padding-right: 44rpx;
}

.px-n12 {
	padding-left: 48px;
	padding-right: 48rpx;
}

.px-n13 {
	padding-left: 52px;
	padding-right: 52rpx;
}

.px-n14 {
	padding-left: 56px;
	padding-right: 56rpx;
}

.px-n15 {
	padding-left: 60px;
	padding-right: 60rpx;
}

.px-n16 {
	padding-left: 64px;
	padding-right: 64rpx;
}

.px-n17 {
	padding-left: 68px;
	padding-right: 68rpx;
}

.px-n18 {
	padding-left: 72px;
	padding-right: 72rpx;
}

.px-n19 {
	padding-left: 76px;
	padding-right: 76rpx;
}

.px-n20 {
	padding-left: 80px;
	padding-right: 80rpx;
}

.px-n21 {
	padding-left: 84px;
	padding-right: 84rpx;
}

.px-n22 {
	padding-left: 88px;
	padding-right: 88rpx;
}

.px-n23 {
	padding-left: 92px;
	padding-right: 92rpx;
}

.px-n24 {
	padding-left: 96px;
	padding-right: 96rpx;
}

.px-n25 {
	padding-left: 100px;
	padding-right: 100rpx;
}

.py-0 {
	padding-top: 0px;
	padding-bottom: 0px;
}

.py-1 {
	padding-top: 1px;
	padding-bottom: 1px;
}

.py-2 {
	padding-top: 2px;
	padding-bottom: 2px;
}

.py-3 {
	padding-top: 3px;
	padding-bottom: 3px;
}

.py-4 {
	padding-top: 4px;
	padding-bottom: 4px;
}

.py-5 {
	padding-top: 5px;
	padding-bottom: 5px;
}

.py-6 {
	padding-top: 6px;
	padding-bottom: 6px;
}

.py-7 {
	padding-top: 7px;
	padding-bottom: 7px;
}

.py-8 {
	padding-top: 8px;
	padding-bottom: 8px;
}

.py-9 {
	padding-top: 9px;
	padding-bottom: 9px;
}

.py-10 {
	padding-top: 10px;
	padding-bottom: 10px;
}

.py-11 {
	padding-top: 11px;
	padding-bottom: 11px;
}

.py-12 {
	padding-top: 12px;
	padding-bottom: 12px;
}

.py-13 {
	padding-top: 13px;
	padding-bottom: 13px;
}

.py-14 {
	padding-top: 14px;
	padding-bottom: 14px;
}

.py-15 {
	padding-top: 15px;
	padding-bottom: 15px;
}

.py-16 {
	padding-top: 16px;
	padding-bottom: 16px;
}

.py-17 {
	padding-top: 17px;
	padding-bottom: 17px;
}

.py-18 {
	padding-top: 18px;
	padding-bottom: 18px;
}

.py-19 {
	padding-top: 19px;
	padding-bottom: 19px;
}

.py-20 {
	padding-top: 20px;
	padding-bottom: 20px;
}

.py-21 {
	padding-top: 21px;
	padding-bottom: 21px;
}

.py-22 {
	padding-top: 22px;
	padding-bottom: 22px;
}

.py-23 {
	padding-top: 23px;
	padding-bottom: 23px;
}

.py-24 {
	padding-top: 24px;
	padding-bottom: 24px;
}

.py-25 {
	padding-top: 25px;
	padding-bottom: 25px;
}

.py-26 {
	padding-top: 26px;
	padding-bottom: 26px;
}

.py-27 {
	padding-top: 27px;
	padding-bottom: 27px;
}

.py-28 {
	padding-top: 28px;
	padding-bottom: 28px;
}

.py-29 {
	padding-top: 29px;
	padding-bottom: 29px;
}

.py-30 {
	padding-top: 30px;
	padding-bottom: 30px;
}

.py-31 {
	padding-top: 31px;
	padding-bottom: 31px;
}

.py-32 {
	padding-top: 32px;
	padding-bottom: 32px;
}

.py-33 {
	padding-top: 33px;
	padding-bottom: 33px;
}

.py-34 {
	padding-top: 34px;
	padding-bottom: 34px;
}

.py-35 {
	padding-top: 35px;
	padding-bottom: 35px;
}

.py-36 {
	padding-top: 36px;
	padding-bottom: 36px;
}

.py-37 {
	padding-top: 37px;
	padding-bottom: 37px;
}

.py-38 {
	padding-top: 38px;
	padding-bottom: 38px;
}

.py-39 {
	padding-top: 39px;
	padding-bottom: 39px;
}

.py-40 {
	padding-top: 40px;
	padding-bottom: 40px;
}

.py-41 {
	padding-top: 41px;
	padding-bottom: 41px;
}

.py-42 {
	padding-top: 42px;
	padding-bottom: 42px;
}

.py-43 {
	padding-top: 43px;
	padding-bottom: 43px;
}

.py-44 {
	padding-top: 44px;
	padding-bottom: 44px;
}

.py-45 {
	padding-top: 45px;
	padding-bottom: 45px;
}

.py-46 {
	padding-top: 46px;
	padding-bottom: 46px;
}

.py-47 {
	padding-top: 47px;
	padding-bottom: 47px;
}

.py-48 {
	padding-top: 48px;
	padding-bottom: 48px;
}

.py-49 {
	padding-top: 49px;
	padding-bottom: 49px;
}

.py-50 {
	padding-top: 50px;
	padding-bottom: 50px;
}

.py-n1 {
	padding-top: 4px;
	padding-bottom: 4px;
}

.py-n2 {
	padding-top: 8px;
	padding-bottom: 8px;
}

.py-n3 {
	padding-top: 12px;
	padding-bottom: 12px;
}

.py-n4 {
	padding-top: 16px;
	padding-bottom: 16px;
}

.py-n5 {
	padding-top: 20px;
	padding-bottom: 20px;
}

.py-n6 {
	padding-top: 24px;
	padding-bottom: 24px;
}

.py-n7 {
	padding-top: 28px;
	padding-bottom: 28px;
}

.py-n8 {
	padding-top: 32px;
	padding-bottom: 32px;
}

.py-n9 {
	padding-top: 36px;
	padding-bottom: 36px;
}

.py-n10 {
	padding-top: 40px;
	padding-bottom: 40px;
}

.py-n11 {
	padding-top: 44px;
	padding-bottom: 44px;
}

.py-n12 {
	padding-top: 48px;
	padding-bottom: 48px;
}

.py-n13 {
	padding-top: 52px;
	padding-bottom: 52px;
}

.py-n14 {
	padding-top: 56px;
	padding-bottom: 56px;
}

.py-n15 {
	padding-top: 60px;
	padding-bottom: 60px;
}

.py-n16 {
	padding-top: 64px;
	padding-bottom: 64px;
}

.py-n17 {
	padding-top: 68px;
	padding-bottom: 68px;
}

.py-n18 {
	padding-top: 72px;
	padding-bottom: 72px;
}

.py-n19 {
	padding-top: 76px;
	padding-bottom: 76px;
}

.py-n20 {
	padding-top: 80px;
	padding-bottom: 80px;
}

.py-n21 {
	padding-top: 84px;
	padding-bottom: 84px;
}

.py-n22 {
	padding-top: 88px;
	padding-bottom: 88px;
}

.py-n23 {
	padding-top: 92px;
	padding-bottom: 92px;
}

.py-n24 {
	padding-top: 96px;
	padding-bottom: 96px;
}

.py-n25 {
	padding-top: 100px;
	padding-bottom: 100px;
}

.ma-0 {
	margin: 0px;
}

.ma-1 {
	margin: 1px;
}

.ma-2 {
	margin: 2px;
}

.ma-3 {
	margin: 3px;
}

.ma-4 {
	margin: 4px;
}

.ma-5 {
	margin: 5px;
}

.ma-6 {
	margin: 6px;
}

.ma-7 {
	margin: 7px;
}

.ma-8 {
	margin: 8px;
}

.ma-9 {
	margin: 9px;
}

.ma-10 {
	margin: 10px;
}

.ma-11 {
	margin: 11px;
}

.ma-12 {
	margin: 12px;
}

.ma-13 {
	margin: 13px;
}

.ma-14 {
	margin: 14px;
}

.ma-15 {
	margin: 15px;
}

.ma-16 {
	margin: 16px;
}

.ma-17 {
	margin: 17px;
}

.ma-18 {
	margin: 18px;
}

.ma-19 {
	margin: 19px;
}

.ma-20 {
	margin: 20px;
}

.ma-21 {
	margin: 21px;
}

.ma-22 {
	margin: 22px;
}

.ma-23 {
	margin: 23px;
}

.ma-24 {
	margin: 24px;
}

.ma-25 {
	margin: 25px;
}

.ma-26 {
	margin: 26px;
}

.ma-27 {
	margin: 27px;
}

.ma-28 {
	margin: 28px;
}

.ma-29 {
	margin: 29px;
}

.ma-30 {
	margin: 30px;
}

.ma-31 {
	margin: 31px;
}

.ma-32 {
	margin: 32px;
}

.ma-33 {
	margin: 33px;
}

.ma-34 {
	margin: 34px;
}

.ma-35 {
	margin: 35px;
}

.ma-36 {
	margin: 36px;
}

.ma-37 {
	margin: 37px;
}

.ma-38 {
	margin: 38px;
}

.ma-39 {
	margin: 39px;
}

.ma-40 {
	margin: 40px;
}

.ma-41 {
	margin: 41px;
}

.ma-42 {
	margin: 42px;
}

.ma-43 {
	margin: 43px;
}

.ma-44 {
	margin: 44px;
}

.ma-45 {
	margin: 45px;
}

.ma-46 {
	margin: 46px;
}

.ma-47 {
	margin: 47px;
}

.ma-48 {
	margin: 48px;
}

.ma-49 {
	margin: 49px;
}

.ma-50 {
	margin: 50px;
}

.ma-n1 {
	margin: 4px;
}

.ma-n2 {
	margin: 8px;
}

.ma-n3 {
	margin: 12px;
}

.ma-n4 {
	margin: 16px;
}

.ma-n5 {
	margin: 20px;
}

.ma-n6 {
	margin: 24px;
}

.ma-n7 {
	margin: 28px;
}

.ma-n8 {
	margin: 32px;
}

.ma-n9 {
	margin: 36px;
}

.ma-n10 {
	margin: 40px;
}

.ma-n11 {
	margin: 44px;
}

.ma-n12 {
	margin: 48px;
}

.ma-n13 {
	margin: 52px;
}

.ma-n14 {
	margin: 56px;
}

.ma-n15 {
	margin: 60px;
}

.ma-n16 {
	margin: 64px;
}

.ma-n17 {
	margin: 68px;
}

.ma-n18 {
	margin: 72px;
}

.ma-n19 {
	margin: 76px;
}

.ma-n20 {
	margin: 80px;
}

.ma-n21 {
	margin: 84px;
}

.ma-n22 {
	margin: 88px;
}

.ma-n23 {
	margin: 92px;
}

.ma-n24 {
	margin: 96px;
}

.ma-n25 {
	margin: 100px;
}

.mt-0 {
	margin-top: 0px;
}

.mt--0 {
	margin-top: -0px;
}

.mt-1 {
	margin-top: 1px;
}

.mt--1 {
	margin-top: -1px;
}

.mt-2 {
	margin-top: 2px;
}

.mt--2 {
	margin-top: -2px;
}

.mt-3 {
	margin-top: 3px;
}

.mt--3 {
	margin-top: -3px;
}

.mt-4 {
	margin-top: 4px;
}

.mt--4 {
	margin-top: -4px;
}

.mt-5 {
	margin-top: 5px;
}

.mt--5 {
	margin-top: -5px;
}

.mt-6 {
	margin-top: 6px;
}

.mt--6 {
	margin-top: -6px;
}

.mt-7 {
	margin-top: 7px;
}

.mt--7 {
	margin-top: -7px;
}

.mt-8 {
	margin-top: 8px;
}

.mt--8 {
	margin-top: -8px;
}

.mt-9 {
	margin-top: 9px;
}

.mt--9 {
	margin-top: -9px;
}

.mt-10 {
	margin-top: 10px;
}

.mt--10 {
	margin-top: -10px;
}

.mt-11 {
	margin-top: 11px;
}

.mt--11 {
	margin-top: -11px;
}

.mt-12 {
	margin-top: 12px;
}

.mt--12 {
	margin-top: -12px;
}

.mt-13 {
	margin-top: 13px;
}

.mt--13 {
	margin-top: -13px;
}

.mt-14 {
	margin-top: 14px;
}

.mt--14 {
	margin-top: -14px;
}

.mt-15 {
	margin-top: 15px;
}

.mt--15 {
	margin-top: -15px;
}

.mt-16 {
	margin-top: 16px;
}

.mt--16 {
	margin-top: -16px;
}

.mt-17 {
	margin-top: 17px;
}

.mt--17 {
	margin-top: -17px;
}

.mt-18 {
	margin-top: 18px;
}

.mt--18 {
	margin-top: -18px;
}

.mt-19 {
	margin-top: 19px;
}

.mt--19 {
	margin-top: -19px;
}

.mt-20 {
	margin-top: 20px;
}

.mt--20 {
	margin-top: -20px;
}

.mt-21 {
	margin-top: 21px;
}

.mt--21 {
	margin-top: -21px;
}

.mt-22 {
	margin-top: 22px;
}

.mt--22 {
	margin-top: -22px;
}

.mt-23 {
	margin-top: 23px;
}

.mt--23 {
	margin-top: -23px;
}

.mt-24 {
	margin-top: 24px;
}

.mt--24 {
	margin-top: -24px;
}

.mt-25 {
	margin-top: 25px;
}

.mt--25 {
	margin-top: -25px;
}

.mt-26 {
	margin-top: 26px;
}

.mt--26 {
	margin-top: -26px;
}

.mt-27 {
	margin-top: 27px;
}

.mt--27 {
	margin-top: -27px;
}

.mt-28 {
	margin-top: 28px;
}

.mt--28 {
	margin-top: -28px;
}

.mt-29 {
	margin-top: 29px;
}

.mt--29 {
	margin-top: -29px;
}

.mt-30 {
	margin-top: 30px;
}

.mt--30 {
	margin-top: -30px;
}

.mt-31 {
	margin-top: 31px;
}

.mt--31 {
	margin-top: -31px;
}

.mt-32 {
	margin-top: 32px;
}

.mt--32 {
	margin-top: -32px;
}

.mt-33 {
	margin-top: 33px;
}

.mt--33 {
	margin-top: -33px;
}

.mt-34 {
	margin-top: 34px;
}

.mt--34 {
	margin-top: -34px;
}

.mt-35 {
	margin-top: 35px;
}

.mt--35 {
	margin-top: -35px;
}

.mt-36 {
	margin-top: 36px;
}

.mt--36 {
	margin-top: -36px;
}

.mt-37 {
	margin-top: 37px;
}

.mt--37 {
	margin-top: -37px;
}

.mt-38 {
	margin-top: 38px;
}

.mt--38 {
	margin-top: -38px;
}

.mt-39 {
	margin-top: 39px;
}

.mt--39 {
	margin-top: -39px;
}

.mt-40 {
	margin-top: 40px;
}

.mt--40 {
	margin-top: -40px;
}

.mt-41 {
	margin-top: 41px;
}

.mt--41 {
	margin-top: -41px;
}

.mt-42 {
	margin-top: 42px;
}

.mt--42 {
	margin-top: -42px;
}

.mt-43 {
	margin-top: 43px;
}

.mt--43 {
	margin-top: -43px;
}

.mt-44 {
	margin-top: 44px;
}

.mt--44 {
	margin-top: -44px;
}

.mt-45 {
	margin-top: 45px;
}

.mt--45 {
	margin-top: -45px;
}

.mt-46 {
	margin-top: 46px;
}

.mt--46 {
	margin-top: -46px;
}

.mt-47 {
	margin-top: 47px;
}

.mt--47 {
	margin-top: -47px;
}

.mt-48 {
	margin-top: 48px;
}

.mt--48 {
	margin-top: -48px;
}

.mt-49 {
	margin-top: 49px;
}

.mt--49 {
	margin-top: -49px;
}

.mt-50 {
	margin-top: 50px;
}

.mt--50 {
	margin-top: -50px;
}

.mt-n1 {
	margin-top: 4px;
}

.mt--n1 {
	margin-top: -4px;
}

.mt-n2 {
	margin-top: 8px;
}

.mt--n2 {
	margin-top: -8px;
}

.mt-n3 {
	margin-top: 12px;
}

.mt--n3 {
	margin-top: -12px;
}

.mt-n4 {
	margin-top: 16px;
}

.mt--n4 {
	margin-top: -16px;
}

.mt-n5 {
	margin-top: 20px;
}

.mt--n5 {
	margin-top: -20px;
}

.mt-n6 {
	margin-top: 24px;
}

.mt--n6 {
	margin-top: -24px;
}

.mt-n7 {
	margin-top: 28px;
}

.mt--n7 {
	margin-top: -28px;
}

.mt-n8 {
	margin-top: 32px;
}

.mt--n8 {
	margin-top: -32px;
}

.mt-n9 {
	margin-top: 36px;
}

.mt--n9 {
	margin-top: -36px;
}

.mt-n10 {
	margin-top: 40px;
}

.mt--n10 {
	margin-top: -40px;
}

.mt-n11 {
	margin-top: 44px;
}

.mt--n11 {
	margin-top: -44px;
}

.mt-n12 {
	margin-top: 48px;
}

.mt--n12 {
	margin-top: -48px;
}

.mt-n13 {
	margin-top: 52px;
}

.mt--n13 {
	margin-top: -52px;
}

.mt-n14 {
	margin-top: 56px;
}

.mt--n14 {
	margin-top: -56px;
}

.mt-n15 {
	margin-top: 60px;
}

.mt--n15 {
	margin-top: -60px;
}

.mt-n16 {
	margin-top: 64px;
}

.mt--n16 {
	margin-top: -64px;
}

.mt-n17 {
	margin-top: 68px;
}

.mt--n17 {
	margin-top: -68px;
}

.mt-n18 {
	margin-top: 72px;
}

.mt--n18 {
	margin-top: -72px;
}

.mt-n19 {
	margin-top: 76px;
}

.mt--n19 {
	margin-top: -76px;
}

.mt-n20 {
	margin-top: 80px;
}

.mt--n20 {
	margin-top: -80px;
}

.mt-n21 {
	margin-top: 84px;
}

.mt--n21 {
	margin-top: -84px;
}

.mt-n22 {
	margin-top: 88px;
}

.mt--n22 {
	margin-top: -88px;
}

.mt-n23 {
	margin-top: 92px;
}

.mt--n23 {
	margin-top: -92px;
}

.mt-n24 {
	margin-top: 96px;
}

.mt--n24 {
	margin-top: -96px;
}

.mt-n25 {
	margin-top: 100px;
}

.mt--n25 {
	margin-top: -100px;
}

.mr-0 {
	margin-right: 0px;
}

.mr--0 {
	margin-right: -0px;
}

.mr-1 {
	margin-right: 1px;
}

.mr--1 {
	margin-right: -1px;
}

.mr-2 {
	margin-right: 2px;
}

.mr--2 {
	margin-right: -2px;
}

.mr-3 {
	margin-right: 3px;
}

.mr--3 {
	margin-right: -3px;
}

.mr-4 {
	margin-right: 4px;
}

.mr--4 {
	margin-right: -4px;
}

.mr-5 {
	margin-right: 5px;
}

.mr--5 {
	margin-right: -5px;
}

.mr-6 {
	margin-right: 6px;
}

.mr--6 {
	margin-right: -6px;
}

.mr-7 {
	margin-right: 7px;
}

.mr--7 {
	margin-right: -7px;
}

.mr-8 {
	margin-right: 8px;
}

.mr--8 {
	margin-right: -8px;
}

.mr-9 {
	margin-right: 9px;
}

.mr--9 {
	margin-right: -9px;
}

.mr-10 {
	margin-right: 10px;
}

.mr--10 {
	margin-right: -10px;
}

.mr-11 {
	margin-right: 11px;
}

.mr--11 {
	margin-right: -11px;
}

.mr-12 {
	margin-right: 12px;
}

.mr--12 {
	margin-right: -12px;
}

.mr-13 {
	margin-right: 13px;
}

.mr--13 {
	margin-right: -13px;
}

.mr-14 {
	margin-right: 14px;
}

.mr--14 {
	margin-right: -14px;
}

.mr-15 {
	margin-right: 15px;
}

.mr--15 {
	margin-right: -15px;
}

.mr-16 {
	margin-right: 16px;
}

.mr--16 {
	margin-right: -16px;
}

.mr-17 {
	margin-right: 17px;
}

.mr--17 {
	margin-right: -17px;
}

.mr-18 {
	margin-right: 18px;
}

.mr--18 {
	margin-right: -18px;
}

.mr-19 {
	margin-right: 19px;
}

.mr--19 {
	margin-right: -19px;
}

.mr-20 {
	margin-right: 20px;
}

.mr--20 {
	margin-right: -20px;
}

.mr-21 {
	margin-right: 21px;
}

.mr--21 {
	margin-right: -21px;
}

.mr-22 {
	margin-right: 22px;
}

.mr--22 {
	margin-right: -22px;
}

.mr-23 {
	margin-right: 23px;
}

.mr--23 {
	margin-right: -23px;
}

.mr-24 {
	margin-right: 24px;
}

.mr--24 {
	margin-right: -24px;
}

.mr-25 {
	margin-right: 25px;
}

.mr--25 {
	margin-right: -25px;
}

.mr-26 {
	margin-right: 26px;
}

.mr--26 {
	margin-right: -26px;
}

.mr-27 {
	margin-right: 27px;
}

.mr--27 {
	margin-right: -27px;
}

.mr-28 {
	margin-right: 28px;
}

.mr--28 {
	margin-right: -28px;
}

.mr-29 {
	margin-right: 29px;
}

.mr--29 {
	margin-right: -29px;
}

.mr-30 {
	margin-right: 30px;
}

.mr--30 {
	margin-right: -30px;
}

.mr-31 {
	margin-right: 31px;
}

.mr--31 {
	margin-right: -31px;
}

.mr-32 {
	margin-right: 32px;
}

.mr--32 {
	margin-right: -32px;
}

.mr-33 {
	margin-right: 33px;
}

.mr--33 {
	margin-right: -33px;
}

.mr-34 {
	margin-right: 34px;
}

.mr--34 {
	margin-right: -34px;
}

.mr-35 {
	margin-right: 35px;
}

.mr--35 {
	margin-right: -35px;
}

.mr-36 {
	margin-right: 36px;
}

.mr--36 {
	margin-right: -36px;
}

.mr-37 {
	margin-right: 37px;
}

.mr--37 {
	margin-right: -37px;
}

.mr-38 {
	margin-right: 38px;
}

.mr--38 {
	margin-right: -38px;
}

.mr-39 {
	margin-right: 39px;
}

.mr--39 {
	margin-right: -39px;
}

.mr-40 {
	margin-right: 40px;
}

.mr--40 {
	margin-right: -40px;
}

.mr-41 {
	margin-right: 41px;
}

.mr--41 {
	margin-right: -41px;
}

.mr-42 {
	margin-right: 42px;
}

.mr--42 {
	margin-right: -42px;
}

.mr-43 {
	margin-right: 43px;
}

.mr--43 {
	margin-right: -43px;
}

.mr-44 {
	margin-right: 44px;
}

.mr--44 {
	margin-right: -44px;
}

.mr-45 {
	margin-right: 45px;
}

.mr--45 {
	margin-right: -45px;
}

.mr-46 {
	margin-right: 46px;
}

.mr--46 {
	margin-right: -46px;
}

.mr-47 {
	margin-right: 47px;
}

.mr--47 {
	margin-right: -47px;
}

.mr-48 {
	margin-right: 48px;
}

.mr--48 {
	margin-right: -48px;
}

.mr-49 {
	margin-right: 49px;
}

.mr--49 {
	margin-right: -49px;
}

.mr-50 {
	margin-right: 50px;
}

.mr--50 {
	margin-right: -50px;
}

.mr-n1 {
	margin-right: 4px;
}

.mr--n1 {
	margin-right: -4px;
}

.mr-n2 {
	margin-right: 8px;
}

.mr--n2 {
	margin-right: -8px;
}

.mr-n3 {
	margin-right: 12px;
}

.mr--n3 {
	margin-right: -12px;
}

.mr-n4 {
	margin-right: 16px;
}

.mr--n4 {
	margin-right: -16px;
}

.mr-n5 {
	margin-right: 20px;
}

.mr--n5 {
	margin-right: -20px;
}

.mr-n6 {
	margin-right: 24px;
}

.mr--n6 {
	margin-right: -24px;
}

.mr-n7 {
	margin-right: 28px;
}

.mr--n7 {
	margin-right: -28px;
}

.mr-n8 {
	margin-right: 32px;
}

.mr--n8 {
	margin-right: -32px;
}

.mr-n9 {
	margin-right: 36px;
}

.mr--n9 {
	margin-right: -36px;
}

.mr-n10 {
	margin-right: 40px;
}

.mr--n10 {
	margin-right: -40px;
}

.mr-n11 {
	margin-right: 44px;
}

.mr--n11 {
	margin-right: -44px;
}

.mr-n12 {
	margin-right: 48px;
}

.mr--n12 {
	margin-right: -48px;
}

.mr-n13 {
	margin-right: 52px;
}

.mr--n13 {
	margin-right: -52px;
}

.mr-n14 {
	margin-right: 56px;
}

.mr--n14 {
	margin-right: -56px;
}

.mr-n15 {
	margin-right: 60px;
}

.mr--n15 {
	margin-right: -60px;
}

.mr-n16 {
	margin-right: 64px;
}

.mr--n16 {
	margin-right: -64px;
}

.mr-n17 {
	margin-right: 68px;
}

.mr--n17 {
	margin-right: -68px;
}

.mr-n18 {
	margin-right: 72px;
}

.mr--n18 {
	margin-right: -72px;
}

.mr-n19 {
	margin-right: 76px;
}

.mr--n19 {
	margin-right: -76px;
}

.mr-n20 {
	margin-right: 80px;
}

.mr--n20 {
	margin-right: -80px;
}

.mr-n21 {
	margin-right: 84px;
}

.mr--n21 {
	margin-right: -84px;
}

.mr-n22 {
	margin-right: 88px;
}

.mr--n22 {
	margin-right: -88px;
}

.mr-n23 {
	margin-right: 92px;
}

.mr--n23 {
	margin-right: -92px;
}

.mr-n24 {
	margin-right: 96px;
}

.mr--n24 {
	margin-right: -96px;
}

.mr-n25 {
	margin-right: 100px;
}

.mr--n25 {
	margin-right: -100px;
}

.mb-0 {
	margin-bottom: 0px;
}

.mb--0 {
	margin-bottom: -0px;
}

.mb-1 {
	margin-bottom: 1px;
}

.mb--1 {
	margin-bottom: -1px;
}

.mb-2 {
	margin-bottom: 2px;
}

.mb--2 {
	margin-bottom: -2px;
}

.mb-3 {
	margin-bottom: 3px;
}

.mb--3 {
	margin-bottom: -3px;
}

.mb-4 {
	margin-bottom: 4px;
}

.mb--4 {
	margin-bottom: -4px;
}

.mb-5 {
	margin-bottom: 5px;
}

.mb--5 {
	margin-bottom: -5px;
}

.mb-6 {
	margin-bottom: 6px;
}

.mb--6 {
	margin-bottom: -6px;
}

.mb-7 {
	margin-bottom: 7px;
}

.mb--7 {
	margin-bottom: -7px;
}

.mb-8 {
	margin-bottom: 8px;
}

.mb--8 {
	margin-bottom: -8px;
}

.mb-9 {
	margin-bottom: 9px;
}

.mb--9 {
	margin-bottom: -9px;
}

.mb-10 {
	margin-bottom: 10px;
}

.mb--10 {
	margin-bottom: -10px;
}

.mb-11 {
	margin-bottom: 11px;
}

.mb--11 {
	margin-bottom: -11px;
}

.mb-12 {
	margin-bottom: 12px;
}

.mb--12 {
	margin-bottom: -12px;
}

.mb-13 {
	margin-bottom: 13px;
}

.mb--13 {
	margin-bottom: -13px;
}

.mb-14 {
	margin-bottom: 14px;
}

.mb--14 {
	margin-bottom: -14px;
}

.mb-15 {
	margin-bottom: 15px;
}

.mb--15 {
	margin-bottom: -15px;
}

.mb-16 {
	margin-bottom: 16px;
}

.mb--16 {
	margin-bottom: -16px;
}

.mb-17 {
	margin-bottom: 17px;
}

.mb--17 {
	margin-bottom: -17px;
}

.mb-18 {
	margin-bottom: 18px;
}

.mb--18 {
	margin-bottom: -18px;
}

.mb-19 {
	margin-bottom: 19px;
}

.mb--19 {
	margin-bottom: -19px;
}

.mb-20 {
	margin-bottom: 20px;
}

.mb--20 {
	margin-bottom: -20px;
}

.mb-21 {
	margin-bottom: 21px;
}

.mb--21 {
	margin-bottom: -21px;
}

.mb-22 {
	margin-bottom: 22px;
}

.mb--22 {
	margin-bottom: -22px;
}

.mb-23 {
	margin-bottom: 23px;
}

.mb--23 {
	margin-bottom: -23px;
}

.mb-24 {
	margin-bottom: 24px;
}

.mb--24 {
	margin-bottom: -24px;
}

.mb-25 {
	margin-bottom: 25px;
}

.mb--25 {
	margin-bottom: -25px;
}

.mb-26 {
	margin-bottom: 26px;
}

.mb--26 {
	margin-bottom: -26px;
}

.mb-27 {
	margin-bottom: 27px;
}

.mb--27 {
	margin-bottom: -27px;
}

.mb-28 {
	margin-bottom: 28px;
}

.mb--28 {
	margin-bottom: -28px;
}

.mb-29 {
	margin-bottom: 29px;
}

.mb--29 {
	margin-bottom: -29px;
}

.mb-30 {
	margin-bottom: 30px;
}

.mb--30 {
	margin-bottom: -30px;
}

.mb-31 {
	margin-bottom: 31px;
}

.mb--31 {
	margin-bottom: -31px;
}

.mb-32 {
	margin-bottom: 32px;
}

.mb--32 {
	margin-bottom: -32px;
}

.mb-33 {
	margin-bottom: 33px;
}

.mb--33 {
	margin-bottom: -33px;
}

.mb-34 {
	margin-bottom: 34px;
}

.mb--34 {
	margin-bottom: -34px;
}

.mb-35 {
	margin-bottom: 35px;
}

.mb--35 {
	margin-bottom: -35px;
}

.mb-36 {
	margin-bottom: 36px;
}

.mb--36 {
	margin-bottom: -36px;
}

.mb-37 {
	margin-bottom: 37px;
}

.mb--37 {
	margin-bottom: -37px;
}

.mb-38 {
	margin-bottom: 38px;
}

.mb--38 {
	margin-bottom: -38px;
}

.mb-39 {
	margin-bottom: 39px;
}

.mb--39 {
	margin-bottom: -39px;
}

.mb-40 {
	margin-bottom: 40px;
}

.mb--40 {
	margin-bottom: -40px;
}

.mb-41 {
	margin-bottom: 41px;
}

.mb--41 {
	margin-bottom: -41px;
}

.mb-42 {
	margin-bottom: 42px;
}

.mb--42 {
	margin-bottom: -42px;
}

.mb-43 {
	margin-bottom: 43px;
}

.mb--43 {
	margin-bottom: -43px;
}

.mb-44 {
	margin-bottom: 44px;
}

.mb--44 {
	margin-bottom: -44px;
}

.mb-45 {
	margin-bottom: 45px;
}

.mb--45 {
	margin-bottom: -45px;
}

.mb-46 {
	margin-bottom: 46px;
}

.mb--46 {
	margin-bottom: -46px;
}

.mb-47 {
	margin-bottom: 47px;
}

.mb--47 {
	margin-bottom: -47px;
}

.mb-48 {
	margin-bottom: 48px;
}

.mb--48 {
	margin-bottom: -48px;
}

.mb-49 {
	margin-bottom: 49px;
}

.mb--49 {
	margin-bottom: -49px;
}

.mb-50 {
	margin-bottom: 50px;
}

.mb--50 {
	margin-bottom: -50px;
}

.mb-n1 {
	margin-bottom: 4px;
}

.mb--n1 {
	margin-bottom: -4px;
}

.mb-n2 {
	margin-bottom: 8px;
}

.mb--n2 {
	margin-bottom: -8px;
}

.mb-n3 {
	margin-bottom: 12px;
}

.mb--n3 {
	margin-bottom: -12px;
}

.mb-n4 {
	margin-bottom: 16px;
}

.mb--n4 {
	margin-bottom: -16px;
}

.mb-n5 {
	margin-bottom: 20px;
}

.mb--n5 {
	margin-bottom: -20px;
}

.mb-n6 {
	margin-bottom: 24px;
}

.mb--n6 {
	margin-bottom: -24px;
}

.mb-n7 {
	margin-bottom: 28px;
}

.mb--n7 {
	margin-bottom: -28px;
}

.mb-n8 {
	margin-bottom: 32px;
}

.mb--n8 {
	margin-bottom: -32px;
}

.mb-n9 {
	margin-bottom: 36px;
}

.mb--n9 {
	margin-bottom: -36px;
}

.mb-n10 {
	margin-bottom: 40px;
}

.mb--n10 {
	margin-bottom: -40px;
}

.mb-n11 {
	margin-bottom: 44px;
}

.mb--n11 {
	margin-bottom: -44px;
}

.mb-n12 {
	margin-bottom: 48px;
}

.mb--n12 {
	margin-bottom: -48px;
}

.mb-n13 {
	margin-bottom: 52px;
}

.mb--n13 {
	margin-bottom: -52px;
}

.mb-n14 {
	margin-bottom: 56px;
}

.mb--n14 {
	margin-bottom: -56px;
}

.mb-n15 {
	margin-bottom: 60px;
}

.mb--n15 {
	margin-bottom: -60px;
}

.mb-n16 {
	margin-bottom: 64px;
}

.mb--n16 {
	margin-bottom: -64px;
}

.mb-n17 {
	margin-bottom: 68px;
}

.mb--n17 {
	margin-bottom: -68px;
}

.mb-n18 {
	margin-bottom: 72px;
}

.mb--n18 {
	margin-bottom: -72px;
}

.mb-n19 {
	margin-bottom: 76px;
}

.mb--n19 {
	margin-bottom: -76px;
}

.mb-n20 {
	margin-bottom: 80px;
}

.mb--n20 {
	margin-bottom: -80px;
}

.mb-n21 {
	margin-bottom: 84px;
}

.mb--n21 {
	margin-bottom: -84px;
}

.mb-n22 {
	margin-bottom: 88px;
}

.mb--n22 {
	margin-bottom: -88px;
}

.mb-n23 {
	margin-bottom: 92px;
}

.mb--n23 {
	margin-bottom: -92px;
}

.mb-n24 {
	margin-bottom: 96px;
}

.mb--n24 {
	margin-bottom: -96px;
}

.mb-n25 {
	margin-bottom: 100px;
}

.mb--n25 {
	margin-bottom: -100px;
}

.ml-0 {
	margin-left: 0px;
}

.ml--0 {
	margin-left: -0px;
}

.ml-1 {
	margin-left: 1px;
}

.ml--1 {
	margin-left: -1px;
}

.ml-2 {
	margin-left: 2px;
}

.ml--2 {
	margin-left: -2px;
}

.ml-3 {
	margin-left: 3px;
}

.ml--3 {
	margin-left: -3px;
}

.ml-4 {
	margin-left: 4px;
}

.ml--4 {
	margin-left: -4px;
}

.ml-5 {
	margin-left: 5px;
}

.ml--5 {
	margin-left: -5px;
}

.ml-6 {
	margin-left: 6px;
}

.ml--6 {
	margin-left: -6px;
}

.ml-7 {
	margin-left: 7px;
}

.ml--7 {
	margin-left: -7px;
}

.ml-8 {
	margin-left: 8px;
}

.ml--8 {
	margin-left: -8px;
}

.ml-9 {
	margin-left: 9px;
}

.ml--9 {
	margin-left: -9px;
}

.ml-10 {
	margin-left: 10px;
}

.ml--10 {
	margin-left: -10px;
}

.ml-11 {
	margin-left: 11px;
}

.ml--11 {
	margin-left: -11px;
}

.ml-12 {
	margin-left: 12px;
}

.ml--12 {
	margin-left: -12px;
}

.ml-13 {
	margin-left: 13px;
}

.ml--13 {
	margin-left: -13px;
}

.ml-14 {
	margin-left: 14px;
}

.ml--14 {
	margin-left: -14px;
}

.ml-15 {
	margin-left: 15px;
}

.ml--15 {
	margin-left: -15px;
}

.ml-16 {
	margin-left: 16px;
}

.ml--16 {
	margin-left: -16px;
}

.ml-17 {
	margin-left: 17px;
}

.ml--17 {
	margin-left: -17px;
}

.ml-18 {
	margin-left: 18px;
}

.ml--18 {
	margin-left: -18px;
}

.ml-19 {
	margin-left: 19px;
}

.ml--19 {
	margin-left: -19px;
}

.ml-20 {
	margin-left: 20px;
}

.ml--20 {
	margin-left: -20px;
}

.ml-21 {
	margin-left: 21px;
}

.ml--21 {
	margin-left: -21px;
}

.ml-22 {
	margin-left: 22px;
}

.ml--22 {
	margin-left: -22px;
}

.ml-23 {
	margin-left: 23px;
}

.ml--23 {
	margin-left: -23px;
}

.ml-24 {
	margin-left: 24px;
}

.ml--24 {
	margin-left: -24px;
}

.ml-25 {
	margin-left: 25px;
}

.ml--25 {
	margin-left: -25px;
}

.ml-26 {
	margin-left: 26px;
}

.ml--26 {
	margin-left: -26px;
}

.ml-27 {
	margin-left: 27px;
}

.ml--27 {
	margin-left: -27px;
}

.ml-28 {
	margin-left: 28px;
}

.ml--28 {
	margin-left: -28px;
}

.ml-29 {
	margin-left: 29px;
}

.ml--29 {
	margin-left: -29px;
}

.ml-30 {
	margin-left: 30px;
}

.ml--30 {
	margin-left: -30px;
}

.ml-31 {
	margin-left: 31px;
}

.ml--31 {
	margin-left: -31px;
}

.ml-32 {
	margin-left: 32px;
}

.ml--32 {
	margin-left: -32px;
}

.ml-33 {
	margin-left: 33px;
}

.ml--33 {
	margin-left: -33px;
}

.ml-34 {
	margin-left: 34px;
}

.ml--34 {
	margin-left: -34px;
}

.ml-35 {
	margin-left: 35px;
}

.ml--35 {
	margin-left: -35px;
}

.ml-36 {
	margin-left: 36px;
}

.ml--36 {
	margin-left: -36px;
}

.ml-37 {
	margin-left: 37px;
}

.ml--37 {
	margin-left: -37px;
}

.ml-38 {
	margin-left: 38px;
}

.ml--38 {
	margin-left: -38px;
}

.ml-39 {
	margin-left: 39px;
}

.ml--39 {
	margin-left: -39px;
}

.ml-40 {
	margin-left: 40px;
}

.ml--40 {
	margin-left: -40px;
}

.ml-41 {
	margin-left: 41px;
}

.ml--41 {
	margin-left: -41px;
}

.ml-42 {
	margin-left: 42px;
}

.ml--42 {
	margin-left: -42px;
}

.ml-43 {
	margin-left: 43px;
}

.ml--43 {
	margin-left: -43px;
}

.ml-44 {
	margin-left: 44px;
}

.ml--44 {
	margin-left: -44px;
}

.ml-45 {
	margin-left: 45px;
}

.ml--45 {
	margin-left: -45px;
}

.ml-46 {
	margin-left: 46px;
}

.ml--46 {
	margin-left: -46px;
}

.ml-47 {
	margin-left: 47px;
}

.ml--47 {
	margin-left: -47px;
}

.ml-48 {
	margin-left: 48px;
}

.ml--48 {
	margin-left: -48px;
}

.ml-49 {
	margin-left: 49px;
}

.ml--49 {
	margin-left: -49px;
}

.ml-50 {
	margin-left: 50px;
}

.ml--50 {
	margin-left: -50px;
}

.ml-n1 {
	margin-left: 4px;
}

.ml--n1 {
	margin-left: -4px;
}

.ml-n2 {
	margin-left: 8px;
}

.ml--n2 {
	margin-left: -8px;
}

.ml-n3 {
	margin-left: 12px;
}

.ml--n3 {
	margin-left: -12px;
}

.ml-n4 {
	margin-left: 16px;
}

.ml--n4 {
	margin-left: -16px;
}

.ml-n5 {
	margin-left: 20px;
}

.ml--n5 {
	margin-left: -20px;
}

.ml-n6 {
	margin-left: 24px;
}

.ml--n6 {
	margin-left: -24px;
}

.ml-n7 {
	margin-left: 28px;
}

.ml--n7 {
	margin-left: -28px;
}

.ml-n8 {
	margin-left: 32px;
}

.ml--n8 {
	margin-left: -32px;
}

.ml-n9 {
	margin-left: 36px;
}

.ml--n9 {
	margin-left: -36px;
}

.ml-n10 {
	margin-left: 40px;
}

.ml--n10 {
	margin-left: -40px;
}

.ml-n11 {
	margin-left: 44px;
}

.ml--n11 {
	margin-left: -44px;
}

.ml-n12 {
	margin-left: 48px;
}

.ml--n12 {
	margin-left: -48px;
}

.ml-n13 {
	margin-left: 52px;
}

.ml--n13 {
	margin-left: -52px;
}

.ml-n14 {
	margin-left: 56px;
}

.ml--n14 {
	margin-left: -56px;
}

.ml-n15 {
	margin-left: 60px;
}

.ml--n15 {
	margin-left: -60px;
}

.ml-n16 {
	margin-left: 64px;
}

.ml--n16 {
	margin-left: -64px;
}

.ml-n17 {
	margin-left: 68px;
}

.ml--n17 {
	margin-left: -68px;
}

.ml-n18 {
	margin-left: 72px;
}

.ml--n18 {
	margin-left: -72px;
}

.ml-n19 {
	margin-left: 76px;
}

.ml--n19 {
	margin-left: -76px;
}

.ml-n20 {
	margin-left: 80px;
}

.ml--n20 {
	margin-left: -80px;
}

.ml-n21 {
	margin-left: 84px;
}

.ml--n21 {
	margin-left: -84px;
}

.ml-n22 {
	margin-left: 88px;
}

.ml--n22 {
	margin-left: -88px;
}

.ml-n23 {
	margin-left: 92px;
}

.ml--n23 {
	margin-left: -92px;
}

.ml-n24 {
	margin-left: 96px;
}

.ml--n24 {
	margin-left: -96px;
}

.ml-n25 {
	margin-left: 100px;
}

.ml--n25 {
	margin-left: -100px;
}

.mx-0 {
	margin-left: 0px;
	margin-right: 0px;
}

.mx-1 {
	margin-left: 1px;
	margin-right: 1px;
}

.mx-2 {
	margin-left: 2px;
	margin-right: 2px;
}

.mx-3 {
	margin-left: 3px;
	margin-right: 3px;
}

.mx-4 {
	margin-left: 4px;
	margin-right: 4px;
}

.mx-5 {
	margin-left: 5px;
	margin-right: 5px;
}

.mx-6 {
	margin-left: 6px;
	margin-right: 6px;
}

.mx-7 {
	margin-left: 7px;
	margin-right: 7px;
}

.mx-8 {
	margin-left: 8px;
	margin-right: 8px;
}

.mx-9 {
	margin-left: 9px;
	margin-right: 9px;
}

.mx-10 {
	margin-left: 10px;
	margin-right: 10px;
}

.mx-11 {
	margin-left: 11px;
	margin-right: 11px;
}

.mx-12 {
	margin-left: 12px;
	margin-right: 12px;
}

.mx-13 {
	margin-left: 13px;
	margin-right: 13px;
}

.mx-14 {
	margin-left: 14px;
	margin-right: 14px;
}

.mx-15 {
	margin-left: 15px;
	margin-right: 15px;
}

.mx-16 {
	margin-left: 16px;
	margin-right: 16px;
}

.mx-17 {
	margin-left: 17px;
	margin-right: 17px;
}

.mx-18 {
	margin-left: 18px;
	margin-right: 18px;
}

.mx-19 {
	margin-left: 19px;
	margin-right: 19px;
}

.mx-20 {
	margin-left: 20px;
	margin-right: 20px;
}

.mx-21 {
	margin-left: 21px;
	margin-right: 21px;
}

.mx-22 {
	margin-left: 22px;
	margin-right: 22px;
}

.mx-23 {
	margin-left: 23px;
	margin-right: 23px;
}

.mx-24 {
	margin-left: 24px;
	margin-right: 24px;
}

.mx-25 {
	margin-left: 25px;
	margin-right: 25px;
}

.mx-26 {
	margin-left: 26px;
	margin-right: 26px;
}

.mx-27 {
	margin-left: 27px;
	margin-right: 27px;
}

.mx-28 {
	margin-left: 28px;
	margin-right: 28px;
}

.mx-29 {
	margin-left: 29px;
	margin-right: 29px;
}

.mx-30 {
	margin-left: 30px;
	margin-right: 30px;
}

.mx-31 {
	margin-left: 31px;
	margin-right: 31px;
}

.mx-32 {
	margin-left: 32px;
	margin-right: 32px;
}

.mx-33 {
	margin-left: 33px;
	margin-right: 33px;
}

.mx-34 {
	margin-left: 34px;
	margin-right: 34px;
}

.mx-35 {
	margin-left: 35px;
	margin-right: 35px;
}

.mx-36 {
	margin-left: 36px;
	margin-right: 36px;
}

.mx-37 {
	margin-left: 37px;
	margin-right: 37px;
}

.mx-38 {
	margin-left: 38px;
	margin-right: 38px;
}

.mx-39 {
	margin-left: 39px;
	margin-right: 39px;
}

.mx-40 {
	margin-left: 40px;
	margin-right: 40px;
}

.mx-41 {
	margin-left: 41px;
	margin-right: 41px;
}

.mx-42 {
	margin-left: 42px;
	margin-right: 42px;
}

.mx-43 {
	margin-left: 43px;
	margin-right: 43px;
}

.mx-44 {
	margin-left: 44px;
	margin-right: 44px;
}

.mx-45 {
	margin-left: 45px;
	margin-right: 45px;
}

.mx-46 {
	margin-left: 46px;
	margin-right: 46px;
}

.mx-47 {
	margin-left: 47px;
	margin-right: 47px;
}

.mx-48 {
	margin-left: 48px;
	margin-right: 48px;
}

.mx-49 {
	margin-left: 49px;
	margin-right: 49px;
}

.mx-50 {
	margin-left: 50px;
	margin-right: 50px;
}

.mx-n1 {
	margin-left: 4px;
	margin-right: 4px;
}

.mx-n2 {
	margin-left: 8px;
	margin-right: 8px;
}

.mx-n3 {
	margin-left: 12px;
	margin-right: 12px;
}

.mx-n4 {
	margin-left: 16px;
	margin-right: 16px;
}

.mx-n5 {
	margin-left: 20px;
	margin-right: 20px;
}

.mx-n6 {
	margin-left: 24px;
	margin-right: 24px;
}

.mx-n7 {
	margin-left: 28px;
	margin-right: 28px;
}

.mx-n8 {
	margin-left: 32px;
	margin-right: 32px;
}

.mx-n9 {
	margin-left: 36px;
	margin-right: 36px;
}

.mx-n10 {
	margin-left: 40px;
	margin-right: 40px;
}

.mx-n11 {
	margin-left: 44px;
	margin-right: 44px;
}

.mx-n12 {
	margin-left: 48px;
	margin-right: 48px;
}

.mx-n13 {
	margin-left: 52px;
	margin-right: 52px;
}

.mx-n14 {
	margin-left: 56px;
	margin-right: 56px;
}

.mx-n15 {
	margin-left: 60px;
	margin-right: 60px;
}

.mx-n16 {
	margin-left: 64px;
	margin-right: 64px;
}

.mx-n17 {
	margin-left: 68px;
	margin-right: 68px;
}

.mx-n18 {
	margin-left: 72px;
	margin-right: 72px;
}

.mx-n19 {
	margin-left: 76px;
	margin-right: 76px;
}

.mx-n20 {
	margin-left: 80px;
	margin-right: 80px;
}

.mx-n21 {
	margin-left: 84px;
	margin-right: 84px;
}

.mx-n22 {
	margin-left: 88px;
	margin-right: 88px;
}

.mx-n23 {
	margin-left: 92px;
	margin-right: 92px;
}

.mx-n24 {
	margin-left: 96px;
	margin-right: 96px;
}

.mx-n25 {
	margin-left: 100px;
	margin-right: 100px;
}

.my-0 {
	margin-top: 0px;
	margin-bottom: 0px;
}

.my-1 {
	margin-top: 1px;
	margin-bottom: 1px;
}

.my-2 {
	margin-top: 2px;
	margin-bottom: 2px;
}

.my-3 {
	margin-top: 3px;
	margin-bottom: 3px;
}

.my-4 {
	margin-top: 4px;
	margin-bottom: 4px;
}

.my-5 {
	margin-top: 5px;
	margin-bottom: 5px;
}

.my-6 {
	margin-top: 6px;
	margin-bottom: 6px;
}

.my-7 {
	margin-top: 7px;
	margin-bottom: 7px;
}

.my-8 {
	margin-top: 8px;
	margin-bottom: 8px;
}

.my-9 {
	margin-top: 9px;
	margin-bottom: 9px;
}

.my-10 {
	margin-top: 10px;
	margin-bottom: 10px;
}

.my-11 {
	margin-top: 11px;
	margin-bottom: 11px;
}

.my-12 {
	margin-top: 12px;
	margin-bottom: 12px;
}

.my-13 {
	margin-top: 13px;
	margin-bottom: 13px;
}

.my-14 {
	margin-top: 14px;
	margin-bottom: 14px;
}

.my-15 {
	margin-top: 15px;
	margin-bottom: 15px;
}

.my-16 {
	margin-top: 16px;
	margin-bottom: 16px;
}

.my-17 {
	margin-top: 17px;
	margin-bottom: 17px;
}

.my-18 {
	margin-top: 18px;
	margin-bottom: 18px;
}

.my-19 {
	margin-top: 19px;
	margin-bottom: 19px;
}

.my-20 {
	margin-top: 20px;
	margin-bottom: 20px;
}

.my-21 {
	margin-top: 21px;
	margin-bottom: 21px;
}

.my-22 {
	margin-top: 22px;
	margin-bottom: 22px;
}

.my-23 {
	margin-top: 23px;
	margin-bottom: 23px;
}

.my-24 {
	margin-top: 24px;
	margin-bottom: 24px;
}

.my-25 {
	margin-top: 25px;
	margin-bottom: 25px;
}

.my-26 {
	margin-top: 26px;
	margin-bottom: 26px;
}

.my-27 {
	margin-top: 27px;
	margin-bottom: 27px;
}

.my-28 {
	margin-top: 28px;
	margin-bottom: 28px;
}

.my-29 {
	margin-top: 29px;
	margin-bottom: 29px;
}

.my-30 {
	margin-top: 30px;
	margin-bottom: 30px;
}

.my-31 {
	margin-top: 31px;
	margin-bottom: 31px;
}

.my-32 {
	margin-top: 32px;
	margin-bottom: 32px;
}

.my-33 {
	margin-top: 33px;
	margin-bottom: 33px;
}

.my-34 {
	margin-top: 34px;
	margin-bottom: 34px;
}

.my-35 {
	margin-top: 35px;
	margin-bottom: 35px;
}

.my-36 {
	margin-top: 36px;
	margin-bottom: 36px;
}

.my-37 {
	margin-top: 37px;
	margin-bottom: 37px;
}

.my-38 {
	margin-top: 38px;
	margin-bottom: 38px;
}

.my-39 {
	margin-top: 39px;
	margin-bottom: 39px;
}

.my-40 {
	margin-top: 40px;
	margin-bottom: 40px;
}

.my-41 {
	margin-top: 41px;
	margin-bottom: 41px;
}

.my-42 {
	margin-top: 42px;
	margin-bottom: 42px;
}

.my-43 {
	margin-top: 43px;
	margin-bottom: 43px;
}

.my-44 {
	margin-top: 44px;
	margin-bottom: 44px;
}

.my-45 {
	margin-top: 45px;
	margin-bottom: 45px;
}

.my-46 {
	margin-top: 46px;
	margin-bottom: 46px;
}

.my-47 {
	margin-top: 47px;
	margin-bottom: 47px;
}

.my-48 {
	margin-top: 48px;
	margin-bottom: 48px;
}

.my-49 {
	margin-top: 49px;
	margin-bottom: 49px;
}

.my-50 {
	margin-top: 50px;
	margin-bottom: 50px;
}

.my-n1 {
	margin-top: 4px;
	margin-bottom: 4px;
}

.my-n2 {
	margin-top: 8px;
	margin-bottom: 8px;
}

.my-n3 {
	margin-top: 12px;
	margin-bottom: 12px;
}

.my-n4 {
	margin-top: 16px;
	margin-bottom: 16px;
}

.my-n5 {
	margin-top: 20px;
	margin-bottom: 20px;
}

.my-n6 {
	margin-top: 24px;
	margin-bottom: 24px;
}

.my-n7 {
	margin-top: 28px;
	margin-bottom: 28px;
}

.my-n8 {
	margin-top: 32px;
	margin-bottom: 32px;
}

.my-n9 {
	margin-top: 36px;
	margin-bottom: 36px;
}

.my-n10 {
	margin-top: 40px;
	margin-bottom: 40px;
}

.my-n11 {
	margin-top: 44px;
	margin-bottom: 44px;
}

.my-n12 {
	margin-top: 48px;
	margin-bottom: 48px;
}

.my-n13 {
	margin-top: 52px;
	margin-bottom: 52px;
}

.my-n14 {
	margin-top: 56px;
	margin-bottom: 56px;
}

.my-n15 {
	margin-top: 60px;
	margin-bottom: 60px;
}

.my-n16 {
	margin-top: 64px;
	margin-bottom: 64px;
}

.my-n17 {
	margin-top: 68px;
	margin-bottom: 68px;
}

.my-n18 {
	margin-top: 72px;
	margin-bottom: 72px;
}

.my-n19 {
	margin-top: 76px;
	margin-bottom: 76px;
}

.my-n20 {
	margin-top: 80px;
	margin-bottom: 80px;
}

.my-n21 {
	margin-top: 84px;
	margin-bottom: 84px;
}

.my-n22 {
	margin-top: 88px;
	margin-bottom: 88px;
}

.my-n23 {
	margin-top: 92px;
	margin-bottom: 92px;
}

.my-n24 {
	margin-top: 96px;
	margin-bottom: 96px;
}

.my-n25 {
	margin-top: 100px;
	margin-bottom: 100px;
}

.t-0 {
	top: 0px;
}

.t--0 {
	top: -0px;
}

.t-1 {
	top: 1px;
}

.t--1 {
	top: -1px;
}

.t-2 {
	top: 2px;
}

.t--2 {
	top: -2px;
}

.t-3 {
	top: 3px;
}

.t--3 {
	top: -3px;
}

.t-4 {
	top: 4px;
}

.t--4 {
	top: -4px;
}

.t-5 {
	top: 5px;
}

.t--5 {
	top: -5px;
}

.t-6 {
	top: 6px;
}

.t--6 {
	top: -6px;
}

.t-7 {
	top: 7px;
}

.t--7 {
	top: -7px;
}

.t-8 {
	top: 8px;
}

.t--8 {
	top: -8px;
}

.t-9 {
	top: 9px;
}

.t--9 {
	top: -9px;
}

.t-10 {
	top: 10px;
}

.t--10 {
	top: -10px;
}

.t-11 {
	top: 11px;
}

.t--11 {
	top: -11px;
}

.t-12 {
	top: 12px;
}

.t--12 {
	top: -12px;
}

.t-13 {
	top: 13px;
}

.t--13 {
	top: -13px;
}

.t-14 {
	top: 14px;
}

.t--14 {
	top: -14px;
}

.t-15 {
	top: 15px;
}

.t--15 {
	top: -15px;
}

.t-16 {
	top: 16px;
}

.t--16 {
	top: -16px;
}

.t-17 {
	top: 17px;
}

.t--17 {
	top: -17px;
}

.t-18 {
	top: 18px;
}

.t--18 {
	top: -18px;
}

.t-19 {
	top: 19px;
}

.t--19 {
	top: -19px;
}

.t-20 {
	top: 20px;
}

.t--20 {
	top: -20px;
}

.t-21 {
	top: 21px;
}

.t--21 {
	top: -21px;
}

.t-22 {
	top: 22px;
}

.t--22 {
	top: -22px;
}

.t-23 {
	top: 23px;
}

.t--23 {
	top: -23px;
}

.t-24 {
	top: 24px;
}

.t--24 {
	top: -24px;
}

.t-25 {
	top: 25px;
}

.t--25 {
	top: -25px;
}

.t-26 {
	top: 26px;
}

.t--26 {
	top: -26px;
}

.t-27 {
	top: 27px;
}

.t--27 {
	top: -27px;
}

.t-28 {
	top: 28px;
}

.t--28 {
	top: -28px;
}

.t-29 {
	top: 29px;
}

.t--29 {
	top: -29px;
}

.t-30 {
	top: 30px;
}

.t--30 {
	top: -30px;
}

.t-31 {
	top: 31px;
}

.t--31 {
	top: -31px;
}

.t-32 {
	top: 32px;
}

.t--32 {
	top: -32px;
}

.t-33 {
	top: 33px;
}

.t--33 {
	top: -33px;
}

.t-34 {
	top: 34px;
}

.t--34 {
	top: -34px;
}

.t-35 {
	top: 35px;
}

.t--35 {
	top: -35px;
}

.t-36 {
	top: 36px;
}

.t--36 {
	top: -36px;
}

.t-37 {
	top: 37px;
}

.t--37 {
	top: -37px;
}

.t-38 {
	top: 38px;
}

.t--38 {
	top: -38px;
}

.t-39 {
	top: 39px;
}

.t--39 {
	top: -39px;
}

.t-40 {
	top: 40px;
}

.t--40 {
	top: -40px;
}

.t-41 {
	top: 41px;
}

.t--41 {
	top: -41px;
}

.t-42 {
	top: 42px;
}

.t--42 {
	top: -42px;
}

.t-43 {
	top: 43px;
}

.t--43 {
	top: -43px;
}

.t-44 {
	top: 44px;
}

.t--44 {
	top: -44px;
}

.t-45 {
	top: 45px;
}

.t--45 {
	top: -45px;
}

.t-46 {
	top: 46px;
}

.t--46 {
	top: -46px;
}

.t-47 {
	top: 47px;
}

.t--47 {
	top: -47px;
}

.t-48 {
	top: 48px;
}

.t--48 {
	top: -48px;
}

.t-49 {
	top: 49px;
}

.t--49 {
	top: -49px;
}

.t-50 {
	top: 50px;
}

.t--50 {
	top: -50px;
}

.t-n1 {
	top: 4px;
}

.t--n1 {
	top: -4px;
}

.t-n2 {
	top: 8px;
}

.t--n2 {
	top: -8px;
}

.t-n3 {
	top: 12px;
}

.t--n3 {
	top: -12px;
}

.t-n4 {
	top: 16px;
}

.t--n4 {
	top: -16px;
}

.t-n5 {
	top: 20px;
}

.t--n5 {
	top: -20px;
}

.t-n6 {
	top: 24px;
}

.t--n6 {
	top: -24px;
}

.t-n7 {
	top: 28px;
}

.t--n7 {
	top: -28px;
}

.t-n8 {
	top: 32px;
}

.t--n8 {
	top: -32px;
}

.t-n9 {
	top: 36px;
}

.t--n9 {
	top: -36px;
}

.t-n10 {
	top: 40px;
}

.t--n10 {
	top: -40px;
}

.t-n11 {
	top: 44px;
}

.t--n11 {
	top: -44px;
}

.t-n12 {
	top: 48px;
}

.t--n12 {
	top: -48px;
}

.t-n13 {
	top: 52px;
}

.t--n13 {
	top: -52px;
}

.t-n14 {
	top: 56px;
}

.t--n14 {
	top: -56px;
}

.t-n15 {
	top: 60px;
}

.t--n15 {
	top: -60px;
}

.t-n16 {
	top: 64px;
}

.t--n16 {
	top: -64px;
}

.t-n17 {
	top: 68px;
}

.t--n17 {
	top: -68px;
}

.t-n18 {
	top: 72px;
}

.t--n18 {
	top: -72px;
}

.t-n19 {
	top: 76px;
}

.t--n19 {
	top: -76px;
}

.t-n20 {
	top: 80px;
}

.t--n20 {
	top: -80px;
}

.t-n21 {
	top: 84px;
}

.t--n21 {
	top: -84px;
}

.t-n22 {
	top: 88px;
}

.t--n22 {
	top: -88px;
}

.t-n23 {
	top: 92px;
}

.t--n23 {
	top: -92px;
}

.t-n24 {
	top: 96px;
}

.t--n24 {
	top: -96px;
}

.t-n25 {
	top: 100px;
}

.t--n25 {
	top: -100px;
}

.r-0 {
	right: 0px;
}

.r--0 {
	right: -0px;
}

.r-1 {
	right: 1px;
}

.r--1 {
	right: -1px;
}

.r-2 {
	right: 2px;
}

.r--2 {
	right: -2px;
}

.r-3 {
	right: 3px;
}

.r--3 {
	right: -3px;
}

.r-4 {
	right: 4px;
}

.r--4 {
	right: -4px;
}

.r-5 {
	right: 5px;
}

.r--5 {
	right: -5px;
}

.r-6 {
	right: 6px;
}

.r--6 {
	right: -6px;
}

.r-7 {
	right: 7px;
}

.r--7 {
	right: -7px;
}

.r-8 {
	right: 8px;
}

.r--8 {
	right: -8px;
}

.r-9 {
	right: 9px;
}

.r--9 {
	right: -9px;
}

.r-10 {
	right: 10px;
}

.r--10 {
	right: -10px;
}

.r-11 {
	right: 11px;
}

.r--11 {
	right: -11px;
}

.r-12 {
	right: 12px;
}

.r--12 {
	right: -12px;
}

.r-13 {
	right: 13px;
}

.r--13 {
	right: -13px;
}

.r-14 {
	right: 14px;
}

.r--14 {
	right: -14px;
}

.r-15 {
	right: 15px;
}

.r--15 {
	right: -15px;
}

.r-16 {
	right: 16px;
}

.r--16 {
	right: -16px;
}

.r-17 {
	right: 17px;
}

.r--17 {
	right: -17px;
}

.r-18 {
	right: 18px;
}

.r--18 {
	right: -18px;
}

.r-19 {
	right: 19px;
}

.r--19 {
	right: -19px;
}

.r-20 {
	right: 20px;
}

.r--20 {
	right: -20px;
}

.r-21 {
	right: 21px;
}

.r--21 {
	right: -21px;
}

.r-22 {
	right: 22px;
}

.r--22 {
	right: -22px;
}

.r-23 {
	right: 23px;
}

.r--23 {
	right: -23px;
}

.r-24 {
	right: 24px;
}

.r--24 {
	right: -24px;
}

.r-25 {
	right: 25px;
}

.r--25 {
	right: -25px;
}

.r-26 {
	right: 26px;
}

.r--26 {
	right: -26px;
}

.r-27 {
	right: 27px;
}

.r--27 {
	right: -27px;
}

.r-28 {
	right: 28px;
}

.r--28 {
	right: -28px;
}

.r-29 {
	right: 29px;
}

.r--29 {
	right: -29px;
}

.r-30 {
	right: 30px;
}

.r--30 {
	right: -30px;
}

.r-31 {
	right: 31px;
}

.r--31 {
	right: -31px;
}

.r-32 {
	right: 32px;
}

.r--32 {
	right: -32px;
}

.r-33 {
	right: 33px;
}

.r--33 {
	right: -33px;
}

.r-34 {
	right: 34px;
}

.r--34 {
	right: -34px;
}

.r-35 {
	right: 35px;
}

.r--35 {
	right: -35px;
}

.r-36 {
	right: 36px;
}

.r--36 {
	right: -36px;
}

.r-37 {
	right: 37px;
}

.r--37 {
	right: -37px;
}

.r-38 {
	right: 38px;
}

.r--38 {
	right: -38px;
}

.r-39 {
	right: 39px;
}

.r--39 {
	right: -39px;
}

.r-40 {
	right: 40px;
}

.r--40 {
	right: -40px;
}

.r-41 {
	right: 41px;
}

.r--41 {
	right: -41px;
}

.r-42 {
	right: 42px;
}

.r--42 {
	right: -42px;
}

.r-43 {
	right: 43px;
}

.r--43 {
	right: -43px;
}

.r-44 {
	right: 44px;
}

.r--44 {
	right: -44px;
}

.r-45 {
	right: 45px;
}

.r--45 {
	right: -45px;
}

.r-46 {
	right: 46px;
}

.r--46 {
	right: -46px;
}

.r-47 {
	right: 47px;
}

.r--47 {
	right: -47px;
}

.r-48 {
	right: 48px;
}

.r--48 {
	right: -48px;
}

.r-49 {
	right: 49px;
}

.r--49 {
	right: -49px;
}

.r-50 {
	right: 50px;
}

.r--50 {
	right: -50px;
}

.r-n1 {
	right: 4px;
}

.r--n1 {
	right: -4px;
}

.r-n2 {
	right: 8px;
}

.r--n2 {
	right: -8px;
}

.r-n3 {
	right: 12px;
}

.r--n3 {
	right: -12px;
}

.r-n4 {
	right: 16px;
}

.r--n4 {
	right: -16px;
}

.r-n5 {
	right: 20px;
}

.r--n5 {
	right: -20px;
}

.r-n6 {
	right: 24px;
}

.r--n6 {
	right: -24px;
}

.r-n7 {
	right: 28px;
}

.r--n7 {
	right: -28px;
}

.r-n8 {
	right: 32px;
}

.r--n8 {
	right: -32px;
}

.r-n9 {
	right: 36px;
}

.r--n9 {
	right: -36px;
}

.r-n10 {
	right: 40px;
}

.r--n10 {
	right: -40px;
}

.r-n11 {
	right: 44px;
}

.r--n11 {
	right: -44px;
}

.r-n12 {
	right: 48px;
}

.r--n12 {
	right: -48px;
}

.r-n13 {
	right: 52px;
}

.r--n13 {
	right: -52px;
}

.r-n14 {
	right: 56px;
}

.r--n14 {
	right: -56px;
}

.r-n15 {
	right: 60px;
}

.r--n15 {
	right: -60px;
}

.r-n16 {
	right: 64px;
}

.r--n16 {
	right: -64px;
}

.r-n17 {
	right: 68px;
}

.r--n17 {
	right: -68px;
}

.r-n18 {
	right: 72px;
}

.r--n18 {
	right: -72px;
}

.r-n19 {
	right: 76px;
}

.r--n19 {
	right: -76px;
}

.r-n20 {
	right: 80px;
}

.r--n20 {
	right: -80px;
}

.r-n21 {
	right: 84px;
}

.r--n21 {
	right: -84px;
}

.r-n22 {
	right: 88px;
}

.r--n22 {
	right: -88px;
}

.r-n23 {
	right: 92px;
}

.r--n23 {
	right: -92px;
}

.r-n24 {
	right: 96px;
}

.r--n24 {
	right: -96px;
}

.r-n25 {
	right: 100px;
}

.r--n25 {
	right: -100px;
}

.b-0 {
	bottom: 0px;
}

.b--0 {
	bottom: -0px;
}

.b-1 {
	bottom: 1px;
}

.b--1 {
	bottom: -1px;
}

.b-2 {
	bottom: 2px;
}

.b--2 {
	bottom: -2px;
}

.b-3 {
	bottom: 3px;
}

.b--3 {
	bottom: -3px;
}

.b-4 {
	bottom: 4px;
}

.b--4 {
	bottom: -4px;
}

.b-5 {
	bottom: 5px;
}

.b--5 {
	bottom: -5px;
}

.b-6 {
	bottom: 6px;
}

.b--6 {
	bottom: -6px;
}

.b-7 {
	bottom: 7px;
}

.b--7 {
	bottom: -7px;
}

.b-8 {
	bottom: 8px;
}

.b--8 {
	bottom: -8px;
}

.b-9 {
	bottom: 9px;
}

.b--9 {
	bottom: -9px;
}

.b-10 {
	bottom: 10px;
}

.b--10 {
	bottom: -10px;
}

.b-11 {
	bottom: 11px;
}

.b--11 {
	bottom: -11px;
}

.b-12 {
	bottom: 12px;
}

.b--12 {
	bottom: -12px;
}

.b-13 {
	bottom: 13px;
}

.b--13 {
	bottom: -13px;
}

.b-14 {
	bottom: 14px;
}

.b--14 {
	bottom: -14px;
}

.b-15 {
	bottom: 15px;
}

.b--15 {
	bottom: -15px;
}

.b-16 {
	bottom: 16px;
}

.b--16 {
	bottom: -16px;
}

.b-17 {
	bottom: 17px;
}

.b--17 {
	bottom: -17px;
}

.b-18 {
	bottom: 18px;
}

.b--18 {
	bottom: -18px;
}

.b-19 {
	bottom: 19px;
}

.b--19 {
	bottom: -19px;
}

.b-20 {
	bottom: 20px;
}

.b--20 {
	bottom: -20px;
}

.b-21 {
	bottom: 21px;
}

.b--21 {
	bottom: -21px;
}

.b-22 {
	bottom: 22px;
}

.b--22 {
	bottom: -22px;
}

.b-23 {
	bottom: 23px;
}

.b--23 {
	bottom: -23px;
}

.b-24 {
	bottom: 24px;
}

.b--24 {
	bottom: -24px;
}

.b-25 {
	bottom: 25px;
}

.b--25 {
	bottom: -25px;
}

.b-26 {
	bottom: 26px;
}

.b--26 {
	bottom: -26px;
}

.b-27 {
	bottom: 27px;
}

.b--27 {
	bottom: -27px;
}

.b-28 {
	bottom: 28px;
}

.b--28 {
	bottom: -28px;
}

.b-29 {
	bottom: 29px;
}

.b--29 {
	bottom: -29px;
}

.b-30 {
	bottom: 30px;
}

.b--30 {
	bottom: -30px;
}

.b-31 {
	bottom: 31px;
}

.b--31 {
	bottom: -31px;
}

.b-32 {
	bottom: 32px;
}

.b--32 {
	bottom: -32px;
}

.b-33 {
	bottom: 33px;
}

.b--33 {
	bottom: -33px;
}

.b-34 {
	bottom: 34px;
}

.b--34 {
	bottom: -34px;
}

.b-35 {
	bottom: 35px;
}

.b--35 {
	bottom: -35px;
}

.b-36 {
	bottom: 36px;
}

.b--36 {
	bottom: -36px;
}

.b-37 {
	bottom: 37px;
}

.b--37 {
	bottom: -37px;
}

.b-38 {
	bottom: 38px;
}

.b--38 {
	bottom: -38px;
}

.b-39 {
	bottom: 39px;
}

.b--39 {
	bottom: -39px;
}

.b-40 {
	bottom: 40px;
}

.b--40 {
	bottom: -40px;
}

.b-41 {
	bottom: 41px;
}

.b--41 {
	bottom: -41px;
}

.b-42 {
	bottom: 42px;
}

.b--42 {
	bottom: -42px;
}

.b-43 {
	bottom: 43px;
}

.b--43 {
	bottom: -43px;
}

.b-44 {
	bottom: 44px;
}

.b--44 {
	bottom: -44px;
}

.b-45 {
	bottom: 45px;
}

.b--45 {
	bottom: -45px;
}

.b-46 {
	bottom: 46px;
}

.b--46 {
	bottom: -46px;
}

.b-47 {
	bottom: 47px;
}

.b--47 {
	bottom: -47px;
}

.b-48 {
	bottom: 48px;
}

.b--48 {
	bottom: -48px;
}

.b-49 {
	bottom: 49px;
}

.b--49 {
	bottom: -49px;
}

.b-50 {
	bottom: 50px;
}

.b--50 {
	bottom: -50px;
}

.b-n1 {
	bottom: 4px;
}

.b--n1 {
	bottom: -4px;
}

.b-n2 {
	bottom: 8px;
}

.b--n2 {
	bottom: -8px;
}

.b-n3 {
	bottom: 12px;
}

.b--n3 {
	bottom: -12px;
}

.b-n4 {
	bottom: 16px;
}

.b--n4 {
	bottom: -16px;
}

.b-n5 {
	bottom: 20px;
}

.b--n5 {
	bottom: -20px;
}

.b-n6 {
	bottom: 24px;
}

.b--n6 {
	bottom: -24px;
}

.b-n7 {
	bottom: 28px;
}

.b--n7 {
	bottom: -28px;
}

.b-n8 {
	bottom: 32px;
}

.b--n8 {
	bottom: -32px;
}

.b-n9 {
	bottom: 36px;
}

.b--n9 {
	bottom: -36px;
}

.b-n10 {
	bottom: 40px;
}

.b--n10 {
	bottom: -40px;
}

.b-n11 {
	bottom: 44px;
}

.b--n11 {
	bottom: -44px;
}

.b-n12 {
	bottom: 48px;
}

.b--n12 {
	bottom: -48px;
}

.b-n13 {
	bottom: 52px;
}

.b--n13 {
	bottom: -52px;
}

.b-n14 {
	bottom: 56px;
}

.b--n14 {
	bottom: -56px;
}

.b-n15 {
	bottom: 60px;
}

.b--n15 {
	bottom: -60px;
}

.b-n16 {
	bottom: 64px;
}

.b--n16 {
	bottom: -64px;
}

.b-n17 {
	bottom: 68px;
}

.b--n17 {
	bottom: -68px;
}

.b-n18 {
	bottom: 72px;
}

.b--n18 {
	bottom: -72px;
}

.b-n19 {
	bottom: 76px;
}

.b--n19 {
	bottom: -76px;
}

.b-n20 {
	bottom: 80px;
}

.b--n20 {
	bottom: -80px;
}

.b-n21 {
	bottom: 84px;
}

.b--n21 {
	bottom: -84px;
}

.b-n22 {
	bottom: 88px;
}

.b--n22 {
	bottom: -88px;
}

.b-n23 {
	bottom: 92px;
}

.b--n23 {
	bottom: -92px;
}

.b-n24 {
	bottom: 96px;
}

.b--n24 {
	bottom: -96px;
}

.b-n25 {
	bottom: 100px;
}

.b--n25 {
	bottom: -100px;
}

.l-0 {
	left: 0px;
}

.l--0 {
	left: -0px;
}

.l-1 {
	left: 1px;
}

.l--1 {
	left: -1px;
}

.l-2 {
	left: 2px;
}

.l--2 {
	left: -2px;
}

.l-3 {
	left: 3px;
}

.l--3 {
	left: -3px;
}

.l-4 {
	left: 4px;
}

.l--4 {
	left: -4px;
}

.l-5 {
	left: 5px;
}

.l--5 {
	left: -5px;
}

.l-6 {
	left: 6px;
}

.l--6 {
	left: -6px;
}

.l-7 {
	left: 7px;
}

.l--7 {
	left: -7px;
}

.l-8 {
	left: 8px;
}

.l--8 {
	left: -8px;
}

.l-9 {
	left: 9px;
}

.l--9 {
	left: -9px;
}

.l-10 {
	left: 10px;
}

.l--10 {
	left: -10px;
}

.l-11 {
	left: 11px;
}

.l--11 {
	left: -11px;
}

.l-12 {
	left: 12px;
}

.l--12 {
	left: -12px;
}

.l-13 {
	left: 13px;
}

.l--13 {
	left: -13px;
}

.l-14 {
	left: 14px;
}

.l--14 {
	left: -14px;
}

.l-15 {
	left: 15px;
}

.l--15 {
	left: -15px;
}

.l-16 {
	left: 16px;
}

.l--16 {
	left: -16px;
}

.l-17 {
	left: 17px;
}

.l--17 {
	left: -17px;
}

.l-18 {
	left: 18px;
}

.l--18 {
	left: -18px;
}

.l-19 {
	left: 19px;
}

.l--19 {
	left: -19px;
}

.l-20 {
	left: 20px;
}

.l--20 {
	left: -20px;
}

.l-21 {
	left: 21px;
}

.l--21 {
	left: -21px;
}

.l-22 {
	left: 22px;
}

.l--22 {
	left: -22px;
}

.l-23 {
	left: 23px;
}

.l--23 {
	left: -23px;
}

.l-24 {
	left: 24px;
}

.l--24 {
	left: -24px;
}

.l-25 {
	left: 25px;
}

.l--25 {
	left: -25px;
}

.l-26 {
	left: 26px;
}

.l--26 {
	left: -26px;
}

.l-27 {
	left: 27px;
}

.l--27 {
	left: -27px;
}

.l-28 {
	left: 28px;
}

.l--28 {
	left: -28px;
}

.l-29 {
	left: 29px;
}

.l--29 {
	left: -29px;
}

.l-30 {
	left: 30px;
}

.l--30 {
	left: -30px;
}

.l-31 {
	left: 31px;
}

.l--31 {
	left: -31px;
}

.l-32 {
	left: 32px;
}

.l--32 {
	left: -32px;
}

.l-33 {
	left: 33px;
}

.l--33 {
	left: -33px;
}

.l-34 {
	left: 34px;
}

.l--34 {
	left: -34px;
}

.l-35 {
	left: 35px;
}

.l--35 {
	left: -35px;
}

.l-36 {
	left: 36px;
}

.l--36 {
	left: -36px;
}

.l-37 {
	left: 37px;
}

.l--37 {
	left: -37px;
}

.l-38 {
	left: 38px;
}

.l--38 {
	left: -38px;
}

.l-39 {
	left: 39px;
}

.l--39 {
	left: -39px;
}

.l-40 {
	left: 40px;
}

.l--40 {
	left: -40px;
}

.l-41 {
	left: 41px;
}

.l--41 {
	left: -41px;
}

.l-42 {
	left: 42px;
}

.l--42 {
	left: -42px;
}

.l-43 {
	left: 43px;
}

.l--43 {
	left: -43px;
}

.l-44 {
	left: 44px;
}

.l--44 {
	left: -44px;
}

.l-45 {
	left: 45px;
}

.l--45 {
	left: -45px;
}

.l-46 {
	left: 46px;
}

.l--46 {
	left: -46px;
}

.l-47 {
	left: 47px;
}

.l--47 {
	left: -47px;
}

.l-48 {
	left: 48px;
}

.l--48 {
	left: -48px;
}

.l-49 {
	left: 49px;
}

.l--49 {
	left: -49px;
}

.l-50 {
	left: 50px;
}

.l--50 {
	left: -50px;
}

.l-n1 {
	left: 4px;
}

.l--n1 {
	left: -4px;
}

.l-n2 {
	left: 8px;
}

.l--n2 {
	left: -8px;
}

.l-n3 {
	left: 12px;
}

.l--n3 {
	left: -12px;
}

.l-n4 {
	left: 16px;
}

.l--n4 {
	left: -16px;
}

.l-n5 {
	left: 20px;
}

.l--n5 {
	left: -20px;
}

.l-n6 {
	left: 24px;
}

.l--n6 {
	left: -24px;
}

.l-n7 {
	left: 28px;
}

.l--n7 {
	left: -28px;
}

.l-n8 {
	left: 32px;
}

.l--n8 {
	left: -32px;
}

.l-n9 {
	left: 36px;
}

.l--n9 {
	left: -36px;
}

.l-n10 {
	left: 40px;
}

.l--n10 {
	left: -40px;
}

.l-n11 {
	left: 44px;
}

.l--n11 {
	left: -44px;
}

.l-n12 {
	left: 48px;
}

.l--n12 {
	left: -48px;
}

.l-n13 {
	left: 52px;
}

.l--n13 {
	left: -52px;
}

.l-n14 {
	left: 56px;
}

.l--n14 {
	left: -56px;
}

.l-n15 {
	left: 60px;
}

.l--n15 {
	left: -60px;
}

.l-n16 {
	left: 64px;
}

.l--n16 {
	left: -64px;
}

.l-n17 {
	left: 68px;
}

.l--n17 {
	left: -68px;
}

.l-n18 {
	left: 72px;
}

.l--n18 {
	left: -72px;
}

.l-n19 {
	left: 76px;
}

.l--n19 {
	left: -76px;
}

.l-n20 {
	left: 80px;
}

.l--n20 {
	left: -80px;
}

.l-n21 {
	left: 84px;
}

.l--n21 {
	left: -84px;
}

.l-n22 {
	left: 88px;
}

.l--n22 {
	left: -88px;
}

.l-n23 {
	left: 92px;
}

.l--n23 {
	left: -92px;
}

.l-n24 {
	left: 96px;
}

.l--n24 {
	left: -96px;
}

.l-n25 {
	left: 100px;
}

.l--n25 {
	left: -100px;
}

.flex {
	display: flex !important;
}

.flex-col {
	flex-direction: column !important;
	display: flex !important;
}

.flex-wrap {
	flex-flow: row wrap !important;
	display: flex !important;
}

.flex-shrink {
	flex-shrink: 0 !important;
	display: flex !important;
}

.flex-row {
	flex-direction: row !important;
	display: flex !important;
}

.flex-reverse {
	flex-direction: row-reverse !important;
	display: flex !important;
}

.flex-top-center {
	justify-content: center !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-top-end {
	justify-content: flex-end !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-start {
	justify-content: flex-start !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-end {
	justify-content: flex-end !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-center {
	justify-content: center !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-between {
	justify-content: space-between !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-around {
	justify-content: space-around !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-end-left {
	justify-content: flex-end !important;
	align-items: flex-start !important;
	align-content: center;
	display: flex !important;
}

.flex-end-center {
	justify-content: flex-end !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-end-right {
	justify-content: flex-end !important;
	align-items: flex-end !important;
	align-content: flex-end;
	display: flex !important;
}

.flex-0 {
	flex: 0 !important;
	flex-grow: 0 !important;
}

.flex-1 {
	flex: 1 !important;
	flex-grow: 1 !important;
}

.flex-2 {
	flex: 2 !important;
	flex-grow: 2 !important;
}

.flex-3 {
	flex: 3 !important;
	flex-grow: 3 !important;
}

.flex-4 {
	flex: 4 !important;
	flex-grow: 4 !important;
}

.flex-5 {
	flex: 5 !important;
	flex-grow: 5 !important;
}

.flex-6 {
	flex: 6 !important;
	flex-grow: 6 !important;
}

.flex-7 {
	flex: 7 !important;
	flex-grow: 7 !important;
}

.flex-8 {
	flex: 8 !important;
	flex-grow: 8 !important;
}

.flex-9 {
	flex: 9 !important;
	flex-grow: 9 !important;
}

.flex-10 {
	flex: 10 !important;
	flex-grow: 10 !important;
}

.flex-11 {
	flex: 11 !important;
	flex-grow: 11 !important;
}

.flex-12 {
	flex: 12 !important;
	flex-grow: 12 !important;
}

.flex-start-top {
	justify-content: flex-start !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-start-center {
	justify-content: flex-start !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-start-end {
	justify-content: flex-start !important;
	align-items: flex-end !important;
	align-content: flex-end;
	display: flex !important;
}

.flex-item-top-start {
	justify-content: flex-start !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-item-top-center {
	justify-content: center !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-item-top-end {
	justify-content: flex-end !important;
	align-items: flex-start !important;
	align-content: flex-start;
	display: flex !important;
}

.flex-item-center-start {
	justify-content: flex-start !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-item-center-center {
	justify-content: center !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-item-center-center {
	justify-content: flex-end !important;
	align-items: center !important;
	align-content: center;
	display: flex !important;
}

.flex-item-end-start {
	justify-content: flex-start !important;
	align-items: flex-end !important;
	align-content: center;
	display: flex !important;
}

.flex-item-end-center {
	justify-content: center !important;
	align-items: flex-end !important;
	align-content: center;
	display: flex !important;
}

.flex-item-top-end {
	justify-content: flex-end !important;
	align-items: flex-end !important;
	align-content: center;
	display: flex !important;
}

.text-red {
	color: #f44336 !important;
}

.red {
	background-color: #f44336;
}

.text-pink {
	color: #e91e63 !important;
}

.pink {
	background-color: #e91e63;
}

.text-purple {
	color: #9c27b0 !important;
}

.purple {
	background-color: #9c27b0;
}

.text-deep-purple {
	color: #673ab7 !important;
}

.deep-purple {
	background-color: #673ab7;
}

.text-indigo {
	color: #3f51b5 !important;
}

.indigo {
	background-color: #3f51b5;
}

.text-blue {
	color: #2196f3 !important;
}

.blue {
	background-color: #2196f3;
}

.text-light-blue {
	color: #03a9f4 !important;
}

.light-blue {
	background-color: #03a9f4;
}

.text-cyan {
	color: #00bcd4 !important;
}

.cyan {
	background-color: #00bcd4;
}

.text-teal {
	color: #009688 !important;
}

.teal {
	background-color: #009688;
}

.text-green {
	color: #4caf50 !important;
}

.green {
	background-color: #4caf50;
}

.text-light-green {
	color: #8bc34a !important;
}

.light-green {
	background-color: #8bc34a;
}

.text-lime {
	color: #cddc39 !important;
}

.lime {
	background-color: #cddc39;
}

.text-yellow {
	color: #ffeb3b !important;
}

.yellow {
	background-color: #ffeb3b;
}

.text-amber {
	color: #ffc107 !important;
}

.amber {
	background-color: #ffc107;
}

.text-orange {
	color: #ff9800 !important;
}

.orange {
	background-color: #ff9800;
}

.text-deep-orange {
	color: #ff5722 !important;
}

.deep-orange {
	background-color: #ff5722;
}

.text-brown {
	color: #795548 !important;
}

.brown {
	background-color: #795548;
}

.text-blue-grey {
	color: #607d8b !important;
}

.blue-grey {
	background-color: #607d8b;
}

.text-grey {
	color: #9e9e9e !important;
}

.grey {
	background-color: #9e9e9e;
}

.text-black {
	color: #000000 !important;
}

.black {
	background-color: #000000;
}

.text-white {
	color: #ffffff !important;
}

.white {
	background-color: #ffffff;
}

.text-lighten-5 {
	color: #fafafa !important;
}

.lighten-5 {
	background-color: #fafafa;
}

.text-lighten-4 {
	color: #f5f5f5 !important;
}

.lighten-4 {
	background-color: #f5f5f5;
}

.text-lighten-3 {
	color: #eeeeee !important;
}

.lighten-3 {
	background-color: #eeeeee;
}

.text-lighten-2 {
	color: #e0e0e0 !important;
}

.lighten-2 {
	background-color: #e0e0e0;
}

.text-lighten-1 {
	color: #bdbdbd !important;
}

.lighten-1 {
	background-color: #bdbdbd;
}

.text-darken-1 {
	color: #757575 !important;
}

.darken-1 {
	background-color: #757575;
}

.text-darken-2 {
	color: #616161 !important;
}

.darken-2 {
	background-color: #616161;
}

.text-darken-3 {
	color: #424242 !important;
}

.darken-3 {
	background-color: #424242;
}

.text-darken-4 {
	color: #212121 !important;
}

.darken-4 {
	background-color: #212121;
}

.text-darken-5 {
	color: #131313 !important;
}

.darken-5 {
	background-color: #131313;
}

.text-darken-6 {
	color: #0a0a0a !important;
}

.darken-6 {
	background-color: #0a0a0a;
} /*# sourceMappingURL=mainweb.css.map */
