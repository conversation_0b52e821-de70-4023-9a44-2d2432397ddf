<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'评价'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-image :width="750" :height="1070" src="/static/img/banner5.png"></tm-image>
			</view>
			<view class="relative flex-col flex-col-center-center">
				<tm-image :width="633" :height="194" src="/static/img/pj.png"></tm-image>
				<view class="flex-col" style="width: 591rpx;">
					<tm-text class="text-align-left" :font-size="22" :lineHeight="33" color="#fff" :label="welcome.title"></tm-text>
					<tm-text class="text-align-left mt-10" :font-size="22" :lineHeight="33" color="#fff" :label="welcome.desc"></tm-text>
				</view>
			</view>
			<view class="area">
				<view class="form-item">
					<view class="title">宝宝乳名</view>
					<input class="input" placeholder="请输入" placeholder-class="c3" v-model="form.baby" />
				</view>
				<view class="form-item">
					<view class="title">宝宝生日</view>
					<tm-time-picker
						:showDetail="{
							year: true,
							month: true,
							day: true,
							hour: false,
							minute: false,
							second: false
						}"
						color="red"
						v-model="form.birthday"
						:defaultValue="form.birthday"
						:immediateChange="true"
						format="YYYY-MM-DD"
						v-model:model-str="form.birthday_str"
					>
						<input class="input" placeholder="请选择" placeholder-class="c3" v-model="form.birthday_str" disabled readonly />
					</tm-time-picker>
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item">
					<view class="title">上户日期</view>
					<tm-time-picker
						:showDetail="{
							year: true,
							month: true,
							day: true,
							hour: false,
							minute: false,
							second: false
						}"
						color="red"
						v-model="form.workdate"
						:defaultValue="form.workdate"
						:immediateChange="true"
						format="YYYY-MM-DD"
						v-model:model-str="form.workdate_str"
					>
						<input class="input" placeholder="请选择" placeholder-class="c3" v-model="form.workdate_str" disabled readonly />
					</tm-time-picker>
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item">
					<view class="title">服务天数</view>
					<tm-picker color="red" v-model:model-str="form.days" :immediateChange="true" :columns="daysList" map-key="word">
						<input class="input" placeholder="请选择" placeholder-class="c3" v-model="form.days" disabled readonly />
					</tm-picker>
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item">
					<view class="title">综合评价</view>
					<view class="flex w-200 right">
						<tm-icon class="mr-10" :font-size="30" color="red" :name="form.star>=1?'tmicon-collection-fill':'tmicon-collection'" @click="form.star=1"></tm-icon>
						<tm-icon class="mr-10" :font-size="30" color="red" :name="form.star>=2?'tmicon-collection-fill':'tmicon-collection'" @click="form.star=2"></tm-icon>
						<tm-icon class="mr-10" :font-size="30" color="red" :name="form.star>=3?'tmicon-collection-fill':'tmicon-collection'" @click="form.star=3"></tm-icon>
						<tm-icon class="mr-10" :font-size="30" color="red" :name="form.star>=4?'tmicon-collection-fill':'tmicon-collection'" @click="form.star=4"></tm-icon>
						<tm-icon class="mr-10" :font-size="30" color="red" :name="form.star>=5?'tmicon-collection-fill':'tmicon-collection'" @click="form.star=5"></tm-icon>
					</view>
				</view>
				<view class="form-item">
					<view class="title">上户照片</view>
					<view class="right">
						<tm-upload 
						v-model="list" 
						:width="list.length?420:210" 
						:rows="list.length?2:1" 
						:imageHeight="200" 
						:url="uploadUrl" 
						formName="photo"
						:maxSize="15*1024*1024"
						:onSuccessAfter="onSuccess"
						:onRemove="e=>onRemove(e,'17')"
						:chooesefileAfter="chooesefileAfter"
						:formData="{
							token,
							type:'17',
							hid:form.hid
							}">
						</tm-upload>
					</view>
				</view>
				<view class="form-item form-item2 nb">
					<view class="title">您的评价</view>
					<textarea :maxlength="-1" class="textarea" placeholder-class="c3" placeholder="说点什么吧..." v-model="form.evaluation" />
				</view>
				<view class="button1" @click="doEvaluate">提交</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import {snb} from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
import { throttle } from '@/until/common'
import { useIdCardUpload } from '@/until/useIdCardUpload'

//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

// 页面数据
const {NavigationBarTitle} = snb()
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})

// Add interface for API response
interface EvaluateResponse {
	code: number
	msg: string
	data: {
		uid: string
		cid: number
		dayArr: number[]
		welcome: {
			title: string
			desc: string
		}
		startDate: string
		endDate: string
	}
}

// Update page data
const welcome = ref({
	title: '',
	desc: ''
})

// Update daysList to be reactive
const daysList = ref<{word: number, id: number}[]>([])

// 使用 useIdCardUpload
const { list, idObj, chooesefileAfter, onSuccess, onRemove } = useIdCardUpload()

const form = ref({
	hid:'',
	days:'',
	evaluation:'',
	picId:'',
	baby:'',
	birthday:'',
	birthday_str:'',
	workdate:'',
	workdate_str:'',
	star:''
})

const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const token = computed(() => store.token)

onLoad((e) => {
	if(e.scene){
	    let scene = decodeURIComponent(e.scene)
	    const params = {}
	    scene.split(',').forEach(item => {
	        const [key, value] = item.split(':')
	        params[key] = value
	    })
		console.log(params);
	    form.value.hid = params.hid
		getData(params.hid)
	} else if (e.hid) {
		form.value.hid = e.hid
		getData(e.hid)
	}

})

// 监听 idObj 变化更新 picId
watch(idObj,val=>{
	let temp = Object.keys(val).map(key=>val[key])
	form.value.picId = temp.join('|')
},{
	deep:true
})

const back = ()=>{
	uni.navigateBack({
		fail: () => {
			goLink('/pages/index/index')
		}
	});
}
const getData = async (hid:string)=>{
	const res = await api.request.ajax({
		url: '/Center/evaluate',
		type: 'POST',
		data: { hid }
	})

	if (res.code === 1 && res.data) {
		welcome.value = res.data.welcome
		daysList.value = res.data.dayArr.map(day => ({
			word: day,
			id: day
		}))
	}
}
const doEvaluate = async ()=>{
	const res = await api.request.ajax({
		url: '/Center/doEvaluate',
		type: 'POST',
		data: {
			...form.value
		}
	})
	if(res.code===1){
		uni.showToast({ icon:'success',title:res.msg })
		setTimeout(()=>{
			back()
		},2000)
	}else{
		uni.showToast({ icon:'none',title:res.msg })
	}
}
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}
.main {
	width: 750rpx;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.area{
		margin-top: 47rpx;
		padding-bottom: 160rpx;
		width: 690rpx;
		min-height: 1059rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.form-item{
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			min-height: 112rpx;
			padding: 40rpx 0;
			border-bottom: 1rpx solid #EBEBEB;
			.title{
				color: #333333;
				font-size: 26rpx;
			}
			.input{
				text-align: right;
				margin-right: 54rpx;
			}
			.right{
				margin-right: 27rpx;
			}
		}
		.form-item2{
			flex-direction: column;
			
			.title{
				width: 100%;
				text-align: left;
			}
			.textarea{
				margin-top: 34rpx;
				width: 100%;
				height: 200rpx;
				padding: 42rpx 30rpx;
				border: 1px solid #EBEBEB;
				border-radius: 10px;
			}
		}
		.nb{
			border-bottom: none;
		}
		.button1{
			width: 566rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
			border-radius: 45rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
			font-size: 30rpx;
			font-weight: 500;
		}
	}
}
</style>