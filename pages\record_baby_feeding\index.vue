<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="218" src="/static/img/info_bg3.png"></tm-image>
				<view class="absolute flex-col flex-col-center-left" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="婴儿科学喂养"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="SCIENTIFIC FEEDING"></tm-text>
				</view>
				<view class="topbox">
					<!-- <voice-record @cbResult="getCbResult"></voice-record> -->
				</view>
			</view>
			<view class="area">
				<view class="form_item">
					<view class="form_item_title">母乳喂养：<text>{{ breastFeedingCount }}</text>次</view>
					<view class="form_item_add" @click="addBreastFeeding">加一次</view>
				</view>
				<view class="form_item">
					<view class="form_item_title">奶粉喂养：<text>{{ formulaFeedingCount }}</text>次</view>
					<view class="form_item_add" @click="addFormulaFeeding">加一次</view>
				</view>
				<view class="form_item form_item2">
					<view class="form_item_title">备注记录</view>
					<view class="fulled">
						<remark-list :list="remark"></remark-list>
					</view>
				</view>
				<view class="form_item form_item2 noborder">
					<view class="form_item_title">备注：</view>
					<view class="remark-input">
						<textarea class="remark-textarea" v-model="remarkContent" :maxlength="-1"></textarea>
						<view class=" bottombox">
							<!-- <voice-record @cbResult="getCbResult"></voice-record> -->
						</view>
						<view class="voice-modal-btn" @touchstart="showVoiceModal">
							按住说话
						</view>
					</view>
				</view>
				
				<view class="submit-btn" @click="submitRecord">提交</view>
			</view>

		</view>

		<!-- 录音弹窗组件 -->
		<voice-record-modal ref="voiceRecordModalRef" @cbResult="handleVoiceModalResult"></voice-record-modal>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import voiceRecord from '@/components/voice-record/voice-record.vue'
import remarkList from '@/components/remark-list/remark-list.vue'
import voiceRecordModal from '@/components/voice-record-modal/voice-record-modal.vue'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 响应式数据
interface RemarkItem {
	content: string;
	time: string;
	type: 'text' | 'voice';
	tempFilePath?: string;
	duration?: number;
}
const breastFeedingCount = ref(3)
const formulaFeedingCount = ref(1)
const remarkContent = ref('')
const remark = ref<RemarkItem[]>([
	{
		content: '今天状态很好',
		time: '2024-6-5 11:40:42',
		type: 'text'
	},
	{
		content: '语音：02:30',
		time: '2024-6-5 11:40:42',
		type: 'voice',
		tempFilePath: '',
		duration: 150
	}
])

// 录音弹窗引用
const voiceRecordModalRef = ref(null)

// 处理录音回调
const getCbResult = (e: any) => {
	if (!e.tempFilePath) return

	const now = new Date()
	const timeStr = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`

	remark.value.push({
		content: `语音：${e.showTime}`,
		time: timeStr,
		type: 'voice',
		tempFilePath: e.tempFilePath,
		duration: e.duration
	})
}

// 显示录音弹窗
const showVoiceModal = () => {
	if (voiceRecordModalRef.value) {
		voiceRecordModalRef.value.showVoice()
	}
}

// 处理录音弹窗回调
const handleVoiceModalResult = (e: any) => {
	getCbResult(e)
}

// 增加母乳喂养次数
const addBreastFeeding = () => {
	breastFeedingCount.value++
}

// 增加奶粉喂养次数
const addFormulaFeeding = () => {
	formulaFeedingCount.value++
}

// 提交记录
const submitRecord = () => {
	// TODO: 实现提交逻辑
	uni.showToast({
		title: '提交成功',
		icon: 'success'
	})
}

</script>
<style lang="less">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.area{
		margin-top: 42rpx;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		padding: 30rpx 40rpx;
		.form_item{
			width: 100%;
			min-height: 160rpx;
			padding: 0 14rpx 0 22rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #EBEBEB;
			.form_item_title{
				font-size: 36rpx;
				color: #333333;
				text{
					color: #F31630;
					font-weight: bold;
				}
			}
			.form_item_add{
				width: 260rpx;
				height: 88rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0rpx 0rpx 10rpx 1rpx rgba(236,84,64,0.41);
				border-radius: 44rpx;
				font-size: 30rpx;
				color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
		.noborder{
			border-bottom: none;
		}
		.form_item2{
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			padding-top: 60rpx;
			padding-bottom: 60rpx;
		}
	}
	.topbox{
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 50rpx;
	}
	.bottombox{
		position: absolute;
		z-index: 2;
		bottom: 0%;
		left: 50%;
		transform: translate(-50%, 50%);
	}
	.remark-input{
		width: 100%;
		position: relative;
		.remark-textarea{
			margin-top: 26rpx;
			width: 100%;
			min-height: 270rpx;
			background: #F6F6F6;
			border-radius: 10px;
			border: 1px solid #EBEBEB;
			padding: 20rpx 30rpx;
		}
	}
	.submit-btn{
		width: 609rpx;
		height: 80rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 10rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 40rpx;
		font-size: 30rpx;
		color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 80rpx;
		margin-bottom: 40rpx;
	}
	.voice-modal-btn{
		margin-top: 20rpx;
		text-align: center;
		padding: 20rpx;
	}
}
@keyframes ripple {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.2;
	}
	100% {
		transform: translate(-50%, -50%) scale(1.4);
		opacity: 0;
	}
}
@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.1);
	}
	100% {
		transform: scale(1);
	}
}
</style>