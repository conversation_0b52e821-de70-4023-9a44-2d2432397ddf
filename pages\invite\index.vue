<template>
    <tm-app>
        <view class="main">
            <customNavigationBar :label="NavigationBarTitle||'一家亲推荐'" :scrollTop="scrollTop" :showBack="true" />
			<image src="/static/img/invite_poster.png" mode="widthFix" class="invite-bg" />
            <view class="invite-text"><text class="invite-text-name">{{auntName}}</text>邀请您加入一家亲</view>
            <view class="area">
                <view class="area-item" @click="goLink('/pages/invite_center/index?type=customer')">
                    <image src="imageUrlPrefix/invite1.jpg" mode="aspectFill" class="area-item-img" />
                    <view class="button b1">推荐客户</view>
                </view>
                <view class="area-item" @click="goLink('/pages/invite_center/index?type=aunt')">
                    <image src="imageUrlPrefix/invite2.jpg" mode="aspectFill" class="area-item-img" />
                    <view class="button b2">推荐姐妹</view>
                </view>
                <view class="area-item" @click="goLink('/pages/invite_poster/index')">
                    <image src="imageUrlPrefix/invite3.jpg" mode="aspectFill" class="area-item-img" />
                    <view class="button b3">分享我的海报</view>
                </view>
            </view>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import { useStore } from '@/until/mainpinia'
import { snb } from '@/components/customNavigationBar/snb'
import { share } from '@/tmui/tool/lib/share'
import tabber from '@/components/tabber/tabber.vue'
const store = useStore()
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline();

const scrollTop = ref(0)
const { NavigationBarTitle } = snb()

// 页面滚动
onPageScroll((e) => {
    scrollTop.value = e.scrollTop
})
const auntName = ref('皖嫂家政')
onLoad(e=>{
	if(e.auntName){
		auntName.value = e.auntName
	}
})

</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
    min-height: 100vh;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background-color: #f5f5f5;
    .invite-bg{
        width: 750rpx;
    }
    .invite-text{
        position: absolute;
        top: 250rpx;
        left: 280rpx;
        font-size: 30rpx;
        color: #DC3E1A;
        .invite-text-name{
            font-weight: bold;
        }
    }
    .area{
        width: 690rpx;
        background: #FFFFFF;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        border-radius: 20rpx 26rpx 26rpx 20rpx;
        position: relative;
        margin-top: -300rpx;
        padding: 30rpx;
        .area-item{
            width: 100%;
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 30rpx;
            &:nth-child(1){
                margin-top: 0;
            }
            .area-item-img{
                width: 100%;
                height: calc((100vh - 800rpx) / 3);
                min-height: 220rpx;
                object-fit: cover;
            }
            .button{
                position: absolute;
                right: 40rpx;
                width: 259rpx;
                height: 87rpx;
                border-radius: 44rpx;
                font-size: 32rpx;
                color: #FFFFFF;
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;

            }
            .b1{
                background: linear-gradient(-15deg, #F31630, #FF6136);
                box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
            }
            .b2{
                background: linear-gradient(-15deg, #F87C0E, #FFBF27);
                box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
            }
            .b3{
                background: linear-gradient(-15deg, #3787FF, #50BFFF);
                box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
            }
        }
    }
}
</style>