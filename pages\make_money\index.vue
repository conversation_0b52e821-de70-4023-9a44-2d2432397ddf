<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'赚钱宝'" :scrollTop="scrollTop" :showBack="!mainMenuSwitch"  />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
			<view class="resume_card">
				<image class="bg-image" src="/static/img/resume_cardbg.png" mode="aspectFill"></image>
				<view class="content">
					<view class="top">
						<view class="avatar_area" @click="showdate = true">
							<tm-image class="avatar" :width="130" :height="130"
								:src="apiData.avatarUrl||apiData.photo"></tm-image>
						</view>
						<view class="user_info">
							<view class="name_wrap">
								<text class="name">{{apiData.name}}</text>
								<text class="age" v-if="apiData.age">{{apiData.age}}</text>
							</view>
							<view class="extra_info" v-if="apiData.workYear || apiData.area">
								<text class="work_year" v-if="apiData.workYear">{{apiData.workYear}}年经验</text>
								<text class="area" v-if="apiData.area">{{apiData.area}}</text>
							</view>
						</view>
					</view>
					<view class="stats" v-if="apiData?.statistics?.switch">
						<view class="stat_item" @click="goLink('/pages/info_schedule/index')">
							<text class="num">{{apiData?.statistics?.jobsCount}}</text>
							<text class="label">累计接单</text>
						</view>
						<view class="stat_item" @click="goLink('/pages/view_evaluate/index')">
							<text class="num">{{apiData?.statistics?.evaluateCount}}</text>
							<text class="label">获得评价</text>
						</view>
						<view class="stat_item" @click="goLink('/pages/view_evaluate/index')">
							<text class="num">{{apiData?.statistics?.rate}}</text>
							<text class="label">好评率</text>
						</view>
					</view>
					<view class="amount" v-if="moneyFlag&&apiData?.statistics?.switch">
						<view class="amount_wrap">
							<view class="amount_info">
								<text class="amount_num">¥ {{apiData?.statistics?.amountTotal}}</text>
								<text class="amount_detail" @click="goLink('/pages/amount_details/index')">明细 ></text>
							</view>
							<view class="withdraw_btn" @click="goLink('/pages/withdraw/index')">立即提现</view>
						</view>
					</view>
				</view>
			</view>
			<view class="yqcard">
				<tm-image :width="690" :height="470" src="/static/img/yqcard.png" class="yqcard_bg"></tm-image>
				<view class="top">
					<view class="stit">邀请姐妹加入</view>
					<view class="desc_wrap" v-if="moneyFlag">
						<text class="desc">她签单上户你有推荐金!</text>
						<text class="link" @click="goLink('/pages/reward_details/index')">奖励明细 ></text>
					</view>
				</view>
				<view class="bottom">
					<view class="stat_box">
						<text class="stat_label">已邀请</text>
						<view class="stat_value">
							<text class="num">{{apiData.recommend?.peopleNum}}</text>
							<text class="unit">人</text>
						</view>
					</view>
					<view class="stat_box" v-if="moneyFlag">
						<text class="stat_label">已获推荐金</text>
						<view class="stat_value">
							<text class="num">{{apiData.recommend?.peopleAmount}}</text>
							<text class="unit">元</text>
						</view>
					</view>
					<tm-image class="qrcode" :width="256" :height="120" src="/static/img/wdzshb.png" @click="goLink('/pages/invite_poster/index')"></tm-image>
				</view>
			</view>
			<view class="qddt" @click="goLink(bookingHall.link)" v-if="bookingHall?.list.length>0">
				<image class="bg-image" src="/static/img/area_bg1.png" mode="aspectFill" />
				<view class="content">
					<view class="stit">
						<view class="title_wrap">
							<text class="title">{{bookingHall.title}}</text>
							<text class="subtitle">好单分享，推荐上工也有奖励哦！</text>
						</view>
					</view>
					<view class="card" v-for="item in bookingHall.list" :key="item.id">
						<view class="top">
							<text class="name">{{item.name}}</text>
							<text class="location" v-if="item.area">{{item.area}}</text>
						</view>
						<view class="bottom">
							<view class="left">
								<text class="desc" v-if="item.qddt">需求类型：{{ item.qddt }}</text>
								<text class="desc" v-if="item.zjStr&&!item.qddt">上工时段：{{item.zjStr}}</text>
								<text class="desc" v-if="item.service&&!item.qddt">需求类型：{{item.service}}</text>
							</view>
							<text class="button">我要接单</text>
						</view>
					</view>
				</view>
			</view>
			<view class="mod_card" v-if="moneyFlag">
				<text class="title">更多获取奖励金的方法</text>
				<view class="content">
					<view class="icon_content" 
						v-for="(item, index) in apiData.wayList" 
						:key="index"
						@click="goLink(item?.url)"
					>
						<tm-image :width="99" :height="91" :src="item?.bgImg"></tm-image>
						<text class="text">{{item?.title}}</text>
					</view>
				</view>
			</view>
			<view class="mod_card" v-if="moneyFlag">
				<text class="title">推广周排行</text>
				<view class="content">
					<view class="p1">
						<text class="p1_1">已有<text class="t1">130</text>人为您点赞，排名第<text class="t1">3</text></text>
						<text class="p1_1">本周：前3名，每人100元现金</text>
					</view>
					<text class="button">查看详情</text>
				</view>
			</view>
			<view class="mod_card">
				<text class="title">查看常见问题</text>
				<view class="content">
					<view class="p1">
						<text class="p1_1" v-for="(problem, index) in problems.slice(0, 2)" :key="problem.id">
							{{index + 1}}、{{problem.title}}
						</text>
					</view>
					<text class="button" @click="goLink('/pages/more_question/index')">查看详情</text>
				</view>
			</view>
			<view class="float-button">
				<!-- #ifdef MP-WEIXIN -->
				<button open-type="contact" class="contact" hover-class="none">
					<tm-image :width="145" :height="146" src="/static/img/float2.png"></tm-image>
				</button>
				<!-- #endif --> 
			</view>
			<OpendateWindow v-model:modelValue="showdate" @send="onchange"></OpendateWindow>
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref, computed, watch, getCurrentInstance } from "vue"
	import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
	import { useStore } from '@/until/mainpinia';
	import { share } from '@/tmui/tool/lib/share'
	import { goLink } from '@/until/index'
	import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
	import { snb } from '@/components/customNavigationBar/snb'
	import * as api from '@/api/index.js'
	import OpendateWindow from '@/components/opendate-Window/opendate-Window.vue'
	//分享功能
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const { NavigationBarTitle } = snb()
	// 页面数据
	const store = useStore()
	const mainMenuSwitch = computed(()=>store.mainMenuSwitch)
	const scrollTop = ref(0)

	interface Problem {
		id: string;
		title: string;
	}

	interface WayItem {
		show: boolean;
		title: string;
		url: string;
		bgImg: string;
		desc?: string;
	}

	interface ApiData {
		name: string;
		age: string;
		area: string;
		workYear: number;
		photo: string;
		avatarUrl: string;
		statistics:{
			switch:number;
			evaluateCount: number;
			jobsCount: number;
			rate: string;
			amountTotal: number;
		}
		wayList: WayItem[];
		recommend?: {
			peopleNum: number;
			peopleAmount: number;
		};
	}

	// 使用 defineProps 来定义组件的 props
	const props = defineProps<{
		scrollTop?: number;
	}>()

	// 初始化数据
	const apiData = ref<ApiData>({
		name: '',
		age: '',
		area: '',
		workYear: 0,
		photo: '',
		avatarUrl:'',
		evaluateCount: 0,
		jobsCount: 0,
		rate: '0%',
		amountTotal: 0,
		wayList: [], // 确保初始化为空数组
		recommend: {
			peopleNum: 0,
			peopleAmount: 0
		}
	})
	const moneyFlag = ref(false)
	// 确保 problems 也被正确初始化
	const problems = ref<Problem[]>([])

	// 确保 store 和其他响应式数据被正确处理
	const bookingHall = computed(() => store.setting.bookingHall || { list: [], link: '', title: '' })

	const getProblem = async () => {
		const res = await api.request.ajax({
			url: '/money/getProblem',
			type: 'POST',
		})
		if (res.code === 1) {
			problems.value = res.data.list
		}
	}

	onPageScroll((e) => {
		scrollTop.value = e.scrollTop
	})
	let count = 0
	const getData = async () => {
		const res = await api.request.ajax({
			url: '/money/index',
			type: 'POST',
		})
		if (res.code === 1) {
			apiData.value = res.data
			if(!apiData.value.avatarUrl&&!count){
				showdate.value = true
				count++
			}
			console.log(res.data);
		}
	}
	onLoad(() => {
		getData()
		getProblem()
	})
	const showdate = ref(false)
	const onchange = ()=>{
		getData()
	}
</script>

<style lang="less" scoped>
	.main {
		width: 750rpx;
		overflow-x: hidden;
		padding-bottom: 300rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}
	.newsad{
		width: 690rpx;
		height: 400rpx;
		margin-top: 60rpx;
		border-radius: 20rpx;
	}
	.resume_card {
		width: 690rpx;
		height: auto;
		min-height: 314rpx;
		position: relative;
		z-index: 1;
		box-shadow: 0rpx 0rpx 10rpx 0rpx #e6e6e6;
		border-radius: 20rpx;
		overflow: hidden;
		
		.bg-image {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
		}
		
		.content {
			position: relative;
			z-index: 2;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.top {
			width: 100%;
			display: flex;
			align-items: center;
			padding: 56rpx 20rpx 0;

			.avatar_area {
				width: 140rpx;
				height: 140rpx;
				background-color: #fff;
				border-radius: 50%;
				box-shadow: 0rpx -2rpx 7rpx 0rpx rgba(133, 43, 40, 0.4);
				display: flex;
				justify-content: center;
				align-items: center;

				.avatar {
					border-radius: 50%;
					overflow: hidden;
				}
			}

			.user_info {
				margin-left: 20rpx;
				
				.name_wrap {
					display: flex;
					flex-direction: column;

					.name {
						font-size: 30rpx;
						color: #606060;
						font-weight: bold;
					}

					.age {
						font-size: 30rpx;
						color: #606060;
						font-weight: bold;
					}
				}

				.extra_info {
					display: flex;
					align-items: center;
					margin-top: 12rpx;

					.work_year {
						font-size: 24rpx;
						color: #858585;
						margin-right: 10rpx;
					}

					.area {
						font-size: 24rpx;
						color: #858585;
					}
				}
			}
		}

		.stats {
			margin-top: 40rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 605rpx;
			border-top: 1rpx solid #E6E6E6;
			padding: 42rpx 46rpx;

			.stat_item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.num {
					font-size: 48rpx;
					color: #EA001A;
					font-weight: bold;
				}

				.label {
					font-size: 26rpx;
					color: #606060;
					margin-top: 8rpx;
				}
			}
		}

		.amount {
			position: absolute;
			top: 56rpx;
			right: 30rpx;

			.amount_wrap {
				display: flex;
				flex-direction: column;
				align-items: center;

				.amount_info {
					display: flex;
					align-items: center;

					.amount_num {
						font-size: 48rpx;
						color: #EA001A;
						font-weight: bold;
					}

					.amount_detail {
						font-size: 20rpx;
						color: #A4A4A4;
						margin-left: 20rpx;
					}
				}

				.withdraw_btn {
					width: 196rpx;
					height: 61rpx;
					background: linear-gradient(90deg, #FB243C, #F14460);
					border-radius: 30rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					font-size: 28rpx;
					margin-top: 16rpx;
				}
			}
		}
	}
	.yqcard {
		width: 690rpx;
		min-height: 470rpx;
		position: relative;
		z-index: 1;
		margin-top: 20rpx;
		padding: 50rpx;

		.yqcard_bg {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
		}

		.top {
			position: relative;
			z-index: 2;
			width: 100%;
			padding-bottom: 29rpx;
			border-bottom: 2rpx solid rgba(219, 122, 112, 0.25);

			.stit {
				font-weight: bold;
				font-size: 36rpx;
				color: #434343;

				&:before {
					content: '';
					margin-right: 16rpx;
					display: inline-block;
					width: 7rpx;
					height: 30rpx;
					background: #EC2236;
					border-radius: 4rpx;
				}
			}

			.desc_wrap {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;

				.desc {
					font-size: 24rpx;
					color: #757575;
				}

				.link {
					font-size: 20rpx;
					color: #757575;
				}
			}
		}

		.bottom {
			position: relative;
			z-index: 2;
			padding-top: 46rpx;
			display: flex;
			align-items: flex-start;
			justify-content: space-around;
			.stat_box {
				margin-right: 44rpx;
				display: flex;
				flex-direction: column;

				.stat_label {
					font-size: 24rpx;
					color: #757575;
				}

				.stat_value {
					display: flex;
					align-items: flex-end;
					margin-top: 8rpx;

					.num {
						font-size: 55rpx;
						color: #EC2236;
						font-weight: bold;
					}

					.unit {
						font-size: 29rpx;
						color: #EC2236;
						font-weight: bold;
						margin-left: 5rpx;
						margin-bottom: 5rpx;
					}
				}
			}

			.qrcode {
				margin-left: 8rpx;
			}
		}
	}
	.float-button {
		position: fixed;
		right: 30rpx;
		bottom: 200rpx;
		z-index: 99;

		.contact {
			background: none;
			padding: 0;
			margin: 0;
			line-height: 1;
			border: none;
		}
	}
</style>