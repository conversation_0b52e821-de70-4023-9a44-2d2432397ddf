{
    "name" : "皖嫂一家亲",
    "appid" : "__UNI__10D0E04",
    "description" : "皖嫂一家亲",
    "versionName" : "1.0.0",
    "versionCode" : 100,
    "transformPx" : false,
    /* 5+App特有相关  */
    "app-plus" : {
        "usingComponents" : true,
        "ignoreVersion" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            /* 使用Canvas模块 */
            "Canvas" : "nvue canvas",
            "VideoPlayer" : {},
            "Geolocation" : {},
            "Camera" : {},
            "Share" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"
                ],
                "minSdkVersion" : 24,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "便于您使用该功能上传您的照片/图片/视频、用于更换头像、发布评论/分享、下载、与客服沟通等场景中读取和写入相册和文件内容。"
                },
                "permissionPhoneState" : {
                    "request" : "none",
                    "prompt" : "便于您使用该功能拨打电话给商家、客服等。"
                },
                "targetSdkVersion" : 30
            },
            /* ios打包配置 */
            "ios" : {
                "idfa" : false,
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "APP需要您的同意，才能访问相册进行选择照片上传/发布信息，如禁止将无法上传选择照片上传/发布信息",
                    "NSCameraUsageDescription" : "APP需要您的同意，才能访问相机进行拍摄您的用户头像，如禁止将无法拍摄用户头像无法更新信息",
                    "NSLocationWhenInUseUsageDescription" : "",
                    "NSContactsUsageDescription" : ""
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx2cbc5cc5228f8788",
                        "UniversalLinks" : "https://www.wansao.com/statics/iosshare/apple-app-site-association/"
                    }
                },
                "payment" : {},
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "maps" : {},
                "oauth" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            }
        },
        "uniStatistics" : {
            "enable" : false
        },
        "nativePlugins" : {}
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxcf2a6bc3466f2bab",
        "setting" : {
            "urlCheck" : true,
            "es6" : true,
            "minified" : true,
            "postcss" : true
        },
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        },
        "requiredPrivateInfos" : [ "getLocation" ],
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于导航"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        },
        "appid" : ""
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "uniStatistics" : {
            "enable" : false
        },
        "appid" : ""
    },
    "uniStatistics" : {
        "enable" : false,
        "version" : "2"
    },
    "vueVersion" : "3",
    "h5" : {
        "uniStatistics" : {
            "enable" : false
        },
        "title" : "皖嫂一家亲",
        "router" : {
            "mode" : "hash",
            "base" : "/h5/"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "devServer" : {
            "https" : true
        }
    },
    "mp-jd" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-kuaishou" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-lark" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        },
        "setting" : {
            "es6" : true
        },
        "appid" : ""
    },
    "quickapp-webview-huawei" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "quickapp-webview-union" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "fallbackLocale" : "zh-Hans",
    "locale" : "zh-Hans",
    "channel_list" : [
        {
            "id" : "tencent",
            "name" : "腾讯"
        },
        {
            "id" : "honor",
            "name" : "荣耀"
        }
    ]
}
