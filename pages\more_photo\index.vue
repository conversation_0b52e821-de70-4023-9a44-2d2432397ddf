<template>
  <tm-app ref="app">
    <view class="main safe-area">
      
      <!-- 自定义Tab切换 -->
      <view class="tabs">
        <view 
          v-for="(tab, idx) in photoTab" 
          :key="tab.index"
          class="tab-item"
          :class="{ 'active': currentTab === tab.index }"
          @click="tabChange(idx)"
        >
          {{ tab.title }}
          <view class="tab-line" v-if="currentTab === tab.index"></view>
        </view>
      </view>

      <!-- 相册列表 -->
      <view class="photo-list" v-if="photoList?.length&&photoList!==null" :class="{'fade-in': photoList.length}">
        <tm-image-group>
          <view class="photo-grid">
            <view 
              class="photo-item"
              v-for="(item, index) in photoList" 
              :key="index"
              @click="preview(photoList.map(img => img.sourceUrl), index)"
            >
              <tm-image
                :width="170" 
                :height="170" 
                :round="7" 
                :src="item.thumbUrl"
                model="aspectFill" 
                class="photo-image"
              />
            </view>
          </view>
        </tm-image-group>
		<!-- 加载状态 -->
		<view class="nomore" v-if="photoList.length&&finished">没有更多了</view>
      </view>
      <!-- 空状态 -->
      <tm-result v-if="!photoList?.length&&photoList!==null" title="暂无相册" />
    </view>
  </tm-app>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onLoad, onPageScroll, onReachBottom } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import * as api from '@/api/index.js'
import { parseParams } from '@/until/parseParams'

const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// 定义接口类型
interface PhotoItem {
  thumbUrl: string
  sourceUrl: string
}

interface PhotoData {
  resumePhoto: {
    [key: string]: {
      count: number
      thisPage: number
      pageTotal: number
      list: PhotoItem[]
    }
  }
  photoTab: Array<{
    index: string
    title: string
  }>
  shareData: {
    title: string
    path: string
    imageUrl: string
  }
  shareTimeline: string
}

// 添加分页相关的响应式数据
const page = ref(1)
const loading = ref(false)
const finished = ref(false)
const hid = ref('')
const port = ref('')
const photoTab = ref<PhotoData['photoTab']>([])
const photoList = ref<PhotoItem[]>(null)
const currentTab = ref('')
const photoData = ref<PhotoData | null>(null)

// 获取相册数据
const getPhotoList = async () => {
  const res = await api.request.ajax({
    url: '/Center/photosMore',
    type: 'POST',
    data: { 
		hid: hid.value,
		port: port.value,
	}
  })
  
  if (res.code === 1) {
    photoData.value = res.data
    photoTab.value = res.data.photoTab
    if(photoTab.value.length > 0){
      currentTab.value = photoTab.value[0].index
    }
    const photos = res.data.resumePhoto[currentTab.value]?.list || []
    photoList.value = photos
    // 设置分享内容
    setShareApp(res.data.shareData)
    // 设置朋友圈分享内容
    if (res.data.shareTimeline) {
      setShareTime(res.data.shareTimeline)
    }
    // 重置分页状态
    page.value = 1
    finished.value = false
  }
}

// 加载更多数据
const loadMore = async () => {
  if (loading.value || finished.value) return
  
  loading.value = true
  try {
    const res = await api.request.ajax({
      url: '/Center/photosMorePage',
      type: 'POST',
      data: {
        page: page.value + 1,
        hid: hid.value,
		port: port.value,
        column: currentTab.value
      }
    })
    
    if (res.code === 1) {
      const newPhotos = res.data.list
      photoList.value.push(...newPhotos)
      page.value++
      
      if (page.value >= res.data.pageTotal) {
        finished.value = true
      }
    } else {
      finished.value = true
    }
  } catch (error) {
    console.error('加载更多失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听页面触底
onReachBottom(() => {
  loadMore()
})

// 切换Tab
const tabChange = (index: number) => {
  
  if (!photoTab.value || !photoTab.value[index]) {
    console.error('Invalid tab index or photoTab not initialized');
    return;
  }

  const newTab = photoTab.value[index].index;
  
  currentTab.value = newTab;
  
  if (!photoData.value?.resumePhoto) {
    console.error('photoData or resumePhoto not initialized');
    return;
  }

  const photos = photoData.value.resumePhoto[currentTab.value]?.list || [];
  
  photoList.value = photos;
  // 重置分页状态
  page.value = 1;
  finished.value = false;
}

// 预览图片
const preview = (list: string[], index: number) => {
  uni.previewImage({
    urls: list,
    current: index
  })
}

onLoad((e) => {
    const params = parseParams(e)
	hid.value = params.hid || hid.value
	port.value = params.port||''
    getPhotoList()
})
</script>

<style lang="less" scoped>
.main {
  width: 750rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  
  &.safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .tabs {
    width: 750rpx;
    height: 88rpx;
    display: flex;
    align-items: center;
    background: #fff;
    position: relative;
    border-bottom: 1rpx solid rgba(0,0,0,0.05);
    margin-bottom: 0;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

    .tab-item {
      position: relative;
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #333;
      transition: all 0.25s ease;

      &.active {
        color: #F83B3B;
        font-weight: bold;
      }

      .tab-line {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: #F83B3B;
        border-radius: 2rpx;
        animation: slideIn 0.3s ease-in-out;
      }
    }
  }

  .photo-list {
    padding: 30rpx 25rpx;
    opacity: 0;
    transition: opacity 0.3s ease;
    background-color: #fff;
    margin-top: 20rpx;
    display: flex;
	flex-direction: column;
	align-items: center;
	.nomore{
		color: #999;
		font-size: 24rpx;
		text-align: center;
		padding: 30rpx 0;
		position: static;
	}
    &.fade-in {
      opacity: 1;
    }

    .photo-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15rpx;
      
      .photo-item {
        aspect-ratio: 1;
        
        .photo-image {
          transition: transform 0.2s ease;
          
          &:active {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  .loading-status {
    padding: 20rpx 0;
    text-align: center;
    color: #999;
    font-size: 24rpx;
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 60rpx;
    opacity: 1;
  }
}
</style> 