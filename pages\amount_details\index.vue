<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle || '金额明细'" :scrollTop="scrollTop" :showBack="true"/>
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="mod_card">
				<view class="title">
					提现记录
					<view class="filter" @click="showFilter = true">
						<text>{{ currentFilter || '全部' }}</text>
						<tm-icon :font-size="20" color="#000" name="tmicon-angle-down"></tm-icon>
					</view>
				</view>
				<view class="amount_record">
					<template v-if="filteredRecord.length">
						<view class="item" v-for="(item, index) in filteredRecord" :key="index">
							<view class="item_left">
								<view class="item_title">{{ item.title }}</view>
								<view class="item_time">{{ item.time }}</view>
							</view>
							<view class="item_value" :class="item.value>0 ? 'amount_red' : ''">{{ item.value }}</view>
						</view>
					</template>
					<view class="no_data" v-else>
						<tm-text :font-size="28" color="#999" label="暂无数据"></tm-text>
					</view>
					<view class="back" @click="goBack">返回</view>
				</view>
			</view>
			<tm-picker 
				v-model:show="showFilter" 
				:columns="filterList" 
				v-model:model-str="currentFilter" 
				:immediateChange="true"
			></tm-picker>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'

//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const { NavigationBarTitle } = snb()

// 页面数据
const store = useStore()
const scrollTop = ref(0)
const showFilter = ref(false)
const currentFilter = ref('')
const amountRecord = ref([])

// 计算筛选列表
const filterList = computed(() => {
	const titles = ['全部', ...new Set(amountRecord.value.map(item => item.title))]
	return titles.map(text => ({ text, value: text }))
})

// 计算筛选后的记录
const filteredRecord = computed(() => {
	if (!currentFilter.value || currentFilter.value === '全部') {
		return amountRecord.value
	}
	return amountRecord.value.filter(item => item.title === currentFilter.value)
})

onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})

const getData = async () => {
	const res = await api.request.ajax({
		url: '/money/withdrawList',
		type: 'POST',
	})
	if (res.code === 1) {
		// 转换数据格式
		amountRecord.value = res.data.map(item => ({
			title: item.title,
			time: item.time,
			value: item.amount > 0 ? '+' + item.amount.toFixed(2) : item.amount.toFixed(2)
		}))
	}
}

onLoad(() => {
	getData()
})

const goBack = () => {
	uni.navigateBack()
}
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.mod_card{
		.filter {
			position: absolute;
			right: 40rpx;
			display: flex;
			align-items: center;
			font-size: 26rpx;
			color: #666;
			
			text {
				margin-right: 10rpx;
			}
		}
		.no_data {
			padding: 40rpx 0;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>