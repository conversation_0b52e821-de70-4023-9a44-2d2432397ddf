<template>
    <tm-app>
        <view class="main">
            <customNavigationBar :label="NavigationBarTitle||'入驻一家亲'" :scrollTop="scrollTop" :showBack="true" />
			<view class="swipe">
				<tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
					color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goLink(bannerList[i].link)"></tm-carousel>
			</view>
            <view class="container">
                <view class="title">
                    <text class="title-bar"></text>
                    加入皖嫂一家亲
                </view>
                <view class="avatar">
                    <image :src="auntInfo.picpath" mode="widthFix" class="avatar-icon" />
                    <view class="avatar-name">皖嫂 <text class="avatar-name-text">{{auntInfo.number}}</text></view>
                    <view class="avatar-invite">邀您加入皖嫂一家亲</view>
                </view>
                
                <!-- 经验选择 -->
                <view class="experience-select">
                    <text class="text">有无经验</text>
                    <view class="btn-group">
                        <view class="btn" 
                            v-for="item in experienceList" 
                            :key="item.value"
                            :class="{ active: experience === item.label }" 
                            @click="selectExperience(item.label, item.value)">
                            {{item.label}}
                        </view>
                    </view>
                </view>

                <!-- 申请表单 -->
                <view class="form-wrapper">
                    <view class="form-item" @click="openWorkTypePicker">
                        <text class="text">类型</text>
                        <input class="input" type="text" v-model="workType" placeholder="请选择您的服务类型" placeholder-style="color: #D3D2D2" disabled />
                    </view>

                    <view class="form-item">
                        <text class="text">姓名</text>
                        <input class="input" type="text" v-model="username" placeholder="请输入您的姓名" placeholder-style="color: #D3D2D2" />
                    </view>

                    <view class="form-item">
                        <text class="text">手机</text>
                        <input class="input" type="text" v-model="phone" placeholder="请输入您的手机号" placeholder-style="color: #D3D2D2" />
                    </view>
                </view>

                <!-- <view class="notice-text">
                    <image src="/static/img/apply_lx.png" mode="widthFix" class="notice-icon" />
                    <text>皖嫂工作人员将电话与您沟通，请保持电话畅通</text>
                </view> -->

                <view class="submit-btn" @click="submit">立即提交审核</view>

                <!-- <view class="help-links">
                    <text @click="goLink('/pages/more_question/index')">什么是皖嫂一家亲?</text>
                    <text @click="goLink('/pages/more_question/index')">怎么领取推荐奖励金?</text>
                    <text @click="goLink('/pages/more_question/index')">怎么提现到银行卡?</text>
                </view> -->

            </view>

            <!-- 添加工作类型选择器 -->
            <tm-picker
                :show="showPicker"
                :columns="workTypeList"
                @confirm="onConfirmWorkType"
                @cancel="showPicker = false"
                v-model:model-str="workType"
                :immediateChange="true"
            />
            <tm-overlay v-model:show="showWin" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
                <view class="dkpopup">
                    <image src="/static/img/yqtk.png" mode="widthFix" class="dkcg"></image>
                    <view class="content content3">
                        <text class="content-text">您已是一家亲成员</text>
                        <text class="content-text">可直接推荐客户，推荐阿姨</text>
                        <text class="content-text">分享专属海报</text>
                        <text class="content-text">邀请其他姐妹吧！</text>
                    </view>
                    <view class="confirm" @click="goLink('/pages/invite/index?auntName='+auntInfo.number)">好的</view>
                </view>
            </tm-overlay>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import { useStore } from '@/until/mainpinia'
import { snb } from '@/components/customNavigationBar/snb'
import { share } from '@/tmui/tool/lib/share'
import { parseParams } from '@/until/parseParams'
const store = useStore()

const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline();

// 数据定义
const auntInfo = ref({
    number: '',
    picpath: ''
})
const showWin = ref(false)
const username = ref('')
const phone = ref('')
const experienceList = ref([])
const workTypeList = ref([])
const experience = ref('有无工作经验')
const workType = ref('')
const experienceIndex = ref(0)
const workTypeIndex = ref(0)
const service_id = ref(null)
const hid = ref(0)
const uid = ref(0)
const shareData = ref({})
const bannerList = ref([])
const scrollTop = ref(0)
const { NavigationBarTitle } = snb()
const showPicker = ref(false)

// 页面滚动
onPageScroll((e) => {
    scrollTop.value = e.scrollTop
})

// 获取页面数据
const getIndexInfo = async () => {
    const res = await api.request.ajax({
        url: '/money/apply',
        type: 'POST',
        data: {
            hid: hid.value,
            uid: uid.value
        }
    })

    if (res.code === 1) {
        auntInfo.value = res.data.auntName
        experienceList.value = res.data.experience
        workTypeList.value = res.data.serviceList.map(item => ({
            text: item.label,
            value: item.value
        }))
        shareData.value = res.data.shareData
        bannerList.value = res.data.banners
        // 设置分享内容
        setShareApp(res.data.shareData)
        setShareTime(res.data.shareTimeline)
    }
}

// 选择经验
const selectExperience = (label: string, value: number) => {
    experience.value = label
    experienceIndex.value = value
}

// 替换原来的 showWorkTypePicker 方法
const openWorkTypePicker = () => {
    if (workTypeList.value.length === 0) {
        uni.showToast({
            title: '暂无可选工作类型',
            icon: 'none'
        })
        return
    }
    showPicker.value = true
}

// 添加确认选择方法
const onConfirmWorkType = (e: any) => {
    const selectedItem = workTypeList.value.find(item => item.text === workType.value)
    if (selectedItem) {
        service_id.value = selectedItem.value
        workTypeIndex.value = workTypeList.value.indexOf(selectedItem)
    }
    showPicker.value = false
}


// 提交申请
const submit = async () => {
    if (experience.value === '有无工作经验') {
        uni.showToast({
            icon: 'none',
            title: '请选择有无工作经验'
        })
        return false
    }

    if (!workType.value) {
        uni.showToast({
            icon: 'none',
            title: '请选择您需要的工作类型'
        })
        return false
    }

    if (!username.value) {
        uni.showToast({
            icon: 'none',
            title: '请输入姓名'
        })
        return false
    }

    if (!(/^1[3456789]\d{9}$/.test(phone.value))) {
        uni.showToast({
            icon: 'none',
            title: '请输入正确的手机号'
        })
        return false
    }

    const res = await api.request.ajax({
        url: '/money/doApply',
        type: 'POST',
        data: {
            hid: hid.value,
            uname: username.value,
            phone: phone.value,
            service_id: service_id.value,
            experience: experienceIndex.value
        }
    })

    if (res.code === 1) {
        uni.showToast({
            title: res.msg,
            success: () => {
                uni.navigateBack()
            }
        })
    } else {
        uni.showToast({
            title: res.msg,
            icon: 'none'
        })
    }
}

// 页面加载
onLoad(async (e) => {
    const params = parseParams(e)
    hid.value = params.hid || hid.value
    uid.value = params.uid || uid.value

    getIndexInfo()
    api.getUserAccountInfo().then(res=>{
        if(res.status === 9){
            showWin.value = true
        }
    })
})

</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background-color: #fff;

    .container {
        margin-top: 280rpx;
        width: 690rpx;
        // height: 1340rpx;
        background: #FFFFFF;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
        border-radius: 20rpx 26rpx 26rpx 20rpx;
        padding: 30rpx;
        position: relative;
        .title {
            font-size: 32rpx;
            padding-bottom: 20rpx;
            border-bottom: 1rpx solid #EBEBEB;
            color: #333333;
            font-weight: bold;
            display: flex;
            align-items: center;

            .title-bar {
                width: 8rpx;
                height: 40rpx;
                border-radius: 2rpx;
                background: #FF4B4B;
                margin-right: 20rpx;
            }
        }
        .avatar {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-top: 30rpx;
            margin-bottom: 70rpx;

            .avatar-icon {
                width: 200rpx;
                height: 200rpx;
                background: #FFFFFF;
                box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
                border-radius: 50%;
            }
            .avatar-name {
                font-weight: bold;
                font-size: 36rpx;
                color: #606060;
                margin-top: 20rpx;
                .avatar-name-text {
                    font-size: 36rpx;
                    color: #E82F1C;
                }
            }
            .avatar-invite {
                font-size: 36rpx;
                color: #858585;
                margin-top: 10rpx;
            }
        }

        .experience-select {
            width: 610rpx;
            height: 100rpx;
            padding-left: 20rpx;
            border-bottom: 1rpx solid #EBEBEB;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .text{
                font-size: 28rpx;
            }
            .btn-group {
                display: flex;

                .btn {
                    flex-shrink: 0;
                    width: 187rpx;
                    height: 80rpx;
                    line-height: 80rpx;
                    text-align: center;
                    border: 2rpx solid #EEEEEE;
                    border-radius: 8rpx;
                    color: #999;
                    margin-left: 38rpx;

                    &.active {
                        background: #FF4B4B;
                        color: #fff;
                        border: none;
                    }
                }
            }
        }

        .form-wrapper {
            .form-item {
                width: 610rpx;
            height: 100rpx;
            padding-left: 20rpx;
            border-bottom: 1rpx solid #EBEBEB;
            display: flex;
            align-items: center;
            justify-content: space-between;
                .text{
                    width: 120rpx;
                    font-size: 28rpx;
                    color: #333333;
                    white-space: nowrap;
                }
                input {
                    width: 400rpx;
                    height: 90rpx;
                    border-radius: 8rpx;
                    padding: 0 30rpx;
                    box-sizing: border-box;
                    font-size: 28rpx;
                    text-align: right;

                    &::placeholder {
                        color: #999;
                    }
                }
            }
        }

        .notice-text {
            display: flex;
            align-items: center;
            color: #D3D2D2;
            font-size: 20rpx;
            text-align: center;
            margin: 30rpx 0;
            justify-content: center;

            .notice-icon {
                width: 28rpx;
                margin-right: 10rpx;
            }
        }

        .submit-btn {
            width: 100%;
            height: 90rpx;
            line-height: 90rpx;
            text-align: center;
            background: linear-gradient(to right, #FF7A45, #FF4B4B);
            color: #fff;
            font-size: 32rpx;
            border-radius: 45rpx;
            margin: 50rpx 0;
        }

        .help-links {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            text-align: center;
            color: #D3D2D2;
            font-size: 20rpx;

            text {
                margin-bottom: 30rpx;
                margin: 0 20rpx;
            }
        }
    }

}
</style>