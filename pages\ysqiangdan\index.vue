<template>
    <tm-app>
        <view class="main">
            <image :src="bg" mode="widthFix" class="topCont" />
            <view class="topContTab">
                <view class="topContTabItem">
                    <image src="/static/img/ysqb_jb.png" mode="widthFix" class="topContTabItem-icon" />
                    <view class="topContTabItem-t">我的皖嫂币：{{ wsb }}个</view>
                </view>
                <view class="topContTabShu"></view>
                <navigator url="../wsb_mx/index" class="topContTabItem">
                    <image src="/static/img/ysqb_mx.png" mode="widthFix" class="topContTabItem-icon" />
                    <view class="topContTabItem-t">查看我的明细</view>
                </navigator>
            </view>

            <view class="notice" v-if="tips.length > 0">
                <image src="/static/img/ysqb_notice.png" mode="widthFix" class="notice-icon" />
                <view class="text-scroll">
                    <view class="text-scroll-content" :style="{ transform: `translateY(-${currentIndex * 20}px)` }">
                        <view class="text-scroll-item" v-for="(item, index) in tips" :key="index">
                            {{item}}
                        </view>
                    </view>
                </view>
            </view>

            <!-- 固定在顶部的菜单 -->
            <view class="sticky-header">
                <view class="menu">
                    <tm-filterMenu ref="filter" fixed>
                        <tm-filterMenu-item 
                            v-for="(config, type) in dropdownConfig" 
                            :key="type"
                            :title="config.title" 
                            :height="(24+config.options.length*80)>800?800:24+config.options.length*80"
                            fontColor="#FB243C"
                        >
                            <view class="filter-dropdown">
                                <view 
                                    v-for="item in config.options" 
                                    :key="item.value" 
                                    class="dropdown-item"
                                    :class="{ active: config.value === item.value }"
                                    @click="handleDropdownChange(item.value, type)"
                                >
                                    {{item.text}}
                                </view>
                            </view>
                        </tm-filterMenu-item>
                    </tm-filterMenu>
                </view>
                <view class="selc-nav">
                    <view class="selc-item" :class="{ 'on': select == 1 }" @click="selectbox(1)">接单大厅</view>
                    <view class="selc-item" :class="{ 'on': select == 2 }" @click="selectbox(2)">我的接单</view>
                </view>
            </view>

            <!-- 订单列表区域 -->
            <view class="order-list" v-if="select == 1">
                <view class="card" v-for="item in orderList" :key="item.id">
                    <view class="card-header">
                        <template v-if="item.qddt">
                            <view class="qddt">{{ item.qddt }}</view>
                        </template>
                        <view v-else class="employer-info">
                            <view class="name">{{ item.name }}</view>
                            <view v-if="item.city" class="location">{{ item.city }}{{ item.area }}</view>
                        </view>
                    </view>
                    
                    <view class="card-content" v-if="!item.qddt">
                        <view class="info-row" v-if="item.service">
                            <text class="label">需求类型</text>
                            <text class="value highlight">{{ item.service }}</text>
                        </view>
                        <view class="info-row" v-if="item.zjStr">
                            <text class="label">上工时段</text>
                            <text class="value">{{ item.zjStr }}</text>
                        </view>
                    </view>

                    <view class="card-footer">
                        <view class="left-area">
                            <view class="distance" v-if="item.distance">距当前{{ item.distance }}</view>
                        </view>
                        <view class="right-area">
                            <view class="button button1" @click="grabOrder(item.id)">我要接单</view>
                        </view>
                    </view>
                </view>
                <view class="nomore" v-if="canReq">没有更多数据了</view>
            </view>

            <view class="order-list" v-if="select == 2">
                <view class="card" v-for="item in orderList2" :key="item.id">
                    <view class="card-header">
                        <template v-if="item.qddt">
                            <view class="qddt">{{ item.qddt }}</view>
                        </template>
                        <view v-else class="employer-info">
                            <view class="name">{{ item.name }}</view>
                            <view v-if="item.city" class="location">{{ item.city }}{{ item.area }}</view>
                        </view>
                    </view>
                    
                    <view class="card-content" v-if="!item.qddt">
                        <view class="info-row" v-if="item.service">
                            <text class="label">需求类型</text>
                            <text class="value highlight">{{ item.service }}</text>
                        </view>
                        <view class="info-row" v-if="item.zjStr">
                            <text class="label">上工时段</text>
                            <text class="value">{{ item.zjStr }}</text>
                        </view>
                    </view>

                    <view class="card-footer">
                        <view class="left-area">
                            <view class="distance" v-if="item.distance">距当前{{ item.distance }}</view>
                        </view>
                        <view class="right-area">
                            <view class="button button2" @click="cancelOrder(item.id)">取消接单</view>
                            <view class="button button3" v-if="item.teacher_phone" @click="dial(item.teacher_phone)">联系老师</view>
                        </view>
                    </view>
                </view>
                <view class="nomore" v-if="canReq">没有更多数据了</view>
            </view>

            <view class="nav-bottom">
                <view class="selc-item2">
                    <view class="text1" @click="goTuijian">推荐客户</view>
                </view>
                <view class="selc-item2">
                    <view class="text1" @click="goLink('/pages/ysduihuan/index')">我要上工</view>
                </view>
                <view class="selc-item2">
                    <view class="text1" @click="goLink('/pages/buywsb/index')">购买皖嫂币</view>
                </view>
            </view>
            <phoneinfo v-model="showPhoneInfo"/>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onShow, onLoad, onReachBottom } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import { goLink } from '@/until/index'
import phoneinfo from '@/components/phoneinfo/phoneinfo.vue'

//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

const store = useStore()

// 数据
const bg = ref("https://wx.wansao.com/statics/family/sendhall/ysqb_bg.png")
const select = ref(1)
const wsb = ref(0)
const tips = ref([])
const orderList = ref([])
const orderList2 = ref([])
const canReq = ref(false)
const page = ref(1)
const latitude = ref(null)
const longitude = ref(null)
const hid = computed(() => store?.userInfo?.hid)
const showPhoneInfo = ref(false)
const resume = ref(0)
const wxphone = ref(null)
const contact_phone = ref('18000000000')
const locationAuth = ref(false)
const currentIndex = ref(0)

// 删除原来的变量，改用统一的配置对象
const dropdownConfig = ref({
    order_type: {
        value: 0,
        title: '订单类型',
        options: [
            { text: '订单类型', value: 0 },
            { text: '月嫂', value: 1 },
            { text: '育婴师', value: 2 }
        ]
    },
    area_id: {
        value: 0,
        title: '工作区域',
        options: [{ text: '工作区域', value: 0 }]
    },
    zhujia_types: {
        value: 0,
        title: '上工时段',
        options: [{ text: '上工时间', value: 0 }]
    }
})

const filter = ref()

// 简化后的下拉切换函数
const handleDropdownChange = (value: any, type: keyof typeof dropdownConfig.value) => {
    const config = dropdownConfig.value[type]
    // 更新值
    config.value = value
    
    // 更新标题
    const selectedOption = config.options.find(item => item.value === value)
    if (selectedOption) {
        config.title = selectedOption.text
    }
    
    // 重置页码并重新获取数据
    page.value = 1
    getOrderList()
    
    // 关闭下拉菜单
    filter.value?.close()
}

const goTuijian = () => {
    if(store?.userInfo?.phone){
        goLink('/pages/tuijian/index')
    }else{
        showPhoneInfo.value = true
    }
}

// 方法
// 获取订单列表
const getOrderList = async () => {
    const res = await api.request.ajax({
        url: 'https://wx.wansao.com/api/Sendhall/orders',
        type: 'POST',
        data: {
            page: page.value,
            area_id: dropdownConfig.value.area_id.value,
            zhujia_types: dropdownConfig.value.zhujia_types.value,
            order_type: dropdownConfig.value.order_type.value,
            latitude: latitude.value,
            longitude: longitude.value
        }
    })
    if (res.code === 1) {
        if (page.value === 1) {
            orderList.value = res.data.orderList
        } else {
            orderList.value = [...orderList.value, ...res.data.orderList]
        }
        canReq.value = res.data.canReq
    }
}

// 获取我的订单列表
const getMyOrderList = async () => {
    const res = await api.request.ajax({
        url: 'https://wx.wansao.com/api/Sendhall/myorder',
        type: 'POST',
        data: { page: page.value }
    })
    if (res.code === 1) {
        if (page.value === 1) {
            orderList2.value = res.data.orderList
        } else {
            orderList2.value = [...orderList2.value, ...res.data.orderList]
        }
        canReq.value = res.data.canReq
    }
}

// 获取位置信息
const getLocation = async () => {
    try {
        // 先检查授权状态
        const authSetting = await uni.getSetting()
        if (!authSetting.authSetting['scope.userLocation']) {
            // 未授权,先请求授权
            await uni.authorize({ scope: 'scope.userLocation' })
        }

        // 获取位置
        const location = await uni.getLocation({
            type: 'gcj02',
        })

        latitude.value = location.latitude
        longitude.value = location.longitude
        locationAuth.value = true
        return true

    } catch (err) {
        console.log('获取位置失败:', err)
        // 用户拒绝授权或其他错误
        uni.showModal({
            title: '微信授权',
            content: '获取您当前地址，可提供更多精准服务',
            success: async (res) => {
                if (res.confirm) {
                    const settingRes = await uni.openSetting()
                    if (settingRes.authSetting['scope.userLocation']) {
                        const success = await getLocation()
                        if (success) {
                            getOrderList()
                        }
                    } else {
                        uni.showToast({
                            title: '请允许获取位置信息',
                            icon: 'none'
                        })
                    }
                } else {
                    uni.showToast({
                        title: '已拒绝授权位置信息',
                        icon: 'none'
                    })
                }
            }
        })
        return false
    }
}

const selectbox = (type: number) => {
    select.value = type
    page.value = 1
    if (type === 1) {
        getOrderList()
    } else {
        getMyOrderList()
    }
}

// 接单
const grabOrder = async (id: string) => {
    const res = await api.request.ajax({
        url: 'https://wx.wansao.com/api/Sendhall/doBooking',
        type: 'POST',
        data: { yid:id,hid:hid.value }
    })
    if (res.code === 1) {
        uni.showModal({
            title: '接单成功',
            content: res.msg,
            showCancel: false,
            success: () => {
                getOrderList()
            }
        })
        getOrderList()
    } else {
        uni.showToast({ title: res.msg, icon: 'none' })
    }
}

// 取消接单
const cancelOrder = async (id: string) => {
    uni.showModal({
        title: '提示',
        content: '确定要取消接单吗？',
        success: async (res) => {
            if (res.confirm) {
                const result = await api.request.ajax({
                    url: 'https://wx.wansao.com/api/Sendhall/cancelBook',
                    type: 'POST',
                    data: { yid:id,hid:hid.value }
                })
                if (result.code === 1) {
                    uni.showToast({ title: result.msg })
                    getMyOrderList()
                } else {
                    uni.showToast({ title: result.msg, icon: 'none' })
                }
            }
        }
    })
}

const dial = (phone: string) => {
    uni.makePhoneCall({
        phoneNumber: phone
    })
}

// 添加轮播控制函数
const startScroll = () => {
    setInterval(() => {
        if (tips.value.length > 0) {
            currentIndex.value = (currentIndex.value + 1) % tips.value.length
        }
    }, 3000) // 每3秒切换一次
}

// 在获取到 tips 数据后启动轮播
const getPageInfo = async () => {
    const res = await api.request.ajax({
        url: 'https://wx.wansao.com/api/Sendhall/pageinfo',
        type: 'POST'
    })
    
    if (res.code === 1) {
        const { service_types, area, zhujia_types, tips: pageInfoTips } = res.data
        
        // 更新订单类型选项
        dropdownConfig.value.order_type.options = [
            { text: '订单类型', value: 0 },
            ...service_types.map(item => ({
                text: item.name,
                value: item.id
            }))
        ]
        
        // 更新工作区域选项
        dropdownConfig.value.area_id.options = [
            { text: '工作区域', value: 0 },
            ...area.map(item => ({
                text: item.name,
                value: item.id
            }))
        ]
        
        // 更新上工时段选项
        dropdownConfig.value.zhujia_types.options = [
            { text: '上工时间', value: 0 },
            ...zhujia_types.map(item => ({
                text: item.name,
                value: item.id
            }))
        ]
        
        // 更新滚动提示
        tips.value = pageInfoTips
        // 启动文字轮播
        startScroll()
    }
    const res2 = await api.request.ajax({
        url: 'https://wx.wansao.com/api/Sendhall/index',
        type: 'POST'
    })
    if(res2.code === 1||res2.code === 200){
        bg.value = res2.data.bg
		wsb.value = res2.data.total
		// swiperqd
    }
}

onLoad(async () => {
    // 获取页面配置信息
    await getPageInfo()
    // 获取位置
    await getLocation()
    getOrderList()
})

onShow(() => {
    // 页面显示时,如果之前未获取到位置,则重试
    if (!locationAuth.value) {
        getLocation()
    }
    if (select.value === 1) {
        getOrderList()
    } else {
        getMyOrderList()
    }
})

// 上拉加载更多
onReachBottom(() => {
    if (!canReq.value) {
        page.value++
        if (select.value === 1) {
            getOrderList()
        } else {
            getMyOrderList()
        }
    }
})
</script>

<style lang="scss" scoped>
.main {
    width: 750rpx;
    min-height: 100vh;
    background: #f3f3f3;
    padding-bottom: 120rpx;

    .topCont{
        width: 750rpx;
    }
    .topContTab {
        margin-top: -160rpx;
        margin-bottom: 40rpx;
        margin-left: 60rpx;
        width: 630rpx;
        position: relative;
        height: 164rpx;
        background: rgba(255, 255, 255, 1);
        border-radius: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .topContTabItem {
            width: 300rpx;
            text-align: center;

            &-icon {
                width: 26rpx;
                height: 28rpx;
            }

            &-t {
                margin-top: 10rpx;
                font-size: 26rpx;
                color: #333;
            }
        }

        .topContTabShu {
            width: 2rpx;
            height: 83rpx;
            background: #eaebed;
        }
    }

    .notice {
        margin-left: 58rpx;
        width: 634rpx;
        height: 60rpx;
        background: #FFFFFF;
        border-radius: 8rpx;
        display: flex;
        align-items: center;
        padding: 0 34rpx;
        position: relative;

        .notice-icon {
            width: 20rpx;
            height: 20rpx;
        }

        .text-scroll {
            flex: 1;
            height: 20px;
            overflow: hidden;
            margin-left: 20rpx;
            
            &-content {
                transition: transform 0.5s;
            }
            
            &-item {
                height: 20px;
                line-height: 20px;
                font-size: 26rpx;
                color: #666;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .sticky-header {
        position: sticky;
        top: -1rpx;
        background: #f3f3f3;
    }

    .menu {
        width: 750rpx;
        background: #f3f3f3;
    }

    .selc-nav {
        width: 750rpx;
        padding: 22rpx 30rpx 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f3f3f3;	
        .selc-item {
            font-size: 34rpx;
            color: #333;
            width: 335rpx;
            height: 100rpx;
            margin: 0 20rpx;
            border-radius: 10rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
            z-index: 1;
            position: relative;
            box-sizing: border-box;

            &.on {
                background: #FB243C;
                color: #fff;
                font-weight: bold;
                border: none;
            }
        }
    }

    .order-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 0;

        .card {
            margin: 20rpx 0;
            width: 690rpx;
            background: #fff;
            border-radius: 16rpx;
            padding: 0;
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            
            &:hover {
                transform: translateY(-2rpx);
                box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
            }

            .card-header {
                padding: 24rpx 30rpx;
                border-bottom: 2rpx solid #f5f5f5;
                
                .qddt {
                    font-size: 32rpx;
                    color: #FB243C;
                    font-weight: 500;
                }
                
                .employer-info {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    
                    .name {
                        font-size: 32rpx;
                        color: #333;
                        font-weight: 500;
                    }
                    
                    .location {
                        font-size: 26rpx;
                        color: #666;
                        display: flex;
                        align-items: center;
                        
                        &::before {
                            content: '';
                            width: 24rpx;
                            height: 24rpx;
                            margin-right: 8rpx;
                            background: url('/static/img/location.png') no-repeat center center/100% auto;
                        }
                    }
                }
            }

            .card-content {
                padding: 24rpx 30rpx;
                
                .info-row {
                    display: flex;
                    align-items: center;
                    margin-bottom: 16rpx;
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                    
                    .label {
                        font-size: 28rpx;
                        color: #666;
                        min-width: 140rpx;
                    }
                    
                    .value {
                        flex: 1;
                        font-size: 28rpx;
                        color: #333;
                        font-weight: 500;
                        
                        &.highlight {
                            color: #FB243C;
                        }
                    }
                }
            }

            .card-footer {
                padding: 24rpx 30rpx;
                // border-top: 2rpx solid #f5f5f5;
                // background: #fafafa;
                display: flex;
                align-items: center;
                justify-content: space-between;


                .left-area {
                    .distance {
                        font-size: 24rpx;
                        color: #999;
                        display: flex;
                        align-items: center;
                        
                        &::before {
                            content: '';
                            width: 24rpx;
                            height: 24rpx;
                            margin-right: 8rpx;
                            background: url('/static/img/distance.png') no-repeat center center/100% auto;
                        }
                    }
                }

                .right-area {
                    display: flex;
                    gap: 20rpx;
                    
                    .button {
                        min-width: 140rpx;
                        height: 64rpx;
                        line-height: 64rpx;
                        border-radius: 32rpx;
                        font-size: 26rpx;
                        font-weight: 500;
                        padding: 0 30rpx;
                        
                        &.button1 {
                            background: linear-gradient(135deg, #FF4B4B, #FB243C);
                            color: #fff;
                            box-shadow: 0 4rpx 12rpx rgba(251, 36, 60, 0.2);
                        }
                        
                        &.button2 {
                            background: #686767;
                            color: #fff;
                        }
                        
                        &.button3 {
                            background: linear-gradient(135deg, #5BA4FF, #4395FF);
                            color: #fff;
                            box-shadow: 0 4rpx 12rpx rgba(67, 149, 255, 0.2);
                        }
                        
                        &:active {
                            transform: scale(0.98);
                            opacity: 0.9;
                        }
                    }
                }
            }
        }
        
        .nomore {
            color: #999;
            font-size: 24rpx;
            text-align: center;
            padding: 30rpx 0;
        }
    }

    .nav-bottom {
        position: fixed;
        bottom: 0;
        left: 0;
        display: flex;
        width: 100%;
        height: 160rpx;
        padding: 0 30rpx;
        justify-content: space-around;
        align-items: center;
        background-color: #f3f3f3;
        box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227, 227, 227, 0.87);
        z-index: 99;

        .selc-item2 {
            background: #FB243C;
            border-radius: 40rpx;
            color: #fff;
            width: 335rpx;
            height: 80rpx;
            margin: 0 6rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 32rpx;
            z-index: 1;
            position: relative;
            box-sizing: border-box;
            .text1 {
                font-size: 28rpx;
                color: #fff;
            }
        }
    }

    .button {
        min-width: 160rpx;
        height: 68rpx;
        border-radius: 34rpx;
        font-size: 28rpx;
        line-height: 68rpx;
        text-align: center;
        transition: all 0.3s ease;
        padding: 0 30rpx;

        &.button1 {
            background: linear-gradient(135deg, #FF4B4B, #FB243C);
            color: #fff;
            box-shadow: 0 4rpx 12rpx rgba(251, 36, 60, 0.2);
            
            &:active {
                transform: scale(0.98);
                box-shadow: 0 2rpx 8rpx rgba(251, 36, 60, 0.15);
            }
        }

        &.button2 {
            background: #686767;
            color: #fff;
            
            &:active {
                transform: scale(0.98);
                opacity: 0.9;
            }
        }

        &.button3 {
            background: linear-gradient(135deg, #5BA4FF, #4395FF);
            color: #fff;
            box-shadow: 0 4rpx 12rpx rgba(67, 149, 255, 0.2);
            
            &:active {
                transform: scale(0.98);
                box-shadow: 0 2rpx 8rpx rgba(67, 149, 255, 0.15);
            }
        }
    }
}

.filter-dropdown {
    padding:0;
    
    .dropdown-item {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        color: #333;
        border-bottom: 1rpx solid #f5f5f5;
        
        &:active {
            background-color: #f8f8f8;
        }
        
        &.active {
            color: #FB243C;
        }
        
        &:last-child {
            border-bottom: none;
        }
    }
}
</style>