	.main{
		background-color: #F7F6F6;
		width: 750rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		overflow-y: hidden;
		.waves {
		    position: relative;
		    width: 750rpx;
		    height:368rpx;
			padding-top: 250rpx;
			background: linear-gradient(to left, rgba(255, 101, 77, 1) 0%, rgba(255, 65, 93, 1) 100%) no-repeat center top/100% 99%;
			flex-shrink: 0;
		}
		.logo-area{
			margin-top: -160rpx;
			position: relative;
			z-index: 2;
			width: 225rpx;
			height: 225rpx;
			border: 8rpx solid #FFFFFF;
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
			.logo-bg{
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background: linear-gradient(90deg, #FF664C, #FF405E);
				animation: bgr 5s linear infinite;
			}
			.logo{
				width: 157rpx;
				position: absolute;
				
			}
		}
		.nav{
			margin-top: 60rpx;
			display: flex;
			align-items: center;
			position: relative;
			.nav-item{
				width: 200rpx;
				font-size: 36rpx;
				font-weight: 500;
				text-align: center;
				transition: .3s;
			}
			.wa{
				width: 100%;
			}
			.line{
				width: 150rpx;
				height: 4rpx;
				background-color: #FF5156;
				position: absolute;
				bottom: -6rpx;
				left: 25rpx;
				transition: .3s;
			}
			.lineright{
				left: 220rpx;
			}
			.navpick{
				color: #FF5156;
			}
		}
		.content{
			width: 100%;
			flex: 1;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
		}
		.form{
			margin-top: 30rpx;
			input{
				width: 608rpx;
				height: 86rpx;
				background: #FFFFFF;
				border: 1px solid #909399;
				border-radius: 43rpx;
				text-align: center;
				margin-bottom: 39rpx;
			}
			.yzm_area{
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 39rpx;
				input{
					width: 396rpx;
					margin-bottom: 0;
				}
				.yzm_button{
					width: 189rpx;
					height: 86rpx;
					background: linear-gradient(90deg, #FF664C, #FF405E);
					border-radius: 43rpx;
					text-align: center;
					line-height: 86rpx;
					font-size: 36rpx;
					color: #F7F6F6;
				}
				.yhq{
					background: #909399;
				}
			}
		}
		.other{
			display: flex;
			align-items: center;
			justify-content: center;
			&>view{
				font-size: 27rpx;
				color: #909399;
			}
			.left{}
			.right{
				margin-left: 62rpx;
				text{
					position: relative;
					&::after{
						content: '';
						width: 90%;
						height: 1px;
						background-color: #909399;
						position: absolute;
						bottom: -2rpx;
						left: 10%;
					}
				}
			}
		}
		.button{
			width: 608rpx;
			height: 86rpx;
			background: linear-gradient(90deg, #FF664C, #FF405E);
			border-radius: 43rpx;
			text-align: center;
			line-height: 86rpx;
			font-size: 36rpx;
			color: #F7F6F6;
		}
		.tplogin{
			display: flex;
			flex-direction: column;
			align-items: center;
			.tptit{
				
				display: flex;
				align-items: center;
				.line{
					width: 122rpx;
					height: 2rpx;
					background: #909399;
				}
					
				.text{
					padding: 0 10rpx;
					font-size: 23rpx;
					color: #606266;
				}
			}
				
			.icon-area{
				margin-top: 26rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 66rpx;
				height: 66rpx;
				background: #26B446;
				border-radius: 50%;
				padding-top: 6rpx;
			}
		}
	}
	

	@keyframes bgr {
		0%{
			transform: rotate(0deg);
		}
		100%{
			transform: rotate(360deg);
		}
	}

