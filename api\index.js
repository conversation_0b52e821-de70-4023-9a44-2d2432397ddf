import wxRequest from './wxRequest.js'
import { useStore } from '@/until/mainpinia';
import app_upgrade from '@/uni_modules/app-upgrade/js_sdk/index.js'
let baseUrl = 'https://wx.wansao.com/family';
let request = new wxRequest({
	baseUrl,
	// #ifdef MP-TOUTIAO
		loginUrl: '/login/dylogin',
	// #endif
	// #ifdef MP-WEIXIN
		loginUrl: '/login/login',
	// #endif
	// 返回数据
	resolveData: function(data) {
		return data
	},
	// 拿到token存起来
	getToken: function(data) {
		// const store = useStore();
		// store.$patch((state) => {
		// 	state.userInfo = data.data
		// })
		uni.setStorageSync('token',data.data.token)
		return data.data.token
	},
	// 正常返回数据的情况
	reqSuccess: function(data) {
		if (data && ![undefined,401, 402].includes(data.code)) {
			return true
		}
		return false
	},
	// token过期需重新获取的情况
	reacquire: function(data) {
		if (data && [401, 402].includes(data.code)) {
			return true
		}
		return false
	}
})
const getUserInfo = ()=>{
	return request.ajax({
		url: '/Center/basicInfo',
		type: 'POST',
	}).then(res => {
		if(res.code===1){
			const store = useStore();
			store.$patch((state) => {
				state.userInfo = res.data
			})
		}else{
			// uni.showToast({ title:res.msg, icon:'none' })
		}
	})
}
const getUserAccountInfo = () => {
    return request.ajax({
        url: '/home/<USER>',
        type: 'POST',
    }).then(res => {
		if (res.code === 1) {
			const store = useStore();
			store.$patch((state) => {
				state.userStatus = res.data.status
				state.userStatusDesc = res.data.desc
				state.mainMenuSwitch = res.data.mainMenuSwitch
			})
			return {
				status: res.data.status,
				desc: res.data.desc,
				mainMenuSwitch:res.data.mainMenuSwitch
			}
			// 根据不同状态进行处理
			switch (res.data.status) {
				case -9:
					uni.showToast({ title: '账号已注销', icon: 'none' })
					break
				case -8:
					uni.showToast({ title: '申请已被拒绝', icon: 'none' })
					break
				case -2:
					uni.showToast({ title: '账号已被系统封禁', icon: 'none' })
					break
				case -1:
					uni.showToast({ title: '账号访问受限', icon: 'none' })
					break
				case 0:
					break
				case 1:
					uni.showToast({ title: '您的申请正在审核中', icon: 'none' })
					break
				case 9:
					uni.showToast({ title: '您已是入驻成员', icon: 'none' })
					break
			}
		}else{
			uni.showToast({ title:res.msg, icon:'none' })
		}
	})
}
const getIndex = ()=>{
	api.request.ajax({
		url: '/home/<USER>',
		type: 'POST',
		// whiteList: true,// 表示不需要token
	}).then(res => {
		if(res.code===1){
			const store = useStore();
			store.$patch((state) => {
				state.setting = res.data
			})
		}else{
			uni.showToast({ title:res.msg, icon:'none' })
		}
	})
}
const appUpData = async ()=>{
	const res =await api.request.ajax({
		url: '/config/appversion',
		type: 'POST',
		whiteList: true,
	})
	
	let showCancel = true
	let status = 0
	let changelog = ''
	if (uni.getSystemInfoSync().platform == "ios") {
		showCancel = !res.data.version.apple_force
		status = res.data.version.apple>plus.runtime.version?1:0
		changelog = res.data.version.apple_content
	}
	if (uni.getSystemInfoSync().platform == "android") {
		showCancel = !res.data.version.android_force
		status = res.data.version.android>plus.runtime.version?1:0
		changelog = res.data.version.android_content
	}
	app_upgrade(async(versionCode)=>{
		if(res.code === 1){
			return {
				changelog,
				status, // 0 版本 | 1 有新版本
				path:res.data.download.android, // 新apk地址
				showCancel //是否强制更新(0是1否)
			}
		}
	},Number(!showCancel))
}
const miniProgramUpdata = ()=>{
	const updateManager = uni.getUpdateManager();
	
	updateManager.onCheckForUpdate(function (res) {
	  // 请求完新版本信息的回调
	  // console.log(res.hasUpdate);
	});
	
	updateManager.onUpdateReady(function (res) {
	  uni.showModal({
		title: '更新提示',
		content: '新版本已经准备好，是否重启应用？',
		success(res) {
		  if (res.confirm) {
			// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
			updateManager.applyUpdate();
		  }
		}
	  });
	
	});
	
	updateManager.onUpdateFailed(function (res) {
	  // 新的版本下载失败
	});
}
const getDistrict = async () => {
	try {
		const res = await new Promise((resolve, reject) => {
			uni.request({
				url: 'https://wx.wansao.com/data/district.json',
				method: 'GET',
				success: (res) => {
					resolve(res)
				},
				fail: (err) => {
					reject(err)
				}
			})
		})
		
		if (res?.data?.list) {  // 检查list是否存在
			// 处理数据结构
			const list = []
			const data = res.data.list
			
			// 处理省级数据
			Object.values(data).forEach(province => {
				if(province.level === '1') {
					const provinceItem = {
						id: province.id,
						text: province.name,
						children: []
					}
					
					// 处理市级数据
					Object.values(data).forEach(city => {
						if(city.level === '2' && city.upid === province.id) {
							const cityItem = {
								id: city.id, 
								text: city.name,
								children: []
							}
							
							// 处理区县数据
							Object.values(data).forEach(area => {
								if(area.level === '3' && area.upid === city.id) {
									cityItem.children.push({
										id: area.id,
										text: area.name
									})
								}
							})
							
							provinceItem.children.push(cityItem)
						}
					})
					
					list.push(provinceItem)
				}
			})

			const store = useStore()
			store.$patch((state) => {
				state.district = list
			})
			return list
		}
	} catch (error) {
		console.error('获取地区数据失败:', error)
		uni.showToast({ 
			title: '获取地区数据失败', 
			icon: 'none' 
		})
	}
}

export {
	request,
	baseUrl,
	getUserInfo,
	getUserAccountInfo,
	getIndex,
	appUpData,
	miniProgramUpdata,
	getDistrict
}