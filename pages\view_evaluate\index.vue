<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'查看评价'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
			<view class="relative flex-center flex-col">
				<text class="title-text">客户评价是对您辛勤劳动最好的赞美</text>
				<text class="count-text">已有{{count}}个评价</text>
			</view>
			<view class="business_card">
				<image src="/static/img/cardbg.png" class="card-bg" mode="widthFix"></image>
				<view class="card-content">
					<view class="avatar-wrapper">
						<view class="avatar-inner">
							<image :src="userInfo.photo"></image>
						</view>
					</view>
					<view class="tag-list">
						<view class="tag" v-for="item in userInfo.certPic" :key="item.tid">
							<image class="tag-icon" :src="item.pic"></image>
							<text class="tag-text">{{ item.name }}</text>
						</view>
					</view>
					<view class="user-info">
						<view class="name-age">
							<text class="name">{{ userInfo.name }}</text>
							<text class="age" v-if="userInfo.age">{{ userInfo.age }}</text>
						</view>
						<view class="experience-area" v-if="userInfo.workYear || userInfo.area">
							<text class="experience" v-if="userInfo.workYear">{{ userInfo.workYear }}年经验</text>
							<text class="area-text" v-if="userInfo.area">{{ userInfo.area }}</text>
						</view>
						<view v-if="userInfo?.jobName?.[0]">
							<view class="job-item" v-for="item in userInfo.jobName" :key="item">
								<text class="job-name">{{ item }}</text>
							</view>
						</view>
					</view>
					<image class="qr-code" :src="userInfo?.evaluateUrl"></image>
					<view class="scan-text">请扫码为我评价</view>
				</view>
			</view>
			<view class="comment_list">
				<view class="comment_card" v-for="item in evaluation" :key="item.id">
					<view class="flex-row-center-start">
						<view class="rounded overflow flex-center">
							<tm-image  :width="110" :height="110" :src="item.avatarUrl"></tm-image>
						</view>
						<view class="flex-col ml-21">
							<tm-text class="text-align-center" :font-size="30" color="#606060" :label="item.nickName"></tm-text>
							<view class="flex-row-center-start mt-20">
								<view class="flex w-150">
									<tm-icon :font-size="30" color="red" name="tmicon-collection-fill" v-for="(item2,index2) in item.star" :key="index2"></tm-icon>
								</view>
								<view class="flex-center ml-n8">
									<tm-icon :font-size="20" color="#858585" name="tmicon-calendar-alt"></tm-icon>
									<tm-text class="text-align-center nowrap ml-10" :font-size="20" color="#858585" :label="'评论日期：'+item.date"></tm-text>
								</view>
							</view>
						</view>
					</view>
					<view class="line"></view>
					<tm-text :font-size="26" :lineHeight="39" color="#606060" :label="item.content"></tm-text>
					<tm-image-group>
						<view class="flex-row-center-between mt-n10 fulled">
							<tm-image
							:width="140" 
							:height="140" 
							:round="7" 
							preview
							:src="item2"
							:key="item2"
							model="aspectFill" 
							v-for="item2 in item.pics"></tm-image>
						</view>
					</tm-image-group>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import {snb} from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const {NavigationBarTitle} = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})
const userInfo = computed(() => store.userInfo)
const evaluation = ref([])
const count = ref(0)
const getData = async () => {
	const res = await api.request.ajax({
		url: '/Center/getEvaluation',
		type: 'POST',
		data: {
			page: 1
		}
	})
	if (res.code === 1) {
		evaluation.value = res.data.list
		count.value = res.data.count
		setShareApp(res.data.shareData)
		setShareTime(res.data.shareTimeline)
	}
}
onLoad(()=>{
	getData()
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.title-text {
		font-size: 30rpx;
		color: #fff;
		text-align: center;
	}
	
	.count-text {
		font-size: 22rpx;
		color: #fff;
		text-align: center;
		margin-top: 10rpx;
	}
	
	.comment_list{
		.comment_card{
			margin-top: 55rpx;
			width: 690rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
			border-radius: 26rpx;
			padding: 52rpx;
			.line{
				margin-top: 31rpx;
				margin-bottom: 31rpx;
				width: 581rpx;
				height: 1rpx;
				background: #F3F3F3;
			}
		}
	}
}
</style>