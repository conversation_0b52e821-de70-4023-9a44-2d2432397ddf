<template>
  <view>
    <view class="address-window" :class="{ on: modelValue }">
      <view class="title">
        <image class="title-logo" :src="titleLogo" mode=""></image>
        <view class="title-txt">
          小程序 申请
        </view>
      </view>
      <view class="notes">
        <view class="notes-top">
          完善你的昵称、头像
        </view>
        <view class="notes-bot">
          主要用于向用户提供具有辨识度的用户中心界面
        </view>
      </view>
      <view class="userbox">
        <button class="mydata-arrow-right" @click="chooseAvatar">
          <view class="userbox-item">
            <image class="userbox-item-img" :src="avatarUrl ? avatarUrl : headimg" mode=""></image>
            <view class="userbox-item-txt">
              点击选择头像
            </view>
          </view>
        </button>
        <view class="userbox-item">
          <view class="userbox-item-nicheng">
            昵称
          </view>
          <input class="userbox-item-input" type="text" v-model="nickName" @input="getNickname" placeholder="请填写昵称" />
        </view>
      </view>
      <view class="task-btn">
        <view class="left" @click="closeStudent">
          取消
        </view>
        <view class="left right" @click="authorize">
          授权
        </view>
      </view>
    </view>
    <view class="mask" @touchmove.prevent="preventTouchMove" v-if="modelValue" @click="closeStudent"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mydate: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'send'])

const headimg = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAoHBwkHBgoJCAkLCwoMDxkQDw4ODx4WFxIZJCAmJSMgIyIoLTkwKCo2KyIjMkQyNjs9QEBAJjBGS0U+Sjk/QD3/wgALCACEAIQBAREA/8QAGgABAAMBAQEAAAAAAAAAAAAAAAMEBQIBB//aAAgBAQAAAAD60AAAAAAAAAAAAjyo718ADJrGzMADFiNW0ADNpPdqQAHmJxoXwAK+T5a0+gBHnVR3oXADC5A0roCgAszAAAAAAAAAAAA//8QANhAAAgEBAwkFBQkAAAAAAAAAAQIDBAARMAUSFSFRY4Gi4RAgImFxExQxQcFAQlBSYHKRodH/2gAIAQEAAT8AsB3AMAD7QB+CzzrBHntwG02lqpZjeWIGwahYOyG9WIPkbUlcSwSY+Qb/AHGr5S9SR+Twj69yjlMtMpPoeOLUgipk/ce5k0EU582OLlGAiQSj72o+naAXYKBeTqFoIxDCqbBiugkQowvU2kT2cjJsJH8dmTYRcZeC41RWJCCAc59g+tiS7FjrJN57KOr93vVh4DZHSRc5CGGHLOkC3ufQfM2nrpJrwvhXZ8zw7scrxNnRkqbU+UVfVL4ThO7SOXY4GTXYo6E3gXXcb8E5LBYkS/11tove8vW2i97y9baL3vL1tove8vW2i97y9LaL3vL1tove8vW1LTCmVgGzififT9E//9k='
const titleLogo = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAMAAACahl6sAAAAjVBMVEUAAACKioqKioqLi4uLi4uLi4uKioqKioqKioqJiYmKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqMjIyKioqJiYmKioqKioqKioqKioqKioqKioqKioqKioqKioqJiYmKioqKioqKioqKioqKioqKioqKioqKioqKioqLi4uKioqKioqKiorDnFfpAAAALnRSTlMAv6gzGwrhtVNHOiL3mm6g8c+vaE4Pj4AVXtR9WebGuYpBJ+yFYy93lMraK3O9YRgd1gAACZZJREFUeNrsm+mSojAUhS/77oYoIthuuE/e//Hmx9RUwNh6gBC7q/x+U9AnucvJjU0fPnz48OHDhw8ffi9pYGyMILXodxJ4g5NzvhQh+0+4u4y16OgefoukbXlyLuwZhRYNDvSjMQa6zTBGk5v/M/fGhEVwMc7yx+2MOxyxVjhlSj8GY5+x9uwij34E8R/WlfHcpDdjDcZMBsU+oDeS3DImi9FsS28iXdrP/7Tz10QfRrP1fD+LhvrE0Qr2jPD0nhp2LL7PXy0aeOYj7X45c57IP6nPFff8XeJGi+BVRHp7jT3Gjkkphs4e8bV3UzS9vPnX48ayIWUk+5CJXPYGNeMwf1gr1impYZExkUluUQumjxzBZUEqmDEBe37t4NHOTGBGvWNoYnrfko5bLL5TM6hfyp3wyZK6kwuJvyupR6yTUGNcksPUEXqKRX1xXd1nZSmzhty7tlVf4XXN+q2T1vIubm2P+uBq3xVcn2SzjViNMCf5XMO67+4nGb276C3l67gzI30Z1WTIagxILsZddlB/HFmNI8nEr4dVvxZiWk/Gm8z9UBFWnG29p3jyXsyqRNQ/9UQxSA5mrbzPCUCyM92SDJJaH4xJDfOagzCpO1bNmrqkCreWlhZ1ZiLknSICVkGnrixZhSmpxGIVll1rulDRFWLKW0QzY5wTqcZnnKxTwusK+gcaELok2zOkdxBLcV0e40zoPSwl1ExrXGlJAb2Jiu8aJ92bq0vvwth1PT0YIWCwFBB3DS79/QkiWGGNmrPoNUEC/5C2aWbLlp5XfoIk8WlyLv7NSOzV2kuadZPQp4ase+kg27kt3KkAN7oRf9xpf7gNDySJTRSyBxQvpQRF6wFRZci7l5UW/EAgSnkRYQP+7LntNC5LJNmmgj1h7NFTKtP6RcsjcynRaoig9WjTrgQHI56LJAOzYC/5QotP3qpkTeWcWhHW4FLga2vuJG/IiEHE9IR9i742h7YRR2N1dqvhcbpYO8I1Xo6Fu0MY1oUnlvxh22jIF9QYrOoKr9hrfILIwVKHmzaOPTCphuugU59r2LS16UjzwakuepQ+DH5ODjXpMSEEXHkpeWKYv9yyLIFs04YAbjwOLLlH1SWgNYZetW4WCjMZRhExri6WJSXfuGZDMV/q8S4zkad29D3pqMmZdya1GSZY0Gx30OoNmwRLxr8s9TL4DC7fHCocWYNrtiKl7njgshhQklg2fh2Xy51Zl+j1mQb1iBM+P501t2aY1bPALhwCTQnxW3xMGhAA7BIurxYQ+m7KLRs9JxXSSZLx1eAjkIEZaQ8dIUVqhfDJRIKF6hrd4YVSITykbcAoAIb2zKuMWiEjMItDsHqE3CooFbLgIYN60AM2JXDUCuFJXKK3gS6W62vZQtAxtYFWoyM2RiuVCvkD390GWFnVuQlVKWSGz9F4FmtYX08UChlyHSuCrUdBT2D8KXVCdOi3mOLTFnJ6+KNMyHTFOHbSYBp+QPrmSZEQn4cVaCdcxG15vLYpEJJu7v9J4dhopuAiauPehfgrdo/W7FKwRI6Hed9Cbkyk4RTghjger2chBhPYJYQQIGOKuEM/TPw438JCckGHHRCEiYyEBm1NvL/+16cmqJCjcBtqEEaKeBT+epNw0pgXnwwUUrI6e6v5TzZ1pNlYBBPbrMIcE7JhVRyfcPgkVKoQ/297Z9ujKBDD8YqAiIAIKKgsgg94Pux8/493ueSSOYK6/9E6mAu/17ibQmfaaTttJBpMEUGa0V4jJ2IWRF21Uq9lCwBBZB3E2cjnBIKrlvpiT4UE+yKSJL5WJgEAix3efvHbrgdAEKaq5pLNIG5+Ll/gF2QrE2Cwi6L+PYzs7YLUyNklkQ/B/rTcfOI6I2IQhMFpHCvUDpp2Q6Xk5vNmQSrEja8Uyv/8RhkA4DQysURy1BM8CLD1pByDCekTpITyDyGafqB9wwZqFCSCji8FatozW+oVaRXkDKWOfKmAaHmEN9QrCBadDtD9d4Y9yC9IjQR68HiQNK+hqVcQB3uDplCNnvukV5AU9KJ2MkuMLZGxZkEKufuCCZchtimYzILgtS3olwugVOOO9AriSKcI9WQMKElx0isIXsNhYXZuh1YXnqH3gjNrqD62SGoo24Kar5L5VGXjPtQBSRJ7aKA2IA4O+D3RBHrUB7ffA/PFawNP1mZQg4IUPLgUiKLimKFCmKdATolbrATXEbzJ1VghaUpHqN75BPkyI0hPcUbtlwy97AS6oJMiDznEwEWtw0iE6P8QaDCSCMVidlxZBmqne2+CJcfHwFV0Dgq1vzf/QjTxshOS5f0P234jHNfrL4r+jJ099OQlU/dhh9A9c9hhQxAVphLfzWBpYslvGhRv6DZU4YUe7b31bNFd3FA0CDfr2Kny6yKygfS5OoZ6L6EYkz0XGBZx4D7zhVfYvhkIBJNYmD5zHSQA7wauxY94F+4gvJgSjHUG7xy74ge+idhXSEU4R7RvxWQgHrHnjcGru21zG72Xb/mhuMcsISay1bOdONa4Sk7Ss7hFuLaIi/LpjiCmrVD4acWjlhgrzjbw1QvtqQI1g2bGkfzBV5S6xMnmFS9hoNwgzBwmcRrk4wkxcwWKobDWVmPqEtcDzBpmSmcZdchAvHb14BICO5cGSiFxX26ccaWucF63r9bsA5aJef5Hwy2G7Xu1pU4wBOpkoX76yaIOWDA1Y/aFpCD9XLkOzFbB4JLz9L0oLLZWdiIibnCbvBtyNgMpSScVciEDZ9+VJDH3Cc0XnTRm9fnnkURCraXp5/7TRtww0uFAZo0w5okYa6QkRU3vpm5Ejj2L2AiBRv585F/cET6Jxz1aAQ/7ucSKrWsqQWa05OBlpmdORDVrRVy5GWmYL2cdPeDiFdfRQFYtc5OfRINTRhJ2SSSbMXHiRqLJaE5v4tcb5xea8gylwR2Kw9aszy1xMNl/iSZhTACMo968X0N6lW250zDsDbj+UtEr1L+8Lsbv0fLr1rzip9dlMhUSaaV0UBuijb1w2WYkGzVpwilu5gvjjFTI4pG4wSonRfhn0YaDMjfh2aehuMV+TlqZ+OIOK395efzTpCzEHSKXtDOOxH3O0X45tFrbrLM2Zp64y+lAnTA2xGNCezWIpot0MY0GMzsUjymW1BmVIbj4zqlT3KngYORQ5/y1Bf/DzHxypuELH+OQ0edgBt9PDv2v6dMYXg1bqBCO1p+iUi3qABQmjAKXPpyJc/QHD6bpn4x1/nn6dBerdo6lb4yKlR3+MYzFxvAXae7Oqaenp6enp6enp6crfgOvbu3FrxqwkAAAAABJRU5ErkJggg=='
const avatarUrl = ref('')
const nickName = ref('')
const openid = ref('')

const authorize = async () => {
  if (!avatarUrl.value) {
    uni.showToast({
      title: '请选择头像',
      icon: "none",
      duration: 1500
    })
    return
  }
  let name = nickName.value.trim()
  if (!name) {
    uni.showToast({
      title: '请填写昵称',
      icon: "none",
      duration: 1500
    })
    return
  }
  
  try {
    const imageBase64 = await getImageBase64(avatarUrl.value)
    let obj = {
      imageBase64,
      nickName: name,
      avatarUrl: avatarUrl.value,
    }
    emit('send', obj)
    emit('update:modelValue', false)
  } catch (error) {
    uni.showToast({
      title: '图片处理失败',
      icon: 'none'
    })
  }
}

const getNickname = (e) => {
  nickName.value = e.detail.value
}

const uploadImg = (url) => {
  avatarUrl.value = url
}

const chooseAvatar = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera']
    })
    if (res.tempFilePaths && res.tempFilePaths[0]) {
      uploadImg(res.tempFilePaths[0])
    }
  } catch (error) {
    uni.showToast({
      title: '选择头像失败',
      icon: 'none'
    })
  }
}

const closeStudent = () => {
  emit('update:modelValue', false)
}

const preventTouchMove = () => {} //阻止滑动

const getImageBase64 = (filePath) => {
  return new Promise((resolve, reject) => {
    uni.getFileSystemManager().readFile({
      filePath,
      encoding: 'base64',
      success: res => {
        resolve(res.data)
      },
      fail: err => {
        reject(err)
      }
    })
  })
}
</script>

<style>
.address-window {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 6666;
  padding: 32rpx 40rpx 200rpx;
  transform: translate3d(0, 100%, 0);
  transition: all .3s cubic-bezier(.25, .5, .5, .9);
  border-radius: 24rpx 24rpx 0 0;
  box-sizing: border-box;
}

.on {
  transform: translate3d(0, 0, 0);
  box-shadow: 0px 0px 30rpx 30rpx rgba(107, 127, 153, 0.30);
}

.title {
  width: 100%;
  padding: 36rpx 10rpx 40rpx;
  box-sizing: border-box;
  overflow: auto;
}

.title-logo {
  float: left;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.title-txt {
  float: left;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  color: #000;
}

.notes {
  width: 100%;
}

.notes-top {
  width: 100%;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 600;
  text-align: left;
  color: #000;
}

.notes-bot {
  width: 100%;
  font-size: 26rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 400;
  text-align: left;
  color: #666;
  margin-top: 10rpx;
}

.userbox {
  width: 100%;
  border-top: 2rpx solid #ededed;
  margin-top: 30rpx;
}

.mydata-arrow-right {
  border-color: #fff;
  padding: 0rpx !important;
  margin: 0rpx !important;
  border: none !important;
  border-radius: 0rpx !important;
  box-sizing: border-box !important;
  background-color: transparent !important;
}

.mydata-arrow-right::after {
  display: none;
}

.userbox-item {
  width: 100%;
  height: 112rpx;
  border-bottom: 2rpx solid #ededed;
  border-left: 2rpx solid #ededed;
  border-right: 2rpx solid #ededed;
  overflow: auto;
  overflow-y: hidden;
  padding: 0 20rpx;
}

.userbox-item-img {
  float: left;
  width: 70rpx;
  height: 70rpx;
  border-radius: 8rpx;
  margin-top: 20rpx;
  margin-right: 20rpx;
}

.userbox-item-txt {
  float: left;
  font-size: 26rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 400;
  line-height: 112rpx;
  text-align: left;
  color: #666;
}

.userbox-item-nicheng {
  float: left;
  font-size: 26rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 400;
  line-height: 112rpx;
  text-align: left;
  color: #000;
}

.userbox-item-input {
  float: left;
  font-size: 26rpx;
  font-family: PingFang SC, PingFang SC-Semibold;
  font-weight: 400;
  line-height: 112rpx;
  text-align: left;
  color: #000;
  margin-top: 34rpx;
  margin-left: 36rpx;
}

.task-btn {
  width: 80%;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin: 44rpx auto 0;
}

.left {
  float: left;
  width: 210rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  background: #F2F2F2;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: center;
  color: #5DBC6F;
}

.left.right {
  float: right;
  background: #5DBC6F;
  color: #ffffff;
}

.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 6665;
}
</style> 