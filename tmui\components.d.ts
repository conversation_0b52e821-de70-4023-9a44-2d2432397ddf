// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/vue-next/pull/3399

declare module 'vue' {
  export interface GlobalComponents {

    TmActionMenu: typeof import('./components/tm-action-menu/tm-action-menu.vue')['default']
    TmAlert: typeof import('./components/tm-alert/tm-alert.vue')['default']
    TmApp: typeof import('./components/tm-app/tm-app.vue')['default']
    TmAvatar: typeof import('./components/tm-avatar/tm-avatar.vue')['default']
    TmBadge: typeof import('./components/tm-badge/tm-badge.vue')['default']
    TmBarcode: typeof import('./components/tm-barcode/tm-barcode.vue')['default']
    TmButton: typeof import('./components/tm-button/tm-button.vue')['default']
    TmCalendar: typeof import('./components/tm-calendar/tm-calendar.vue')['default']
    TmCalendarView: typeof import('./components/tm-calendar-view/tm-calendar-view.vue')['default']
    TmCard: typeof import('./components/tm-card/tm-card.vue')['default']
    TmCarousel: typeof import('./components/tm-carousel/tm-carousel.vue')['default']
    TmCascader: typeof import('./components/tm-cascader/tm-cascader.vue')['default']
    TmCell: typeof import('./components/tm-cell/tm-cell.vue')['default']
    TmChart: typeof import('./components/tm-chart/tm-chart.vue')['default']
    TmCheckbox: typeof import('./components/tm-checkbox/tm-checkbox.vue')['default']
    TmCheckboxGroup: typeof import('./components/tm-checkbox-group/tm-checkbox-group.vue')['default']
    TmCityCascader: typeof import('./components/tm-city-cascader/tm-city-cascader.vue')['default']
    TmCityPicker: typeof import('./components/tm-city-picker/tm-city-picker.vue')['default']
    TmCodeinput: typeof import('./components/tm-codeinput/tm-codeinput.vue')['default']
    TmCol: typeof import('./components/tm-col/tm-col.vue')['default']
    TmCollapse: typeof import('./components/tm-collapse/tm-collapse.vue')['default']
    TmCollapseItem: typeof import('./components/tm-collapse-item/tm-collapse-item.vue')['default']
    TmComment: typeof import('./components/tm-comment/tm-comment.vue')['default']
    TmCountdown: typeof import('./components/tm-countdown/tm-countdown.vue')['default']
    TmCoupon: typeof import('./components/tm-coupon/tm-coupon.vue')['default']
    TmCropimg: typeof import('./components/tm-cropimg/tm-cropimg.vue')['default']
    TmDataTable: typeof import('./components/tm-data-table/tm-data-table.vue')['default']
    TmDescriptions: typeof import('./components/tm-descriptions/tm-descriptions.vue')['default']
    TmDescriptionsItem: typeof import('./components/tm-descriptions-item/tm-descriptions-item.vue')['default']
    TmDivider: typeof import('./components/tm-divider/tm-divider.vue')['default']
    TmDragList: typeof import('./components/tm-drag-list/tm-drag-list.vue')['default']
    TmDrawer: typeof import('./components/tm-drawer/tm-drawer.vue')['default']
    TmDropdown: typeof import('./components/tm-dropdown/tm-dropdown.vue')['default']
    TmFloatButton: typeof import('./components/tm-float-button/tm-float-button.vue')['default']
    TmForm: typeof import('./components/tm-form/tm-form.vue')['default']
    TmFormItem: typeof import('./components/tm-form-item/tm-form-item.vue')['default']
    TmGrid: typeof import('./components/tm-grid/tm-grid.vue')['default']
    TmGridItem: typeof import('./components/tm-grid-item/tm-grid-item.vue')['default']
    TmIcon: typeof import('./components/tm-icon/tm-icon.vue')['default']
    TmImage: typeof import('./components/tm-image/tm-image.vue')['default']
    TmImageGroup: typeof import('./components/tm-image-group/tm-image-group.vue')['default']
    TmIndexes: typeof import('./components/tm-indexes/tm-indexes.vue')['default']
    TmIndexesItem: typeof import('./components/tm-indexes-item/tm-indexes-item.vue')['default']
    TmInput: typeof import('./components/tm-input/tm-input.vue')['default']
    TmKeyborad: typeof import('./components/tm-keyborad/tm-keyborad.vue')['default']
    TmMessage: typeof import('./components/tm-message/tm-message.vue')['default']
    TmModal: typeof import('./components/tm-modal/tm-modal.vue')['default']
    TmMore: typeof import('./components/tm-more/tm-more.vue')['default']
    TmNavbar: typeof import('./components/tm-navbar/tm-navbar.vue')['default']
    TmNotification: typeof import('./components/tm-notification/tm-notification.vue')['default']
    TmOverlay: typeof import('./components/tm-overlay/tm-overlay.vue')['default']
    TmPagination: typeof import('./components/tm-pagination/tm-pagination.vue')['default']
    TmPaper: typeof import('./components/tm-paper/tm-paper.vue')['default']
    TmPicker: typeof import('./components/tm-picker/tm-picker.vue')['default']
    TmPickerView: typeof import('./components/tm-picker-view/tm-picker-view.vue')['default']
    TmPopover: typeof import('./components/tm-popover/tm-popover.vue')['default']
    TmProgress: typeof import('./components/tm-progress/tm-progress.vue')['default']
    TmQrcode: typeof import('./components/tm-qrcode/tm-qrcode.vue')['default']
    TmRadio: typeof import('./components/tm-radio/tm-radio.vue')['default']
    TmRadioGroup: typeof import('./components/tm-radio-group/tm-radio-group.vue')['default']
    TmRate: typeof import('./components/tm-rate/tm-rate.vue')['default']
    TmRender: typeof import('./components/tm-render/tm-render.vue')['default']
    TmResult: typeof import('./components/tm-result/tm-result.vue')['default']
    TmRollNotice: typeof import('./components/tm-roll-notice/tm-roll-notice.vue')['default']
    TmRow: typeof import('./components/tm-row/tm-row.vue')['default']
    TmScrollx: typeof import('./components/tm-scrollx/tm-scrollx.vue')['default']
    TmScrolly: typeof import('./components/tm-scrolly/tm-scrolly.vue')['default']
    TmHtml: typeof import('./components/tm-html/tm-html.vue')['default']
    TmFilterMenu: typeof import('./components/tm-filterMenu/tm-filterMenu.vue')['default']
    TmFilterMenuItem: typeof import('./components/tm-filterMenu-item/tm-filterMenu-item.vue')['default']
    TmSegtab: typeof import('./components/tm-segtab/tm-segtab.vue')['default']
    TmSheet: typeof import('./components/tm-sheet/tm-sheet.vue')['default']
    TmSignBoard: typeof import('./components/tm-sign-board/tm-sign-board.vue')['default']
    TmSkeleton: typeof import('./components/tm-skeleton/tm-skeleton.vue')['default']
    TmSkeletonLine: typeof import('./components/tm-skeleton-line/tm-skeleton-line.vue')['default']
    TmSlideSwitch: typeof import('./components/tm-slide-switch/tm-slide-switch.vue')['default']
    TmSlider: typeof import('./components/tm-slider/tm-slider.vue')['default']
    TmSpin: typeof import('./components/tm-spin/tm-spin.vue')['default']
    TmStatistic: typeof import('./components/tm-statistic/tm-statistic.vue')['default']
    TmStepper: typeof import('./components/tm-stepper/tm-stepper.vue')['default']
    TmSteps: typeof import('./components/tm-steps/tm-steps.vue')['default']
    TmStepsItem: typeof import('./components/tm-steps-item/tm-steps-item.vue')['default']
    TmSticky: typeof import('./components/tm-sticky/tm-sticky.vue')['default']
    TmSwitch: typeof import('./components/tm-switch/tm-switch.vue')['default']
    TmTabbar: typeof import('./components/tm-tabbar/tm-tabbar.vue')['default']
    TmTabbarItem: typeof import('./components/tm-tabbar-item/tm-tabbar-item.vue')['default']
    TmTable: typeof import('./components/tm-table/tm-table.vue')['default']
    TmTabs: typeof import('./components/tm-tabs/tm-tabs.vue')['default']
    TmTabsPane: typeof import('./components/tm-tabs-pane/tm-tabs-pane.vue')['default']
    TmTag: typeof import('./components/tm-tag/tm-tag.vue')['default']
    TmText: typeof import('./components/tm-text/tm-text.vue')['default']
    TmTimePicker: typeof import('./components/tm-time-picker/tm-time-picker.vue')['default']
    TmTimeView: typeof import('./components/tm-time-view/tm-time-view.vue')['default']
    TmTimeline: typeof import('./components/tm-timeline/tm-timeline.vue')['default']
    TmTimelineItem: typeof import('./components/tm-timeline-item/tm-timeline-item.vue')['default']
    TmTranslate: typeof import('./components/tm-translate/tm-translate.vue')['default']
    TmTree: typeof import('./components/tm-tree/tm-tree.vue')['default']
    TmUpload: typeof import('./components/tm-upload/tm-upload.vue')['default']
    TmVirtualList: typeof import('./components/tm-virtual-list/tm-virtual-list.vue')['default']
    TmWaterfall: typeof import('./components/tm-waterfall/tm-waterfall.vue')['default']
    TmWaterfallItem: typeof import('./components/tm-waterfall-item/tm-waterfall-item.vue')['default']
    TmWatermark: typeof import('./components/tm-watermark/tm-watermark.vue')['default']
    TmWeekbar: typeof import('./components/tm-weekbar/tm-weekbar.vue')['default']

  }
}

export { }
