# 皖嫂一家亲客户端

基于uni-app开发的家政服务平台APP，为家政服务人员提供一站式服务解决方案。

## 项目简介

皖嫂一家亲是一个专注于家政服务行业的移动应用平台，为家政服务人员提供简历管理、接单、打卡、评价等全方位功能支持。

## 技术栈

- 框架：uni-app
- 前端：Vue 3 + TypeScript
- UI库：[TMUI 3.1.1](https://tmui.design/)
- 状态管理：Pinia
- 构建工具：Vite
- 图表库：ECharts 5.4.3

## 主要功能

- 📱 用户认证与登录
- 📝 简历管理系统
- 💼 接单系统
- ⏰ 上下户打卡
- ⭐ 用户评价
- 📰 新闻资讯
- 🎯 活动管理
- 💰 收益管理
- 🤝 推荐奖励

## 项目结构

```
├── api/            # API接口
├── components/     # 公共组件
├── pages/          # 页面文件
├── static/         # 静态资源
├── tmui/           # UI组件库
├── until/          # 工具函数
├── theme/          # 主题配置
└── router/         # 路由配置
```

## 开发环境搭建

1. 安装依赖
```bash
npm install
```

2. 运行项目
```bash
# HBuilder X中运行
```

## 打包发布

### Android打包配置

1. 证书信息
- 包名：com.wansao.family
- 证书别名：wsyjq
- 证书密码：wsadminapp
- 证书路径：./wsyjq.keystore

2. 生成证书命令
```bash
keytool -genkey -alias wsyjq -keyalg RSA -keysize 2048 -validity 36500 -keystore wsyjq.keystore
```

### 证书详细信息

- 名字和姓氏：huyuntao
- 组织单位：安徽省皖嫂家政服务有限责任公司
- 组织名称：安徽省皖嫂家政服务有限责任公司
- 城市：合肥市
- 省份：安徽省
- 国家：CN

## 注意事项

- 项目使用TypeScript开发，请确保代码符合类型规范
- 使用Pinia进行状态管理，请遵循相关规范
- UI组件优先使用TMUI提供的组件
- 注意Android隐私合规要求

## 贡献指南

1. Fork 本仓库
2. 创建新的功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

ISC License


