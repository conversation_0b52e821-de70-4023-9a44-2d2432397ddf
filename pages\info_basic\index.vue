<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="基础资料"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="资料更新后，经审核无误会展示出来！"></tm-text>
				</view>
			</view>
			<view class="area">
				<view class="form-item" v-if="form.name">
					<view class="title">姓名</view>
					<input class="input" placeholder="请输入" placeholder-class="c3" v-model="form.name" disabled readonly />
				</view>
				<view class="form-item" v-if="form.phone">
					<view class="title">电话</view>
					<input type="tel" class="input" placeholder="请输入" placeholder-class="c3" v-model="form.phone" disabled readonly />
				</view>
				<view class="form-item" @click="openArea">
					<view class="title">省市区</view>
					<input class="input" placeholder="请选择" placeholder-class="c3" disabled readonly v-model="form.areaStr" />
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item">
					<view class="title">街道地址</view>
					<input class="input" placeholder="请输入" placeholder-class="c3" v-model="form.address" />
				</view>
				<view class="form-item" @click="showBlood = true">
					<view class="title">血型</view>
					<input class="input" placeholder="请选择" placeholder-class="c3" disabled readonly v-model="form.blood" />
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item" @click="showEducation = true">
					<view class="title">学历</view>
					<input class="input" placeholder="请选择" placeholder-class="c3" disabled readonly v-model="form.education" />
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item form-item2 nb">
					<view class="title">自我介绍</view>
					<textarea maxlength="1000" class="textarea" placeholder-class="c3" placeholder="向户主们自我介绍下，您的从业经历服务经验与特长，以及拿手好菜吧" v-model="form.selfMsg"/>
					<view class="copa" @click="copySelfMsg">
						<text>一键复制</text>
						<text>自我介绍</text>
					</view>
				</view>
			</view>
			<view class="flex-center">
				<view class="button2" @click="back">返回上一页</view>
				<view class="button1" @click="saveBasicInfo">提交审核</view>
			</view>
			<tm-city-picker v-model:show="showdate" v-model="form.area" v-model:model-str="form.areaStr" :city="cityData"></tm-city-picker>
			<tm-picker v-model:show="showBlood" :columns="bloodList" v-model="form.blood" selectedModel="name" :immediateChange="true"></tm-picker>
			<tm-picker v-model:show="showEducation" :columns="educationList" v-model="form.education" selectedModel="name" :immediateChange="true"></tm-picker>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue"
import { onShow, onLoad } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
const showdate = ref(false)
const showBlood = ref(false)
const showEducation = ref(false)
const bloodList = [
	{text: 'A型', value: 'A'},
	{text: 'B型', value: 'B'},
	{text: 'AB型', value: 'AB'},
	{text: 'O型', value: 'O'},
	{text: '其他', value: '其他'},
]
const educationList = [
	{text: '小学', value: '小学'},
	{text: '初中', value: '初中'},
	{text: '高中', value: '高中'},
	{text: '大专', value: '大专'},
	{text: '本科', value: '本科'},
]

interface FormField {
	name: string
	is_change: number
	value: string
	item?: string[]
}

interface ApiResponse {
	code: number
	msg: string
	data: {
		auntname: FormField
		phone: FormField
		area: FormField
		address: FormField
		blood: FormField
		education: FormField
		selfMsg: FormField
	}
}

const form = ref({
	name: '',
	phone: '',
	area: [] as number[],
	areaStr: '',
	address: '',
	blood: [] as string[],
	education: [] as string[],
	selfMsg: '',
})

const getUserInfo = async () => {
	const res = await api.request.ajax<ApiResponse>({
		url: '/Center/getBasicInfo',
		type: 'POST',
	})
	
	if (res.code === 1 && res.data) {
		const data = res.data
		
		// Only update fields if they're empty or if modification is allowed
		if (data.auntname?.value && (data.auntname.is_change === 1 || !form.value.name)) {
			form.value.name = data.auntname.value
		}
		
		if (data.phone?.value && (data.phone.is_change === 1 || !form.value.phone)) {
			form.value.phone = data.phone.value
		}
		
		if (data.area?.value && (data.area.is_change === 1 || !form.value.area.length)) {
			form.value.area = data.area.value.split(',').map(Number)
			// Update areaStr if needed
			if (data.area.value && store.district) {
				// You may need to implement logic to convert area codes to strings
				// using the district data
			}
		}
		
		if (data.address?.value && (data.address.is_change === 1 || !form.value.address)) {
			form.value.address = data.address.value
		}
		
		if (data.blood?.value && (data.blood.is_change === 1 || !form.value.blood.length)) {
			form.value.blood = [data.blood.value]
		}
		
		if (data.education?.value && (data.education.is_change === 1 || !form.value.education.length)) {
			form.value.education = [data.education.value]
		}
		
		if (data.selfMsg?.value && (data.selfMsg.is_change === 1 || !form.value.selfMsg)) {
			form.value.selfMsg = data.selfMsg.value
		}
	}
}

const openArea = () => {
	if(!form.value.area.length){
		form.value.area = [12,186,2224]
	}
	showdate.value = true
}
const copySelfMsg = () => {
	if(!form.value.selfMsg) return
	uni.setClipboardData({
		data: form.value.selfMsg,
		success: function () {
			uni.showToast({ title: '复制成功', icon: 'none' });
		}
	});
}
const saveBasicInfo = async () => {
	// if(!form.value.name)return uni.showToast({ title: '请输入姓名', icon: 'none' })
	// if(!form.value.phone)return uni.showToast({ title: '请输入电话', icon: 'none' })
	if(!form.value.area.length)return uni.showToast({ title: '请选择省市区', icon: 'none' })
	if(!form.value.address)return uni.showToast({ title: '请输入街道地址', icon: 'none' })
	if(!form.value.blood.length)return uni.showToast({ title: '请选择血型', icon: 'none' })
	if(!form.value.education.length)return uni.showToast({ title: '请选择学历', icon: 'none' })
	if(!form.value.selfMsg)return uni.showToast({ title: '请输入自我介绍', icon: 'none' })
	const res = await api.request.ajax({
		url: '/Center/saveBasicInfo',
		type: 'POST',
		data: {
			name: form.value.name,
			phone: form.value.phone,
			area: form.value.area.join(','),
			address: form.value.address,
			blood: form.value.blood.join(','),
			education: form.value.education.join(','),
			selfMsg: form.value.selfMsg,
		}
	})
	if (res.code === 1) {
		uni.showToast({title: '提交成功', icon: 'success'})
		api.getUserInfo()
		setTimeout(() => {
			back()
		}, 1000)
	}else{
		uni.showToast({title: res.msg || '提交失败', icon: 'none'})
	}
}
const cityData = computed(() => store.district || [])
onLoad(async () => {
	if (!store.district) {
		await api.getDistrict()
	}
	await getUserInfo()
})
const back = () => {
	uni.navigateBack({
		fail:()=>{
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	})
}
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}

.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.area{
		padding-bottom: 160rpx;
		width: 690rpx;
		min-height: 1059rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.form-item{
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			min-height: 112rpx;
			padding: 40rpx 0;
			border-bottom: 1rpx solid #EBEBEB;
			.title{
				width: 140rpx;
				color: #333333;
				font-size: 26rpx;
				white-space:nowrap;
			}
			.input{
				text-align: left;
				flex:1
			}
			.right{
				margin-right: 27rpx;
			}
		}
		.form-item2{
			flex-direction: column;
			
			.title{
				width: 100%;
				text-align: left;
			}
			.textarea{
				margin-top: 34rpx;
				width: 100%;
				min-height: 269rpx;
				padding: 42rpx 30rpx;
				border: 1px solid #EBEBEB;
				border-radius: 10px;
			}
			.copa{
				width: 145rpx;
				height: 145rpx;
				background: linear-gradient(-15deg, #4A87F8, #58CFFD);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(204,204,204,0.41);
				border-radius: 50%;
				border: 8rpx solid #FFFFFF;
				display: flex;
				flex-direction: column;
				align-items:center;
				justify-content: center;
				font-weight: 500;
				font-size: 22rpx;
				color: #FFFFFF;
				line-height: 26rpx;
				position: relative;
				z-index: 2;
				top: 20rpx;
			}
		}
		.nb{
			border-bottom: none;
		}

	}
	.button1{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
	.button2{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #D5D5D5, #F0F0F0);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(192,192,192,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #333333;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
}
</style>