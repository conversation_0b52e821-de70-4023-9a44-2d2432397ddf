<template>
    <tm-app>
        <view class="container">
            <view class="topCont" :style="{ backgroundImage: `url('https://wx.wansao.com/statics/family/sendhall/dh_bg.png')` }">
                <view class="userInfo">
                    <view class="userInfo-left">
                        <image :src="avater" mode="" class="userInfo-left-avater" />
                        <view>
                            <view class="userInfo-left-name">{{name}}</view>
                            <view class="userInfo-left-wsb">我的皖嫂币<text>{{wsb}}个</text></view>
                        </view>
                    </view>
                    <navigator url="../wsb_mx/index" class="userInfo-right">
                        <image src="/static/img/dh_mx.png" mode="" class="userInfo-right-icon" />
                        <text class="userInfo-right-mx">查看我的明细</text>
                    </navigator>
                </view>
            </view>

            <view class="duihuan">
                <view class="topContTab">
                    <image src="/static/img/dh_hg.png" mode="" class="dh_hg" />
                    <view class="duihuan-mx">
                        <image src="/static/img/dh_jb.png" mode="" />
                        <view class="duihuan-mx-price">3币/单</view>
                    </view>

                    <template v-if="!store?.userInfo?.phone">
                        <view class="dhBtn" @click="showPhoneInfo = true">选择月份 兑换上工机会</view>
                    </template>
                    <template v-else>
                        <tm-time-picker
                            :showDetail="{
                                year: true,
                                month: true,
                                day: false,
                                hour: false,
                                minute: false,
                                second: false
                            }"
                            v-model="indexTime"
                            :defaultValue="indexTime"
                            format="YYYY-MM"
                            v-model:model-str="indexTimeStr"
                            :start="startTime"
                            :end="endTime"
                        >
                            <view class="dhBtn">选择月份 兑换上工机会</view>
                        </tm-time-picker>
                    </template>
                </view>

                <view class="duihuan-rule">
                    <image src="/static/img/dh_tit.png" mode="" class="dh-tit" />
                    <view class="duihuan-rule-cont">
                        <tm-html :content="dhRule" :tag-style="tagStyle" :container-style="htmlStyle"></tm-html>
                    </view>
                </view>

                <view class="tabShow">
                    <image src="/static/img/ysqb_tj.png" mode="" class="tabShow-img" />
                    <view class="tabShow-des">
                        <view class="p1">获得更多皖嫂币</view>
                        <view class="p2">2币/推荐</view>
                    </view>
                    <view class="tabShow-btn" @click="recommend">我要推荐客户</view>
                </view>

                <view class="tabShow">
                    <image src="/static/img/ysqb_gm.png" mode="" class="tabShow-img" />
                    <view class="tabShow-des">
                        <view class="p1">购买皖嫂币</view>
                        <view class="p2">300元/币</view>
                    </view>
                    <navigator class="tabShow-btn" url="../buywsb/index">立即购买</navigator>
                </view>

                <view class="banquan">安徽皖嫂家政服务有限责任公司</view>
            </view>
            <phoneinfo v-model="showPhoneInfo"/>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref,watch } from 'vue'
import { onShow, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import { useStore } from '@/until/mainpinia'
import phoneinfo from '@/components/phoneinfo/phoneinfo.vue'

const store = useStore()

// 分享功能
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// 响应式数据
const wsb = ref(0)
const avater = ref('')
const name = ref('')
const dhRule = ref('')
const indexTime = ref('')
const startTime = ref()
const endTime = ref('')
const isFamily = ref(null)
const msg = ref('')
const showPhoneInfo = ref(false)
const indexTimeStr = ref('')


const tagStyle = ref({
	image: 'max-width:100%;text-wrap: normal;white-space: normal;',
	img: 'max-width:100%;text-wrap: normal;white-space: normal;',
	video: 'max-width:100%;text-wrap: normal;white-space: normal;',
	table: 'max-width:100%;text-wrap: normal;white-space: normal;',
	view:'max-width:100%;text-wrap: normal;white-space: normal;',
	text:'max-width:100%;text-wrap: normal;white-space: normal;',
	span:'max-width:100%;text-wrap: normal;white-space: normal;',
	div:'max-width:100%;text-wrap: normal;white-space: normal;',
	p:'max-width:100%;text-wrap: normal;white-space: normal;margin: 10rpx 0',
})
const htmlStyle = ref('font-size: 30rpx;line-height: 60rpx;white-space: normal;')
// 获取用户信息
const getuserInfo = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/getUserWxInfo',
            type: 'POST'
        })
        if (res.code === 200) {
            avater.value = res.data.wxInfo.avatarUrl
            name.value = res.data.wxInfo.nickname
            wsb.value = res.data.total
            isFamily.value = res.data.wxInfo.isFamily
            msg.value = res.data.wxInfo.noMsg
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
    }
}

// 获取说明
const getshuoming = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/single',
            type: 'POST'
        })
        if (res.code === 200) {
            dhRule.value = res.data.content
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取说明失败:', error)
    }
}

// 选择日期
const selectDataTime = async (e: any) => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/exchange',
            type: 'POST',
            data: {
                month: indexTimeStr.value
            }
        })
        if (res.code === 200) {
            uni.showModal({
                title: '',
                content: res.msg,
                showCancel: false,
                success(res) {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '../ysqiangdan/index'
                        })
                    }
                }
            })
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('兑换失败:', error)
    }
}
watch(indexTimeStr, async (newVal) => {
    selectDataTime(newVal)
})


// 推荐
const recommend = () => {
    if (!store?.userInfo?.phone) {
        showPhoneInfo.value = true
    } else {
        uni.navigateTo({ url: '/pages/tuijian/index' })
    }
}


// 生命周期钩子
onLoad(() => {
    getuserInfo()
    getshuoming()
})

onShow(() => {
    getuserInfo()
    setTimeout(() => {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#E33631'
        })
    }, 100);
})
</script>

<style lang="scss" scoped>
page {
    background-color: #f4f6fa;
    font-size: 26rpx;
    color: #6f6e6e;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.topCont {
    width: 750rpx;
    height: 437rpx;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.userInfo {
    width: 750rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60rpx;

    &-left {
        display: flex;
        align-items: center;
        margin-left: 60rpx;

        &-avater {
            width: 130rpx;
            height: 130rpx;
            border-radius: 50%;
            border: 1rpx solid #f0d1d3;
            margin-right: 30rpx;
        }

        &-name {
            font-size: 40rpx;
            color: #fff;
        }

        &-wsb {
            font-size: 30rpx;
            color: #fff;

            text {
                font-size: 40rpx;
                margin-left: 5rpx;
            }
        }
    }

    &-right {
        width: 240rpx;
        height: 72rpx;
        line-height: 72rpx;
        background: #c11312;
        border-radius: 30rpx 0 0 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        &-mx {
            color: #fff;
            font-size: 28rpx;
        }

        &-icon {
            width: 25rpx;
            height: 27rpx;
            margin-right: 15rpx;
        }
    }
}

.duihuan {
    position: relative;
    top: -190rpx;

    &-mx {
        display: flex;
        align-items: center;

        image {
            width: 31rpx;
            height: 30rpx;
            margin-right: 10rpx;
        }

        &-price {
            font-size: 38rpx;
            color: #666;
        }
    }

    &-rule {
        margin-bottom: 30rpx;
        width: 690rpx;
        background: #fff;
        border-radius: 10rpx;
        box-shadow: 0 3rpx 16rpx #ddd;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40rpx 30rpx;
        box-sizing: border-box;

        &-cont {
            width: 100%;
            margin-top: 20rpx;
            font-size: 28rpx;
            color: #909399;
        }
    }
}

.topContTab {
    width: 690rpx;
    height: 308rpx;
    background: #fdf4f4;
    border-radius: 20rpx;
    position: relative;
    box-shadow: 0 3rpx 16rpx #ddd;
    margin-bottom: 30rpx;
    padding: 80rpx 50rpx 50rpx;
    box-sizing: border-box;
}

.dh_hg {
    position: absolute;
    top: 50rpx;
    right: 0;
    width: 231rpx;
    height: 203rpx;
}

.dh-tit {
    width: 302rpx;
    height: 49rpx;
}

.dhBtn {
    background: linear-gradient(180deg, #e6443c, #e53c36, #df2121);
    width: 414rpx;
    height: 89rpx;
    line-height: 89rpx;
    border-radius: 50rpx;
    text-align: center;
    color: #fff;
    font-size: 34rpx;
    margin-top: 30rpx;
}

.tabShow {
    margin-bottom: 30rpx;
    width: 690rpx;
    background: #fff;
    border-radius: 10rpx;
    box-shadow: 0 3rpx 16rpx #ddd;
    display: flex;
    align-items: center;
    padding: 30rpx;
    box-sizing: border-box;

    &-img {
        width: 115rpx;
        height: 115rpx;
    }

    &-des {
        width: 200rpx;
        margin-left: 30rpx;

        .p1 {
            color: #df2121;
            font-size: 28rpx;
        }

        .p2 {
            color: #333;
            font-size: 40rpx;
        }
    }

    &-btn {
        margin-left: 50rpx;
        width: 230rpx;
        height: 82rpx;
        background: #df2121;
        line-height: 82rpx;
        border-radius: 50rpx;
        text-align: center;
        font-size: 30rpx;
        color: #fff;
    }
}

.banquan {
    color: #c6c1c1;
    text-align: center;
    font-size: 28rpx;
}
</style> 