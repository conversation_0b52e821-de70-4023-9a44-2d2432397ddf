<template>
	<tm-app ref="app">
		<view class="main">
			<view class="card">
				<tm-image :width="686" :height="323" src="imageUrlPrefix/activity1.png"></tm-image>
				<view class="card_content">
					<view class="left">
						<view class="title">家政经理人培训班第十五期</view>
						<view class="time">2024年05月17日-18日</view>
					</view>
					<view class="right">
						<view class="button" @click="goLink('/pages/act_details/index')">立即报名</view>
					</view>
				</view>
			</view>
			<view class="card">
				<tm-image :width="686" :height="323" src="imageUrlPrefix/activity2.png"></tm-image>
				<view class="card_content">
					<view class="left">
						<view class="title">家政经理人培训班第十五期</view>
						<view class="time">2024年05月17日-18日</view>
					</view>
					<view class="right">
						<view class="button2">已结束</view>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1rpx solid #E8E8E8;
	.card {
		margin-top: 40rpx;
		width: 686rpx;
		height: 495rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		.card_content{
			display: flex;
			justify-content: space-between;
			padding: 45rpx 24rpx 45rpx 30rpx;
			.title{
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
			}
			.time{
				font-size: 24rpx;
				color: #9C9C9C;
			}
			.button{
				width: 209rpx;
				height: 87rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
				border-radius: 43rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #fff;
			}
			.button2{
				width: 209rpx;
				height: 87rpx;
				background: #FFFFFF;
				border-radius: 43rpx;
				border: 1rpx solid #A6A6A6;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #A6A6A6;
			}
		}
	}
}
</style>