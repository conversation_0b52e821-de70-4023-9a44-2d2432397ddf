import { defineStore } from 'pinia';

/**
 * 阿姨信息类型定义
 */
type UserInfo = Partial<{
  /** 微信ID */
  user_id: string;
  /** 会员ID */
  hid: string;
  /** 阿姨名称 */
  name: string;
  /** 头像 */
  photo: string;
  /** 年龄，false不展示 */
  age: string;
  /** 籍贯 */
  area: string;
  /** 工作经验，false不展示 */
  workYear: number;
  /** 学历 */
  education: string;
  /** 自我介绍 */
  content: string;
  /** 自我介绍字数 */
  contentLen: number;
  /** 服务时间段 */
  zhujiastr: string;
  /** 皖嫂已签单数 */
  signedBill: string;
  /** 育婴师-个人证照 */
  yysPersonPic: Array<{
    /** 缩略图 */
    thumbUrl: string;
    /** 原图 */
    SourceUrl: string;
  }>;
  /** 育婴师工作经历 */
  yysExperList: Array<{
    /** ID */
    id: string;
    /** 会员ID */
    hid: string;
    /** 状态 */
    status: string;
    /** 说明 */
    remark: string;
    /** 区域 */
    area: string;
  }>;
  /** 核心认证 */
  signPic: string;
  /** 职业等级，可兼任多项 */
  jobName: string[];
  /** 职业技能 */
  skillsInfo: Array<{
    /** 技能名称 */
    name: string;
  }>;
  /** 认证图标 */
  certPic: Array<{
    /** 认证图标 */
    pic: string;
    /** 图标名称 */
    name: string;
    /** 描述 */
    desc: string;
    /** 模板 */
    tpl: string;
    /** TID */
    tid: number;
    /** 显示状态 */
    show: boolean;
    /** 图片集合 */
    imgs: string[];
  }>;
  /** 评价二维码 */
  evaluateUrl: string;
}>;

/**
 * 首页数据类型定义
 */
type Setting = Partial<{
  /** 首页轮播 */
  bannerList: Array<{
    /** ID */
    id: string;
    /** 标题 */
    title: string;
    /** 链接 */
    link: string;
    /** 图片链接 */
    src: string;
    /** 访问是否需要授权 */
    is_auth: string;
  }>;
  
  /** 广告位轮播 */
  adList: Array<{
    /** ID */
    id: string;
    /** 标题 */
    title: string;
    /** 链接 */
    link: string;
    /** 图片链接 */
    src: string;
    /** 访问是否需要授权 */
    is_auth: string;
  }>;

  /** 快捷按钮 */
  btnList: Array<{
    /** ID */
    id: string;
    /** 标题 */
    title: string;
    /** 链接 */
    link: string;
    /** 图片链接 */
    src: string;
    /** 访问是否需要授权 */
    is_auth: string;
  }>;

  /** 抢单大厅 */
  bookingHall: Array<{
    /** ID */
    id: string;
    /** 姓名 */
    name: string;
    /** 城市 */
    city: string;
    /** 区域 */
    area: string;
    /** 服务类型 */
    service: string;
    /** 住家��态 */
    zjStr: string;
    /** 是否抢单大厅 */
    qddt: boolean;
  }>;

  /** 我的简历与档期 */
  memberInfo: {
    /** 是否开启 */
    switch: number;
    /** 按钮数据 */
    resume_btn: Array<{
      /** 是否需要授权 */
      is_auth: number;
      /** 标题 */
      title: string;
      /** 链接 */
      url: string;
    }>;
    /** 档期数据 */
    schedule: Array<{
      /** 百分比 */
      percent: number;
      /** 月份 */
      month: number;
      /** 占用天数 */
      allDay: number;
    }>;
    /** 添加档期 */
    schedule_btn: {
      /** 是否需要授权 */
      is_auth: number;
      /** 标题 */
      title: string;
      /** 链接 */
      url: string;
    };
  };

  /** 周排行数据 */
  rankList: {
    /** 是否开启 */
    switch: number;
    /** 列表 */
    list: Array<any>; // 未提供详细字段，使用 any
    /** 背景列表 */
    bgList: Array<any>; // 未提供详细字段，使用 any
  };

  /** 分享宝资讯 */
  newsList: {
    /** 是否开启 */
    switch: number;
    /** 栏目数据 */
    items: Array<{
      /** 栏目ID */
      id: number;
      /** 栏目名称 */
      title: string;
    }>;
    /** 默认栏目数据 */
    data: Array<{
      /** ID */
      id: string;
      /** ��击数 */
      hits: string;
      /** 内页小程序链接 */
      link: string;
      /** 标题 */
      title: string;
      /** 日期 */
      date: string;
      /** 题图 */
      img: boolean;
    }>;
  };

  /** 页面分享配置 */
  shareData: {
    /** 标题 */
    title: string;
    /** 图片链接 */
    imageUrl: string;
  };
}>;

// 添加District类型定义
type DistrictItem = {
	text: string;
	value: string;
	children?: DistrictItem[];
}

export const useStore = defineStore('main', {
	state: () => {
		return {
			// acc:0,
			screenScene:null,
			thisAppointmentType:null,
			pid:null,
			source:null,
			servicephone:null,
			userInfo:{} as UserInfo,
			setting:{} as Setting,
			district: null as DistrictItem[] | null,
			token:null,
			userStatus:0,
			userStatusDesc:'',
			mainMenuSwitch:1
		}
	},
	actions: {
		// setTabber(page:number) {
  //           this.acc = page
		// },
	},
});

