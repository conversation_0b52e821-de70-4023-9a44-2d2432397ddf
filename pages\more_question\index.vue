<template>
	<tm-app ref="app">
		<view class="main">
			<view class="top">
				<tm-image class="absolute" :width="688" :height="188" src="/static/img/qetop.png"></tm-image>
				<!-- <view class="text_area">
					<view class="text">
						已有<text class="t1">{{apiData.statistics?.certTotal}}位</text>阿姨完成认证成功入驻
					</view>
					<view class="text">
						已有<text class="t1">{{apiData.statistics?.moneyTotal}}位</text>阿姨成功拿到奖励金
					</view>
				</view> -->
				<view class="p2">
					已有
					<text class="p2_text">{{ apiData.statistics?.certTotal }}</text>
					名阿姨入驻<br>
					已服务
					<text class="p2_text">{{ apiData.statistics?.customerTotal }}</text>
					位客户</view>
			</view>
			<view class="center" v-if="apiData?.notice?.length">
				<tm-image :width="30" :height="34" src="/static/img/tz.png"></tm-image>
				<view class="text-scroll">
					<view class="text-scroll-content" :style="{ transform: `translateY(-${currentIndex * 40}rpx)` }">
						<view class="text-scroll-item" v-for="(item, index) in apiData.notice" :key="index">
							{{item}}
						</view>
					</view>
				</view>
			</view>

			<view class="mod_card">
				<view class="title">常见问题</view>
				<view class="content">
					<view class="p1">
						<view class="question_item" v-for="(problem, index) in problems" :key="problem.id">
							<view class="question_header" @click="handleQuestionClick(problem.id)">
								<view class="p1_1">
									{{index + 1}}、{{problem.title}}
								</view>
								<tm-icon :font-size="24" :name="activeId === problem.id ? 'tmicon-angle-up' : 'tmicon-angle-down'" />
							</view>
							<view class="answer"
									:style="{ maxHeight: activeId === problem.id ? answerHeight + 'px' : '0' }"
									:class="{'answer-expanded': activeId === problem.id}">
								{{problemInfo?.content || ''}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { share } from '@/tmui/tool/lib/share'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

// 问题数据类型
interface Problem {
	title: string
	id: string
}

interface ProblemInfo {
	id: string
	title: string
	content: string
}

interface ApiResponse {
	statistics: {
		certTotal: number
		moneyTotal: number
	}
	notice: string[]
	list: Problem[]
	problemInfo?: ProblemInfo
}

const problems = ref<Problem[]>([])
const apiData = ref<ApiResponse>({
	statistics: {
		certTotal: 0,
		moneyTotal: 0
	},
	notice: [],
	list: []
})
const activeId = ref<string>('')
const problemInfo = ref<ProblemInfo | null>(null)
const answerHeight = ref(0)
const currentIndex = ref(0)

const getData = async () => {
	const res = await api.request.ajax({
		url: '/money/getProblem',
		type: 'POST',
	})
	if (res.code === 1) {
		problems.value = res.data.list
		apiData.value = res.data
		startScroll()
	}
}

const calculateHeight = (content: string) => {
	// 假设每个字符平均宽度为14px，行高为1.6
	const charsPerLine = Math.floor(606 / 14) // 606rpx是容器宽度
	const lines = Math.ceil(content.length / charsPerLine)
	// 24rpx是字体大小，1.6是行高，20rpx是上下padding
	return lines * 24 * 1.6 + 40
}

const handleQuestionClick = async (id: string) => {
	if (activeId.value === id) {
		activeId.value = ''
		answerHeight.value = 0
		return
	}
	
	const res = await api.request.ajax({
		url: '/money/getProblem',
		type: 'POST',
		data: { id }
	})
	
	if (res.code === 1) {
		activeId.value = id
		problemInfo.value = res.data.problemInfo
		// 计算答案内容的高度
		if (res.data.problemInfo?.content) {
			answerHeight.value = calculateHeight(res.data.problemInfo.content)
		}
	}
}

const startScroll = () => {
	setInterval(() => {
		if (apiData.value.notice?.length > 0) {
			currentIndex.value = (currentIndex.value + 1) % apiData.value.notice.length
		}
	}, 3000) // 每3秒切换一次
}

onLoad(() => {
	getData()
})
</script>

<style lang="less" scoped>
	.main {
		width: 750rpx;
		background-color: #fff;
		min-height: 100vh;
		overflow-x: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		.top{
			margin-top: 30rpx;
			width: 688rpx;
			height: 188rpx;
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: center;
			.text_area{
				position: relative;
				color: #fff;
				margin-left: 60rpx;
				font-size: 24rpx;
				.t1{
					font-size: 36rpx;
					font-weight: bold;
				}
			}
			.p2 {
				position: relative;
				color: #fff;
				margin:0 0 0 60rpx;
				font-size: 24rpx;
                .p2_text{
                    font-size: 40rpx;
					color: #fff;
                    font-weight: bold;
                    margin: 0 10rpx;
                }
			}
		}
		.center{
			margin-top: 30rpx;
			width: 687rpx;
			height: 62rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
			border-radius: 31rpx;
			display: flex;
			align-items: center;
			padding: 0 42rpx;
			.text-scroll {
				flex: 1;
				height: 40rpx;
				overflow: hidden;
				margin-left: 20rpx;
				
				&-content {
					transition: transform 0.5s;
				}
				
				&-item {
					height: 40rpx;
					line-height: 40rpx;
					font-size: 24rpx;
					color: #666;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
		.mod_card{
			margin-top: 30rpx;
			.content{
				.p1{
					width: 100%;
					.question_item {
						width: 100%;
						border-bottom: 1rpx solid #F3F3F3;
						
						.question_header {
							display: flex;
							justify-content: space-between;
							align-items: center;
							padding-right: 20rpx;
							
							.p1_1 {
								flex: 1;
								font-size: 26rpx;
								height: 80rpx;
								line-height: 80rpx;
							}
						}
						
						.answer {
							font-size: 24rpx;
							color: #666;
							padding: 0 30rpx 20rpx;
							line-height: 1.6;
							max-height: 0;
							overflow: hidden;
							transition: 0.3s;
							opacity: 0;
							transform-origin: top;
							
							&.answer-expanded {
								opacity: 1;
							}
						}
					}
				}
			}
		}
	}
</style>