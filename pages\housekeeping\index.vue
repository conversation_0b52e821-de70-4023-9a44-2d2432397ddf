<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'上户宝'" :scrollTop="scrollTop" />
			<view class="swipe">
				<image class="banner-img" src="/static/img/banner4.png" mode="aspectFill"></image>
			</view>
			<view class="resume_cardbg">
				<image src="/static/img/resume_cardbg.png" class="resume_cardbg_mainImg"></image>
				<view class="resume_cardbg_content">
					<view class="p1"><text class="p1_text">上户宝</text></view>
					<view class="p2">管理个人资料、证照<br>下载名片，邀请客户评价</view>
				</view>
				<view class="resume_cardbg_content">
					<view class="box" @click="goLink('/pages/clock/index')">
						<view class="circle1"></view>
						<view class="circle2"></view>
						<view class="boxcontent">
							<tm-icon name="tmicon-position-fill" :font-size="50" color="#fff"></tm-icon>
							<view class="text1">上户打卡</view>
						</view>
					</view>
				</view>
			</view>
			<view class="mt-n12">
				<tm-carousel autoplay :round="0" :width="721" :height="197" :indicatorDots="false" imgmodel="widthFix"
					color="#FCD9DB" :list="adList" rangKey="src" @click="item=>goLink(item.url||'/pages/news/index?type=1')"></tm-carousel>
			</view>
			<view class="area2">
				<view class="resume_card" v-for="(item, index) in cardList" :key="index" @click="goLink(item.url)">
					<image class="bg-image" :src="item.bgImg" mode="aspectFill"></image>
					<view class="card_content">
						<view class="p1">{{item.title}}</view>
						<view class="p2">{{item.desc}}</view>
					</view>
				</view>
			</view>
			<tabber></tabber>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import {snb} from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const {NavigationBarTitle} = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})
const adList = ref([])
const cardList = ref([
	{
		bgImage: 'imageUrlPrefix/homekeepcard1.png',
		title: '上下户打卡',
		desc: '按时打卡，与工资息息相关哦！',
		link: '/pages/clock/index'
	},
])
// 获取首页信息
const getIndexInfo = async () => {
    try {
        const res = await api.request.ajax({
            url: '/work/index',
            type: 'POST'
        })
        if (res.code !== 1) {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return false
        }
        cardList.value = res.data.btnList
        adList.value = res.data.adList
		console.log(cardList.value);
		
    } catch (error) {
        console.error('获取首页信息失败:', error)
    }
}

// 生命周期钩子
onLoad(() => {
    getIndexInfo()
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.resume_cardbg {
		width: 690rpx;
		height: 313rpx;
		position: relative;
		z-index: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 69rpx 0 56rpx;
		.resume_cardbg_mainImg{
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}
		.resume_cardbg_content{
			display: flex;
			flex-direction: column;
			align-items: flex-start;
			position: relative;
			z-index: 2;
			.p1 {
				position: relative;
				.p1_text{
					font-weight: bold;
					font-size: 61rpx;
					color: #333;
					position: relative;
					z-index: 2;
				}
				&::after{
					width: 197rpx;
					height: 14rpx;
					background: rgba(242, 62, 70, 0.45);
					border-radius: 7rpx;
					content: '';
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					bottom: 4rpx;
				}
			}

			.p2 {
				margin-top: 50rpx;
				width: 270rpx;
				font-size: 24rpx;
				color: #9C9C9C;
				line-height: 36rpx;
			}
			.resume_cardbg_button {
				width: 262rpx;
				height: 88rpx;
				background: linear-gradient(-15deg, #F31630, #FF6136);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
				border-radius: 44rpx;
				font-weight: 500;
				font-size: 30rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: 60rpx;
			}
			.resume_cardbg_button2{
				margin-top: 30rpx;
				background: linear-gradient(-31deg, #7681F6, #574DF2);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(118, 129, 246, 0.41);
			}
			.box{
				width: 198rpx;
				height: 198rpx;
				position: relative;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				.circle1{
					width: 166rpx;
					height: 166rpx;
					background: linear-gradient(-15deg, #F31630, #FF984C);
					border-radius: 50%;
					opacity: 0.2;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					animation: ripple 1.5s linear infinite;
				}
				.circle2{
					width: 166rpx;
					height: 166rpx;
					background: linear-gradient(-15deg, #F31630, #FF984C);
					border-radius: 50%;
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
				}
				.boxcontent{
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					position: relative;
					z-index: 2;
					.text1{
						margin-top: 10rpx;
						font-size: 24rpx;
						color: #fff;
					}
				}
			}
		}
	}
	.area2 {
		margin-top: 40rpx;
		width: 688rpx;
	}
}

@keyframes ripple {
	0% {
		transform: translate(-50%, -50%) scale(1);
		opacity: 0.2;
	}
	100% {
		transform: translate(-50%, -50%) scale(1.8);
		opacity: 0;
	}
}
</style>