<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff"
						label="身份证"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff"
						label="资料更新后，经审核无误会展示出来！"></tm-text>
				</view>
			</view>
			<view class="area">
				<view class="form-item">
					<view class="title">身份证号</view>
					<input class="input" type="idcard" placeholder="请输入" placeholder-class="c3" v-model="form.idcard" />
				</view>
				<view class="form-item">
					<view class="title">有效期</view>
					<tm-time-picker
						:showDetail="{
							year: true,
							month: true,
							day: true,
							hour: false,
							minute: false,
							second: false
						}"
						v-model="form.date_copy"
						:defaultValue="form.date_copy"
						format="YYYY-MM-DD"
						v-model:model-str="form.date"
						start="2000-1-1 00:00:00"
						end="2048-6-15 23:59:59"
						class="fulled"
					>
					<input class="input" placeholder="请选择" placeholder-class="c3" v-model="form.date" />
					</tm-time-picker>
					
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<tm-upload class="mt-n13" color="rgba(0,0,0,0)" :imageHeight="370" :width="612" :rows="1" :maxFile="1"
					v-model="list1" :url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024" :onSuccessAfter="cardUpload1.onSuccess"
					:onRemove="cardUpload1.onRemove" :chooesefileAfter="cardUpload1.chooesefileAfter" :formData="{
						token,
						hid,
						type: '1',
					}">
					<template v-slot:icon>
						<tm-image :width="612" :height="370" src="/static/img/idcard1.png"></tm-image>
					</template>
				</tm-upload>

				<tm-upload class="mt-n13" color="rgba(0,0,0,0)" :imageHeight="370" :width="612" :rows="1" :maxFile="1"
					v-model="list2" :url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024" :onSuccessAfter="cardUpload2.onSuccess"
					:onRemove="cardUpload2.onRemove" :chooesefileAfter="cardUpload2.chooesefileAfter" :formData="{
						token,
						hid,
						type: '2',
					}">
					<template v-slot:icon>
						<tm-image :width="612" :height="370" src="/static/img/idcard2.png"></tm-image>
					</template>
				</tm-upload>
			</view>
			<view class="flex-center mt-n18">
				<view class="button2" @click="back">返回上一页</view>
				<view class="button1" @click="saveIdcard">提交审核</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue"
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import { useIdCardUpload } from '@/until/useIdCardUpload'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
const form = ref({
	idcard: '',
	date: '',
	date_copy: '',
	pic_back_id: '',
	pic_hand_id: ''
})
// const idcard = computed(() => store?.userInfo?.idcard)
// watch(idcard,(val)=>{
// 	if(val){
// 		if(val.id_number&&!form.value.idcard){
// 			form.value.idcard = val.id_number
// 		}
// 		if(val.idcard_date&&!form.value.date){
// 			form.value.date = val.idcard_date
// 		}
// 	}
// },{immediate:true})
const token = computed(() => store.token)
const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const hid = computed(() => store?.userInfo?.hid)
// 使用封装的身份证上传逻辑
const cardUpload1 = useIdCardUpload()
const cardUpload2 = useIdCardUpload()
const {list:list1} = cardUpload1
const {list:list2} = cardUpload2


// 监听身份证正面照片ID变化
watch(cardUpload1.picId, (newVal) => {
	form.value.pic_back_id = newVal
})

// 监听身份证反面照片ID变化
watch(cardUpload2.picId, (newVal) => {
	form.value.pic_hand_id = newVal
})

// 提交审核
const saveIdcard = async () => {
	if(!form.value.idcard) return uni.showToast({title: '请输入身份证号', icon: 'none'})
	if(!/^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))(\d|X|x)$/.test(form.value.idcard)) return uni.showToast({title: '身份证号格式错误', icon: 'none'})
	if(!form.value.date) return uni.showToast({title: '请选择有效期', icon: 'none'})
	if(!form.value.pic_back_id) return uni.showToast({title: '请上传身份证正面照片', icon: 'none'})
	if(!form.value.pic_hand_id) return uni.showToast({title: '请上传身份证反面照片', icon: 'none'})
	const res = await api.request.ajax({
		url: '/Center/saveIdcard',
		type: 'POST',
		data: form.value
	})
	if (res.code === 1) {
		uni.showToast({title: '提交成功', icon: 'success'})
		api.getUserInfo()
		setTimeout(() => {
			back()
		}, 1000)
	}else{
		uni.showToast({title: res.msg || '提交失败', icon: 'none'})
	}
}
const back = () => {
	uni.navigateBack({
		fail:()=>{
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	})
}
</script>

<style lang="less" scoped>
/deep/.c3 {
	color: #D3D2D2;
}

.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.area {
		padding-bottom: 160rpx;
		width: 690rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.form-item {
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			min-height: 112rpx;
			padding: 40rpx 0;
			border-bottom: 1rpx solid #EBEBEB;

			.title {
				width: 140rpx;
				flex-shrink: 0;
				color: #333333;
				font-size: 26rpx;
			}

			.input {
				text-align: left;
				flex: 1
			}

			.right {
				margin-right: 27rpx;
			}
		}

		.form-item2 {
			flex-direction: column;

			.title {
				width: 100%;
				text-align: left;
			}

			.textarea {
				margin-top: 34rpx;
				width: 100%;
				height: 269rpx;
				padding: 42rpx 30rpx;
				border: 1px solid #EBEBEB;
				border-radius: 10px;
			}

			.copa {
				width: 145rpx;
				height: 145rpx;
				background: linear-gradient(-15deg, #4A87F8, #58CFFD);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(204, 204, 204, 0.41);
				border-radius: 50%;
				border: 8rpx solid #FFFFFF;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				font-weight: 500;
				font-size: 22rpx;
				color: #FFFFFF;
				line-height: 26rpx;
				position: relative;
				z-index: 2;
				top: -72rpx;
			}
		}

		.nb {
			border-bottom: none;
		}

	}

	.button1 {
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}

	.button2 {
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #D5D5D5, #F0F0F0);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(192, 192, 192, 0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #333333;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
}
</style>