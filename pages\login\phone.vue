<template>
	<tm-app>
		<view class="main">
			<image src="/static/img/loginbg.svg" mode="widthFix" class="waves"></image>
			<view class="logo-area">
				<view class="logo-bg"></view>
				<image src="/static/img/logo_1.png" mode="widthFix" class="logo"></image>
			</view>
			<view class="nav">
				<view class="nav-item wa">欢迎使用 皖嫂一家亲</view>
			</view>
			<view class="content">
				<button type="default" class="button" open-type="getPhoneNumber"
					@getphonenumber="decryptPhoneNumber">手机号一键登陆</button>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
	import {ref,getCurrentInstance} from 'vue'
	import { onLoad } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import * as api from '@/api/index.js'
	import { goLink } from '@/until/index'
	import { share } from "@/tmui/tool/lib/share";
	import { useStore } from '@/until/mainpinia';
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const store = useStore();
	const frompath = ref('')
	const { proxy } = getCurrentInstance();
	const _this = proxy
	onLoad(()=>{
	  const eventChannel = _this.getOpenerEventChannel()
	  if(!eventChannel.on)return
	  eventChannel.on('frompath', function(data) {
		frompath.value = data.data
		console.log(frompath.value);
	  })
	})
	
	
	const decryptPhoneNumber = (e) => {
		console.log(e);
		if (e.detail.code) {
			api.request.ajax({
				url: '/login/getWxPhone',
				type: 'POST',
				data:{
					code:e.detail.code,
					frompath:frompath.value
				}
			}).then(res => {
				if(res.code===1){
					store.$patch((state) => {
						state.userInfo.phone = res.data.phone
					})
					uni.navigateBack({
						fail:()=>{
							uni.reLaunch({
								url:'/pages/index/index'
							})
						}
					});
				}else{
					uni.showToast({ title:res.msg, icon:'none' })
				}
			})
		} else {
	
		}
	}
</script>

<style lang="less" scoped>
	@import url(index.less);
	.main{
		.content{
			padding-bottom: 200rpx;
		}
	}
	.agree{
		margin-top: 40rpx;
		display: flex;
		align-items: center;
		color: #909399 !important;
		.agree_bottom{
			display: flex;
			align-items: center;
		}
	}
</style>