/**
 * 分享配置
 */
import { wxshareConfig } from "./interface"
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { useTmpiniaStore } from './tmpinia'
import * as api from '@/api/index.js'
export const share = (args : wxshareConfig = {}) => {
	const store = useTmpiniaStore()
	// console.log(store.tmStore.wxshareConfig_miniMp);
	let defaultWxshareConfig : wxshareConfig = {
		...args
	}
	// 分享朋友默认配置
	let shareAppOptions: wxshareConfig = Object.assign({},store.tmStore.wxshareConfig_miniMp)
	// 分享朋友圈默认配置
	let shareTimeOptions: wxshareConfig = Object.assign({},store.tmStore.wxshareConfig_miniMp)
	// onShareAppMessage
	const shareApp = (options: wxshareConfig = {}) => {
		onShareAppMessage((res): wxshareConfig => {
			// console.log(res.from,res.target);
			// if(res.from==='menu'){
				if(shareAppOptions.aid&&shareAppOptions.column){
					api.request.ajax({
						url: '/home/<USER>',
						type: 'POST',
						data: { 
							aid:shareAppOptions.aid,
							column:shareAppOptions.column,
							platform:'family'
						}
					})
				}
				return {
					...defaultWxshareConfig,
					...options,
					...shareAppOptions
				}
			// }
			// if(res.from==='button'){
			// 	return {
			// 		title:'',
			// 		imageUrl:'',
			// 		desc:''
			// 	}
			// }
		})
	}
	// 添加onShareAppMessage参数  
	const setShareApp = (options: wxshareConfig = {}) => {
		shareAppOptions = options
	}
	// onShareTimeline  
	const shareTime = (options: wxshareConfig = {}) => {
		onShareTimeline((): wxshareConfig => {
			return {
				...defaultWxshareConfig,
				...options,
				...shareTimeOptions
			}
		})
	}
	// 添加onShareTimeline参数  
	const setShareTime = (options = {}) => {
		shareTimeOptions = options
	}

	return {
		onShareAppMessage: shareApp,
		onShareTimeline: shareTime,
		setShareApp,
		setShareTime,
	}
}