<template>
	<tm-app>
		<web-view :src="url"></web-view>
	</tm-app>
</template>

<script lang="ts" setup>
	import { ref,getCurrentInstance } from 'vue'
	import { onShow, onLoad, } from '@dcloudio/uni-app'
	import tmApp from '@/tmui/components/tm-app/tm-app.vue'
	import { share } from "@/tmui/tool/lib/share";
	const { onShareAppMessage, onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline()
	const url = ref('')
	const { proxy } = getCurrentInstance();
	const _this = proxy
	onLoad(()=>{
	  const eventChannel = _this.getOpenerEventChannel() ;
	  eventChannel.on('webLink', function(data) {
		url.value = data.data
	  })
	})
</script>

<style lang="less" scoped>

</style>