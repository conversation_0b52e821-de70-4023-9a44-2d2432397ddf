/* #ifdef H5 */
body::-webkit-scrollbar,
div::-webkit-scrollbar,
*::-webkit-scrollbar {
	display: none;
}

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}
uni-page-body {
	height: 100% !important;
	min-height: auto !important;
}
body.pages-index-index uni-page-body,
body {
	padding-bottom: 0 !important;
}

text {
	font-family: 'sans-serif';
}

/* #endif */

/* #ifndef APP-NVUE */
page,
view,
uni-view,
text,
swiper,
swiper-item,
image,
navigator,
button,
input,
textarea{
	margin: 0;
	padding: 0;
	border: none;
	box-sizing: border-box;
	background-size: 100% 100%;
	background-repeat: no-repeat;
	max-width: 100%;
	word-wrap: break-word;
}

image {
	height: auto;
}
view {
	box-sizing: border-box;
}

input,
button,
textarea{
	border: none;
	background: none;
}

input:focus,
button:focus{
	outline: none;
}
page {
	background: #fff;
	font-size: 26rpx;
	font-family: '微软雅黑', 'Microsoft YaHei', <PERSON><PERSON><PERSON>, <PERSON>l;
	width: 100%;
	overflow-x: hidden;
}
page::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
page ::v-deep ::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
button::after {
	display: none;
}
/* #endif */
scroll-view ::v-deep ::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
.font-bold{
	font-weight: bold;
}
.w-150{
	width: 150rpx;
}
.text-align-justify{
	text-align: justify;
}
.main {
	background-color: #f5f5f5;
	width: 100%;
	overflow-x: hidden;
}
.main::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
.main ::v-deep ::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
tm-app {
	align-items: center;
	width: 100%;
	overflow-x: hidden;
}
tm-app::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
tm-app ::v-deep ::-webkit-scrollbar {
	display: none;
	width: 0 !important;
	height: 0 !important;
	-webkit-appearance: none;
	background: transparent;
}
.qddt {
	margin: 50rpx auto 0;
	width: 690rpx;
	// height: 980rpx;
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 2rpx 2rpx 10rpx 2rpx #e6e6e6;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	.bg-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}
	.content {
		position: relative;
		z-index: 2;
		width: 100%;
	}
	.stit {
		width: 100%;
		position: relative;
		display: flex;
		align-items: center;
		padding: 30rpx 30rpx;

		&::before {
			content: '';
			width: 8rpx;
			height: 30rpx;
			background: #fef1ae;
			border-radius: 4rpx;
			margin-right: 18rpx;
		}
		.title_wrap {
			display: flex;
			align-items: flex-end;

			.title {
				font-size: 36rpx;
				color: #fff;
				font-weight: bold;
			}

			.subtitle {
				font-size: 24rpx;
				color: #fff;
				margin-left: 16rpx;
				margin-bottom: 4rpx;
			}
		}
	}
	.content{
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	.card {
		width: 630rpx;
		background: #fff;
		border-radius: 24rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		
		
		&:hover {
			transform: translateY(-2rpx);
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		}

		.top {
			padding: 24rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 2rpx solid #f5f5f5;

			.name {
				font-size: 28rpx;
				color: #2d3436;
				font-weight: 600;
				
				&::before {
					content: '雇主：';
					color: #666;
					font-weight: normal;
				}
			}

			.location {
				font-size: 24rpx;
				color: #666;
				display: flex;
				align-items: center;
				
				&::before {
					content: '';
					width: 24rpx;
					height: 24rpx;
					margin-right: 8rpx;
					background: url('/static/img/location.png') no-repeat center/contain;
				}
			}
		}

		.bottom {
			padding: 24rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.desc {
					font-size: 28rpx;
					color: #666;
					line-height: 1.5;
					position: relative;
					padding-left: 10rpx;
					
				}
			}

			.button {
				min-width: 140rpx;
				height: 64rpx;
				line-height: 64rpx;
				border-radius: 32rpx;
				font-size: 26rpx;
				font-weight: 500;
				padding: 0 30rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background: linear-gradient(135deg, #FF4B4B, #FB243C);
				color: #fff;
				box-shadow: 0 4rpx 12rpx rgba(251, 36, 60, 0.2);
				
				&:active {
					transform: scale(0.95);
					opacity: 0.9;
				}
				
			}
		}
	}
}
.mod_card{
	margin-top: 60rpx;
	width: 690rpx;
	min-height: 348rpx;
	background: #FFFFFF;
	box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
	border-radius: 26rpx;
	padding: 68rpx 51rpx;
	position: relative;
	.title{
		display: flex;
		align-items: center;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		padding-bottom: 15rpx;
		border-bottom: 1rpx solid #E6E6E6;
		&::before{
			content: '';
			width: 8rpx;
			border-radius: 4rpx;
			height: 30rpx;
			background: linear-gradient(-31deg, #FF6136, #F31630);
			margin-right: 22rpx;
		}
	}
	.content{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 54rpx;
		.p1{
			font-size: 26rpx;
			line-height: 50rpx;
			color: #9c9c9c;
			
			.p1_1{
				display: block;
				.t1{
					color: #ED3444;
					font-size: 36rpx;
				}
			}
		}
		.button{
			width: 209rpx;
			height: 100rpx;
			margin-left: 0;
			flex-shrink: 0;
			background: linear-gradient(-31deg, #FF6136, #F31630);
			border-radius: 50rpx;
			font-size: 30rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.icon_content{
			width: 20%;
			display: flex;
			flex-direction: column;
			align-items: center;
			.text{
				font-size: 22rpx;
				font-weight: bold;
			}
		}
	}
	.info{
		width: 582rpx;
		height: 106rpx;
		border-bottom: 1rpx solid #E6E6E6;
		display: flex;
		padding-top: 50rpx;
		.left{
			font-size: 26rpx;
			color: #333;
			width: 80rpx;
			text-align: justify;
			text-align-last:  justify;
			white-space:nowrap;
		}
		.right{
			margin-left: 58rpx;
			font-size: 26rpx;
			color: #333;
		}
	}
	.amount_record{
		.item{
			height: 132rpx;
			width: 582rpx;
			border-bottom: 1rpx solid #E6E6E6;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.item_left{
				display: flex;
				flex-direction: column;
				.item_title{
					font-size: 30rpx;
					font-weight: 500;
					color: #333;
				}
				.item_time{
					font-size: 22rpx;
					color: #9c9c9c;
				}
			}
			.item_value{
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
			}
			.amount_red{
				color: #ED3444;
			}
		}
		.back{
			margin-top: 80rpx;
			width: 609rpx;
			height: 80rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
			border-radius: 40rpx;
			font-size: 30rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
		}

	}
}
.float-button{
	position: fixed;
	z-index: 9;
	right: 10rpx;
	bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.dkpopup{
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: -100rpx;
	width: 574rpx;
	height: 660rpx;
	.dkcg{
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		left: 0;
	}
	.title{
		font-weight: bold;
		font-size: 50rpx;
		color: #E54D59;
		position: relative;
		margin-top: 70rpx;
		margin-left: 150rpx;
	}
	.right2{
		width: 155rpx;
		position: relative;
		z-index: 2;
		margin-top: 80rpx;
	}
	.content{
		position: relative;
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		text-align: justify;
		color: #A6A6A6;
		font-weight: 500;
		font-size: 30rpx;
		line-height: 48rpx;
		.content-text{
			max-width: 420rpx;
		}
	}
	.content2{
		margin-top: 100rpx;
		color: #646363;
	}
	.content3{
		margin-top: 270rpx;
		color: #3c3c3c;
	}
	.confirm{
		width: 289rpx;
		height: 87rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		border-radius: 44rpx;
		color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 30rpx;
		position: absolute;
		bottom: 50rpx;
	}
	.button_area{
		display: flex;
		justify-content: center;
		margin-top: 80rpx;
		position: relative;
		.button{
			width: 225rpx;
			height: 90rpx;
			margin: 0 16rpx;
			background: linear-gradient(-15deg, #4A87F8, #58CFFD);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(48,139,227,0.41);
			border-radius: 45rpx;
			font-size: 30rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.button2{
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		}
	}
}
.process_area{
	width: 690rpx;
	min-height: 1186rpx;
	background: #FFFFFF;
	box-shadow: 1rpx 1rpx 0rpx 0rpx #FFFFFF;
	border-radius: 20rpx 26rpx 26rpx 20rpx;
	position: relative;
	padding: 60rpx 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.process{
		width: 100%;
		display: flex;
		justify-content: space-between;
		.process-item{
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			.top{
				width: 66rpx;
				height: 66rpx;
				border-radius: 50%;
				border: 2rpx solid #C4C4C4;
				font-size: 53rpx;
				font-weight: bold;
				color: #C4C4C4;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				&:before{
					content: '';
					width: 84rpx;
					height: 2rpx;
					position: absolute;
					left: -114rpx;
					top: 50%;
					transform: translateY(-50%);
					background-image: linear-gradient(to right, 
						#C4C4C4 28%,
						transparent 28%, transparent 36%,
						#C4C4C4 36%, #C4C4C4 64%,
						transparent 64%, transparent 72%,
						#C4C4C4 72%, #C4C4C4 100%
					);
					background-size: 84rpx 2rpx;
				}
			}
			.bottom{
				margin-top: 18rpx;
				font-size: 30rpx;
				color: #fff;
				width: 180rpx;
				height: 55rpx;
				background: #C4C4C4;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				padding-left: 10rpx;
				
				&:before{
					content: '';
					position: absolute;
					left: 0;
					top: 0;
					width: 0;
					height: 0;
					border-top: 27.5rpx solid transparent;
					border-bottom: 27.5rpx solid transparent;
					border-left: 20rpx solid #fff;
				}
				
				&:after{
					content: '';
					position: absolute;
					left: 100%;
					top: 0;
					width: 0;
					height: 0;
					border-top: 27.5rpx solid transparent;
					border-bottom: 27.5rpx solid transparent;
					border-left: 20rpx solid #C4C4C4;
				}
			}
			&:first-child{
				.top{
					&:before{
						display: none;
					}
				}
			}
			&:last-child{
				.bottom{
					margin-right: 20rpx;
				}
			}
		}
		.active{
			.top{
				border-color: #F33C42;
				color: #F33C42;
				&:before{
					background-image: linear-gradient(to right, 
						#F33C42 28%,
						transparent 28%, transparent 36%,
						#F33C42 36%, #F33C42 64%,
						transparent 64%, transparent 72%,
						#F33C42 72%, #F33C42 100%
					);
				}
			}
			.bottom{
				background: #F33C42;
				&:after{
					border-left-color: #F33C42;
				}
			}
		}
	}
	.content{
		margin-top: 86rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.text1{
			font-size: 30rpx;
			color: #333333;
			line-height: 48rpx;
		}
	}
	.content2{
		margin-top: 40rpx;
	}
	.qrcode{
		margin-top: 50rpx;
		width: 334rpx;
		height: 334rpx;
		// border-radius: 50%;
		border: 10rpx solid #ECECEC;
	}
	.qrcode2{
		margin-top: 100rpx;
		width: 260rpx;
		height: 260rpx;
		border-radius: 50%;
		border: 10rpx solid #ECECEC;
	}
	.btn{
		width: 609rpx;
		height: 150rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 75rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 50rpx;
		.btn-text{
			font-size: 30rpx;
			font-weight: bold;
			color: #FFFFFF;
			line-height: 1.5;
			text-align: center;

		}
	}
	.btn2{
		position: absolute;
		bottom: 168rpx;
	}
	.rebind{
		font-size: 30rpx;
		color: #515151;
		line-height: 40rpx;
		position: absolute;
		bottom: 80rpx;
	}
	.form{
		margin-top: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.form-label{
			width: 100%;
			margin-top: 56rpx;
			font-size: 30rpx;
			color: #333333;
		}
		.form-radio {
			width: 100%;
			margin-top: 44rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			padding-bottom: 54rpx;
			border-bottom: 1rpx solid #EBEBEB;
			
			.form-radio-item {
				width: 257rpx;
				height: 72rpx;
				border-radius: 10rpx;
				border: 1rpx solid #EBEBEB;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #999;
				font-size: 30rpx;
				
				position: relative;
				overflow: hidden;
				
				&:active {
					transform: scale(0.98);
				}
				
				&:before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: #fafafa;
					z-index: -1;
				}
			}
			
			.active {
				background: linear-gradient(45deg, #F33C42, #FF6136);
				color: #fff;
				border: none;
				box-shadow: 0 2rpx 8rpx rgba(243, 60, 66, 0.3);
				transform: translateY(-2rpx);
				font-weight: 500;
				
				&:before {
					display: none;
				}
				
				&:active {
					transform: translateY(-1rpx) scale(0.98);
					box-shadow: 0 1rpx 4rpx rgba(243, 60, 66, 0.2);
				}
			}
		}
		.form-textarea{
			width: 609rpx;
			height: 178rpx;
			background: #F6F6F6;
			border-radius: 10rpx;
			border: 1rpx solid #EBEBEB;
			padding: 20rpx;
		}
		.upload-btn{
			margin-top: 90rpx;
			width: 432rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #4A87F8, #58CFFD);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(48,139,227,0.41);
			border-radius: 45rpx;
			font-size: 30rpx;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.tm-upload-area{
			margin-top: 52rpx;
		}
		.contact{
			margin-top: 57rpx;
			font-size: 30rpx;
			color: #F41A30;
			text-decoration-line: underline;
		}
		.form-desc{
			width: 546rpx;
			font-size: 30rpx;
			color: #333333;
			line-height: 48rpx;
			text-align: justify;
		}
		.form-label2{
			width: 100%;
			font-weight: bold;
			font-size: 36rpx;
			color: #333333;
			padding: 20rpx 0;
			margin-top: 20rpx;
		}
		.form-radio2{
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 14rpx 40rpx;
			
			.form-radio2-item{
				font-size: 30rpx;
				color: #333333;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				
				
				&:before {
					content: '';
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					border: 2rpx solid #BFBFBF;
					margin-right: 20rpx;
					
				}
			}
			.active {
				color: #F33C42;
				font-weight: 500;
				
				&:before {
					border-color: #F33C42;
					background: linear-gradient(45deg, #F33C42, #FF6136);
					box-shadow: 0 2rpx 6rpx rgba(243, 60, 66, 0.3);
					transform: scale(1.05);
				}
			}
		}
		.border_bottom{
			width: 100%;
			border-bottom: 1px solid #EBEBEB;
			margin: 10rpx 0;
		}
		.form-multiple{
			width: 100%;
			padding: 14rpx 14rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			flex-wrap: wrap;
			.form-multiple-item{
				width: 293rpx;
				height: 63rpx;
				background: #F6F6F6;
				border-radius: 32rpx;
				border: 1rpx solid #BFBFBF;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: #333333;
				margin-bottom: 18rpx;
				
				
				&:active {
					transform: scale(0.98);
				}
			}
			.active {
				background: linear-gradient(45deg, #F33C42, #FF6136);
				color: #FFFFFF;
				border: none;
				box-shadow: 0 2rpx 8rpx rgba(243, 60, 66, 0.3);
				transform: translateY(-2rpx);
				font-weight: 500;
				
				&:active {
					transform: translateY(-1rpx) scale(0.98);
					box-shadow: 0 1rpx 4rpx rgba(243, 60, 66, 0.2);
				}
			}
		}
	}
}
.resume_card {
	width: 688rpx;
	height: 200rpx;
	color: #FFFFFF;
	margin-bottom: 32rpx;
	position: relative;
	border-radius: 20rpx;
	overflow: hidden;

	.bg-image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	.card_content {
		position: relative;
		z-index: 2;
		padding: 40rpx 50rpx;
	}

	.p1 {
		font-size: 34rpx;
		font-weight: 700;
		letter-spacing: 0.5rpx;
		position: relative;
		display: inline-block;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.p2 {
		width: 400rpx;
		margin-top: 10rpx;
		white-space: pre-line !important;
		word-break: break-all;
		word-wrap: break-word;
		font-size: 24rpx;
		line-height: 1.4em;
		padding-top: 10rpx;
		letter-spacing: 0.3rpx;
		color: rgba(255, 255, 255, 0.95);
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.08);
	}
}
.swipe{
	height: 200rpx;
	width: 750rpx;
	.banner-img {
		width: 100%;
		height: 876rpx;
	}
}
.business_card {
	margin-top: 132rpx;
	padding-bottom: 90rpx;
	width: 690rpx;
	background: #FFFFFF;
	box-shadow: 0px 0px 12rpx 1rpx rgba(227, 227, 227, 0.87);
	border-radius: 26rpx;
	position: relative;

	.card-bg {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 238rpx;
		border-radius: 0 0 26rpx 26rpx;
	}

	.card-content {
		position: relative;
		width: 100%;
		height: 100%;
		z-index: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.avatar-wrapper {
		margin-top: -100rpx;
		width: 200rpx;
		height: 200rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 12rpx 1rpx rgba(227, 227, 227, 0.87);
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;

		.avatar-inner {
			display: flex;
			justify-content: center;
			align-items: center;
			overflow: hidden;

			image {
				width: 170rpx;
				height: 170rpx;
				border-radius: 50%;
			}
		}
	}

	.tag-list {
		width: 100%;
		display: flex;
		justify-content: center;
		margin-top: 30rpx;

		.tag {
			width: 20%;
			height: 58rpx;
			background: #FFFFFF;
			box-shadow: 0px 0px 12rpx 1rpx rgba(227, 227, 227, 0.87);
			border-radius: 29rpx;
			position: relative;
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0 10rpx;

			.tag-icon {
				position: absolute;
				left: 0;
				width: 58rpx;
				height: 58rpx;
			}

			.tag-text {
				margin-left: 39rpx;
			}
		}
	}

	.user-info {
		margin-top: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.name-age {
			display: flex;
			justify-content: center;

			.name {
				font-size: 36rpx;
				color: #606060;
				font-weight: normal;
			}

			.age {
				font-size: 36rpx;
				color: #606060;
				margin-left: 20rpx;
			}
		}

		.experience-area {
			display: flex;
			margin-top: 20rpx;

			.experience {
				font-size: 26rpx;
				color: #858585;
			}

			.area-text {
				font-size: 26rpx;
				color: #858585;
				margin-left: 10rpx;
			}
		}

		.job-item {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 20rpx;

			.job-name {
				font-size: 26rpx;
				color: #606060;
				margin-right: 15rpx;
			}

			.star {
				width: 16rpx;
				height: 22rpx;
				margin-right: 5rpx;
			}
		}
	}

	.logo {
		margin-top: 20rpx;
		width: 118rpx;
		height: 100rpx;
	}

	.divider {
		margin-top: 46rpx;
		width: 90%;
		border-bottom: 2rpx #B2B2B2 dashed;
	}

	.org-text {
		margin-top: 15rpx;
		text-align: center;
		font-size: 26rpx;
		color: #858585;
	}

	.org-name {
		margin-top: 6rpx;
		text-align: center;
		font-size: 26rpx;
		color: #858585;
	}

	.qr-code {
		width: 234rpx;
		height: 234rpx;
		margin-top: 40rpx;
	}

	.scan-text {
		width: 212rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 40rpx;
		font-size: 22rpx;
		color: #000000;
	}

	.contact {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx;

		.contact-text {
			font-size: 22rpx;
			color: #606060;
		}

		.phone {
			width: 153rpx;
			height: 57rpx;
			margin-left: 5rpx;
		}
	}

	.address {
		margin-top: 20rpx;
		text-align: center;
		font-size: 26rpx;
		color: #606060;
	}
}
.reqired{
	&::before{
		content: '*';
		color: red;
		margin-right: 4rpx;
		font-size: 28rpx;
		font-weight: bold;
	}
}
.charts {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	.tip {
		margin-top: 20rpx;
		font-size: 26rpx;
		color: #a0a0a0;
	}

	.list {
		width: 100%;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;

		.line {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 40rpx;

			.bar {
				height: 304rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.container {
					width: 304rpx;
					transform: rotate(270deg);
				}
			}

			.day {
				margin-top: 6rpx;
				font-size: 26rpx;
				line-height: 30rpx;
				font-weight: 400;
				color: #000;
				white-space: nowrap;
			}

			.month {
				margin-top: 6rpx;
				line-height: 30rpx;
				font-size: 26rpx;
				color: #000;
				white-space: nowrap;
			}
		}
	}
}