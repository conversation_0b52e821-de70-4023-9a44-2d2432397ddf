import { ref, watch } from 'vue'
import * as api from '@/api/index.js'
import { throttle } from '@/until/common'

interface UploadFile {
  url: string
  uid?: string
  [key: string]: any
}

interface UploadConfig {
  token?: string
  hid?: string | number
  type?: string
  [key: string]: any
}

export function useIdCardUpload() {
  const list = ref<UploadFile[]>([])
  const idObj = ref<Record<string, string>>({})
  const picId = ref('')

  // 监听 idObj 变化时更新 picId
  watch(idObj, val => {
    let temp = Object.keys(val).map(key => val[key])
    picId.value = temp.join(',')
  }, {
    deep: true
  })

  // 压缩图片
  const compressImage = async (src: string, maxSize: number = 5 * 1024 * 1024): Promise<string> => {
    const getFileInfo = async (path: string): Promise<number> => {
      return new Promise((resolve, reject) => {
        const fs = uni.getFileSystemManager();
        fs.getFileInfo({
          filePath: path,
          success: res => resolve(res.size),
          fail: err => reject(err)
        })
      })
    }

    try {
      const startTime = Date.now()  // 记录开始时间
      const originalSize = await getFileInfo(src)
      if (originalSize <= maxSize) {
        return src
      }

      uni.showLoading({
        title: '压缩中...',
        mask: true
      })

      let initialQuality = Math.min(80, Math.floor((maxSize / originalSize) * 90))
      let left = 10
      let right = initialQuality
      let lastValidPath = null
      let lastValidSize = originalSize
      let compressionCount = 0
      let compressionTimes: number[] = []  // 记录每次压缩的耗时

      const compress = async (quality: number): Promise<string> => {
        const compressStartTime = Date.now()
        return new Promise((resolve, reject) => {
          uni.compressImage({
            src,
            quality,
            success: res => {
              compressionTimes.push(Date.now() - compressStartTime)  // 记录单次压缩耗时
              resolve(res.tempFilePath)
            },
            fail: err => reject(err)
          })
        })
      }

      // 使用二分查找法来寻找合适的压缩质量，不限制次数
      while (left <= right) {
        compressionCount++
        const mid = Math.floor((left + right) / 2)
        const compressedPath = await compress(mid)
        const fileSize = await getFileInfo(compressedPath)

        if (fileSize <= maxSize) {
          // 找到符合大小的结果
          lastValidPath = compressedPath
          lastValidSize = fileSize
          // 继续尝试找更高质量的结果
          left = mid + 1
        } else {
          // 压缩不够，需要降低质量
          right = mid - 1
        }
      }

      // 如果二分查找没找到结果，使用线性递减
      if (!lastValidPath) {
        let quality = 1  // 从1%开始
        while (quality > 0) {
          compressionCount++
          const compressedPath = await compress(quality)
          const fileSize = await getFileInfo(compressedPath)
          
          if (fileSize <= maxSize) {
            lastValidPath = compressedPath
            lastValidSize = fileSize
            break
          }
          quality = Math.max(1, quality - 1)  // 每次减少1%，但不低于1%
        }
      }

      uni.hideLoading()

      if (lastValidPath) {
        const endTime = Date.now()
        const totalTime = endTime - startTime
        const compressionRatio = ((originalSize - lastValidSize) / originalSize * 100).toFixed(2)
        
        const avgCompressionTime = compressionTimes.reduce((a, b) => a + b, 0) / compressionTimes.length
        
        console.log(`图片压缩信息:
          初始大小: ${(originalSize / 1024 / 1024).toFixed(2)}MB
          最终大小: ${(lastValidSize / 1024 / 1024).toFixed(2)}MB
          压缩率: ${compressionRatio}%
          压缩次数: ${compressionCount}次
          总耗时: ${totalTime}ms (${(totalTime/1000).toFixed(2)}秒)
          平均每次压缩耗时: ${avgCompressionTime.toFixed(2)}ms
          压缩详情: ${compressionTimes.map(t => `${t}ms`).join(', ')}`)

        return lastValidPath
      } else {
        // 如果实在压缩不了，给出友好提示
        uni.showToast({
          title: '图片太大，请选择较小的图片',
          icon: 'none',
          duration: 3000
        })
        return src  // 返回原图，让上层业务决定如何处理
      }

    } catch (err) {
      uni.hideLoading()
      console.error('压缩图片失败:', err)
      
      uni.showToast({
        title: '图片压缩失败，请重试',
        icon: 'none'
      })
      
      return src
    }
  }

  // 文件选择后的处理
  const chooesefileAfter = async (files: UploadFile[]) => {
    const processedFiles = await Promise.all(files.map(async item => {
      try {
        item.url = await compressImage(item.url)
      } catch (err) {
        console.error('处理文件失败:', err)
      }
      return item
    }))

    return processedFiles
  }

  // 上传成功的处理
  const onSuccess = (e: any) => {
    let res = JSON.parse(e.response)
    if (res.code === 1) {
      idObj.value[e.uid] = res.data.id
      return true
    } else {
      return false
    }
  }

  // 删除照片的API调用
  const deletePhoto = async (id: string,type?:string) => {
    try {
      const res = await api.request.ajax({
        url: '/Tools/delPhoto',
        type: 'POST',
        data: { id,type }
      })
      return res
    } catch(e) {
      console.error('Delete photo failed:', e)
      throw e
    }
  }

  // 删除文件的处理
  const onRemove = throttle(async (e: UploadFile,type?:string) => {
    if (e.statusCode === 3) {
      try {
        const res = await deletePhoto(idObj.value[e.uid],type)
        if (res.code === 1) {
          Reflect.deleteProperty(idObj.value, e.uid)
          return true
        } else {
          uni.showToast({ icon: 'none', title: res.msg })
          return false
        }
      } catch(err) {
        uni.showToast({ icon: 'none', title: '删除失败' })
        return false
      }
    } else {
      return true
    }
  }, 500)

  // 上传图片
  const uploadImage = async (file: UploadFile, config: UploadConfig = {}) => {
    try {
      const uploadUrl = await compressImage(file.url)

      const result = await api.request.upload({
        url: '/Center/uploadPhotoSave',
        filePath: uploadUrl,
        name: 'photo',
        formData: config
      })

      // wxRequest.upload 返回的是完整的上传响应，需要取 data
      const response = typeof result.data === 'string' ? JSON.parse(result.data) : result.data
      
      if(response.code === 1) {
        if(file.uid) {
          idObj.value[file.uid] = response.data.id
        }
        return response
      }
      
      // 如果不是成功状态，返回错误响应而不是抛出错误
      uni.showToast({
        title: response.msg || '上传失败',
        icon: 'none'
      })
      return response
      
    } catch(e) {
      console.error('Upload failed:', e)
      uni.showToast({
        title: '上传失败，请重试',
        icon: 'none'
      })
      return {
        code: 0,
        msg: '上传失败，请重试'
      }
    }
  }

  // 批量上传图片
  const uploadImages = async (files: UploadFile[], config: UploadConfig = {}) => {
    const results = []
    for(const file of files) {
      try {
        const result = await uploadImage(file, config)
        results.push(result)
      } catch(e) {
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
      }
    }
    return results
  }

  return {
    list,
    idObj,
    picId,
    compressImage,
    chooesefileAfter,
    onSuccess,
    onRemove,
    uploadImage,
    uploadImages,
    deletePhoto
  }
} 