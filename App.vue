<template>
	<view></view>
</template>
<script lang="ts" setup>
	import { onLaunch,onShow } from '@dcloudio/uni-app'
	import * as api from '@/api/index.js'
	import { useStore } from '@/until/mainpinia';
	import { useTmpiniaStore } from '@/tmui/tool/lib/tmpinia'
	import { share } from '@/tmui/tool/lib/share'
	const { setShareApp, setShareTime,	onShareAppMessage,onShareTimeline } = share();
	onShareAppMessage();
	onShareTimeline();
	const store = useStore()
	const tmstore = useTmpiniaStore();
	onShow((e)=>{
		console.log('onShow',e);
		
		if(e.query.scene){
			store.$patch((state) => {
				state.screenScene = e.query.scene
			})
		}
	})
	const getIndex = ()=>{
		api.request.ajax({
			url: '/home/<USER>',
			type: 'POST',
			// whiteList: true,
		}).then(res => {
			if(res.code===1){
				tmstore.setWxShare(Object.assign(
					res.data.shareData,
				))
				store.$patch((state) => {
					state.setting = res.data
					state.token = uni.getStorageSync('token')
				})
				setShareApp(res.data.share)
				setShareTime(res.data.share)
			}else{
				uni.showToast({ title:res.msg, icon:'none' })
			}
		})
	}

	onLaunch((p)=>{
		console.log('onLaunch',p);
		
		store.$patch(state=>{
			state.pid = p.query.pid
			state.source = p.query.source
		})
		// #ifdef APP-PLUS || H5
		if (uni.getSystemInfoSync().platform == "ios") {
			uni.getNetworkType({
				success: function (res) {
					if(res.networkType!=='none'){
						getIndex()
						api.getUserAccountInfo()
						api.appUpData()
					}else{
						let callback = function (res) {
							if(res.isConnected){
								getIndex()
								api.getUserAccountInfo()
								api.appUpData()
								uni.offNetworkStatusChange(callback)
							}
						}
						uni.onNetworkStatusChange(callback);
					}
				},
			});

		}else{
			getIndex()
			api.getUserAccountInfo()
			api.appUpData()
		}
		// #endif 
		// #ifndef APP-PLUS || H5
		getIndex()
		api.getUserInfo()
		api.getUserAccountInfo()
		api.miniProgramUpdata()
		// #endif 
	})
</script>

<style>
	/* #ifdef APP-NVUE */
	@import './tmui/scss/nvue.css';
	/* #endif */
	/* #ifndef APP-NVUE */
	@import './tmui/scss/noNvue.css';
	/* #endif */

	@import 'global.css';
</style>