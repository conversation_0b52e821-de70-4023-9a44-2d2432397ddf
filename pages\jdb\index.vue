<template>
    <tm-app>
        <view class="mainbody">
            <customNavigationBar :label="NavigationBarTitle||'接单宝'" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-carousel autoplay :round="0" :width="750" :height="624" model="dot" imgmodel="widthFix"
					color="#FCD9DB" :list="bannerList" rangKey="src" @click="i=>goPage(bannerList,i)"></tm-carousel>
			</view>
            <view class="header-bill">
                <image src="https://wx.wansao.com/statics/images/family/person.png" class="person" mode="widthFix" />
                <view class="header-text">
                    <view>已有<text class="big">{{aunt}}位阿姨</text>完成名片/简历制作</view>
                    <view>已有<text class="big">{{customer}}位客户</text>查看阿姨名片/简历制作</view>
                    <view>已有<text class="big">{{contract}}位客户</text>通过阿姨名片/简历成功签约合同</view>
                </view>
            </view>

            <navigator class="item-block-pad red-pad" url="../ysqiangdan/index" hover-class="bottom-nav-hover">
                <view>
                    <view class="item-block-title">月嫂、育婴师 接单大厅</view>
                    <view class="item-text-small">实时订单，预接单，上工更方便</view>
                </view>
                <image src="https://wx.wansao.com/statics/images/family/sqiblock01.png" class="item-block-img" mode="widthFix" />
            </navigator>

            <navigator class="item-block-pad blue-pad" url="../grab/index" hover-class="bottom-nav-hover">
                <view>
                    <view class="item-block-title">保姆、家政 抢单大厅</view>
                    <view class="item-text-small">实时订单，预接单，上工更方便</view>
                </view>
                <image src="https://wx.wansao.com/statics/images/family/sqiblock01.png" class="item-block-img" mode="widthFix" />
            </navigator>

            <navigator class="item-block-pad" url="../info_schedule/index" hover-class="bottom-nav-hover">
                <view>
                    <view class="item-block-title">档期管理</view>
                    <view class="item-text-small">录入档期后，皖嫂的老师将根据档期给您推荐工作</view>
                </view>
                <image src="https://wx.wansao.com/statics/images/family/linkblock01.png" class="item-block-img" mode="widthFix" />
            </navigator>
            <tabber></tabber>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref,computed } from 'vue'
import { onShow, onLoad, onPageScroll } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'
import tabber from '@/components/tabber/tabber.vue'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import {snb} from '@/components/customNavigationBar/snb'
import { useStore } from '@/until/mainpinia';
const {NavigationBarTitle} = snb()
// 分享功能
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})
const store = useStore()
const bannerList = computed(() => store.setting.bannerList)
// 响应式数据
const pageon = ref(2)
const aunt = ref('321')
const customer = ref('56558')
const contract = ref('558')
const isFamily = ref(false)
const musicShow = ref(false)
const shareData = ref({})
const refresh = ref(true)

// 获取首页信息
const getIndexInfo = async () => {
    try {
        refresh.value = false
        const res = await api.request.ajax({
            url: '/Home/orderHall',
            type: 'POST'
        })
        if (res.code !== 1) {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
            return false
        }
        refresh.value = true
        shareData.value = res.data.shareData
        musicShow.value = res.data.musicShow
        aunt.value = res.data.aunt
        customer.value = res.data.customer
        contract.value = res.data.contract
        isFamily.value = res.data.isFamily
    } catch (error) {
        console.error('获取首页信息失败:', error)
    }
}

// 生命周期钩子
onLoad(() => {
    getIndexInfo()
})

onShow(() => {
    if (refresh.value) {
        getIndexInfo()
    }
})
</script>

<style lang="scss" scoped>
	.mainbody{
		height: 100vh;
	}
.header-bill {
    margin: -170rpx auto 0;
    position: relative;
    z-index: 2;
    width: 690rpx;
    min-height: 300rpx;
    background: linear-gradient(to bottom right, #FFFFFF, #FAFAFA);
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
    border-radius: 30rpx;
    padding: 40rpx 30rpx;
    transition: all 0.3s ease;
    display: flex;
    &:hover {
        transform: translateY(-3rpx);
        box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
    }
}

.header-text {
    margin-left: 20rpx;
    
    view {
        margin-bottom: 20rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}

.big {
    font-size: 36rpx;
    font-weight: 600;
    color: #FF295C;
    margin: 0 8rpx;
}

.person {
    width: 100rpx;
    
    &:hover {
        transform: translateY(-50%) scale(1.05);
    }
}

.item-block-pad {
    background: linear-gradient(to right, #f29d43, #ff8c55);
    border-radius: 20rpx;
    margin: 30rpx 30rpx 15px;
    padding: 20rpx;
    height: 180rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    line-height: 1.8em;
    color: #fff;
    position: relative;
}

.item-block-title {
    font-size: 35rpx;
}

.item-block-img {
    width: 118rpx;
    flex-shrink: 0;
}

.red-pad {
    background: linear-gradient(to right, #ff553c, #ff295c);
}

.blue-pad {
    background: linear-gradient(to right, #be7cfe, #9e80fa);
}

.item-text-small {
    line-height: 1.4em;
    font-size: 26rpx;
    padding-top: 10rpx;
}
</style>