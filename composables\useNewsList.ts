import { ref, watch } from 'vue'
import * as api from '@/api/index.js'

export function useNewsList() {
    // 状态
    const newsList = ref([])
    const subtitle = ref([])
    const bannerList = ref([])
    const currentTab = ref(0)
    const loading = ref(false)
    const hasMore = ref(true)
    const page = ref(1)

    // 获取分享宝列表数据
    const getNewsList = async () => {
        try {
            const res = await api.request.ajax({
                url: '/money/newsList',
                type: 'POST',
                data: {
                    column: 'news',
                }
            })
            if (res.code === 1) {
                newsList.value = res.data.data.list
                subtitle.value = res.data.subtitle
                bannerList.value = res.data.banners
            }
        } catch (error) {
            console.error('加载分享宝列表失败:', error)
        }
    }

    // 获取更多列表数据
    const getList = async () => {
        if (loading.value) return
        loading.value = true
        try {
            const res = await api.request.ajax({
                url: '/money/getNewsMore',
                type: 'POST',
                data: {
                    column: subtitle.value[currentTab.value]?.column,
                    page: page.value
                }
            })
            if (res.code === 1) {
                let resultList = []
                if(subtitle.value[currentTab.value]?.column === 'active'){
                    resultList = res.data.data.list || []
                }else{
                    resultList = res.data.list || []
                }
                if(page.value === 1){
                    newsList.value = resultList
                }else{
                    newsList.value = [...newsList.value, ...resultList]
                }
                hasMore.value = resultList.length > 0
            }
        } catch (error) {
            console.error('加载列表失败:', error)
        } finally {
            loading.value = false
        }
    }

    // 监听currentTab变化
    watch(currentTab, () => {
        page.value = 1
        getList()
    })

    // 处理标签切换
    const handleTabChange = (index: number) => {
        currentTab.value = index
    }

    // 处理加载更多
    const loadMore = () => {
        if (loading.value) return
        page.value++
        getList()
    }

    // 处理详情跳转
    const handleDetail = (item: any) => {
        const title = subtitle.value[currentTab.value]?.name
        if(subtitle.value[currentTab.value]?.column === 'active'){
            uni.navigateTo({
                url: `/pages/act_details/index?id=${item.id}`
            })
        }else{
            uni.navigateTo({
                url: `/pages/news_details/index?id=${item.id}&title=${title}`
            })
        }
    }

    return {
        // 状态
        newsList,
        subtitle,
        bannerList,
        currentTab,
        loading,
        hasMore,
        // 方法
        getNewsList,
        handleTabChange,
        loadMore,
        handleDetail
    }
} 