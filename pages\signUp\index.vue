<template>
	<tm-app ref="app">
		<view class="main">
			<image mode="widthFix" :src="formConfig.banner" class="main_img"></image>
			<view class="card">
				<view class="form_item" v-for="(field, index) in formConfig.fieldData" :key="index">
					<view class="label">{{ field.title }}</view>
					<input 
						:type="field.textType" 
						:placeholder="field.placeholder"
						v-model="formData[field.title]"
					>
				</view>
				<view class="submit" @click="handleSubmit">
					立即报名
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'

// Define interfaces
interface FormField {
	type: number
	title: string
	textType: string
	placeholder: string
}

interface FormConfig {
	title: string
	banner: string
	fieldData: FormField[]
	shareAppMessage: {
		title: string
		path: string
		imageUrl: string
	}
	shareTimeline: {
		title: string
		query: string
		imageUrl: string
	}
}

// Reactive data
const formConfig = ref<FormConfig>({} as FormConfig)
const formData = ref<Record<string, string>>({})
const activityId = ref('')
const sharerId = ref('')

// Share functionality
const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// Get form configuration
const getFormConfig = async (id: string, sid: string) => {
	try {
		const res = await api.request.ajax({
			url: '/money/getActFormSettings',
			type: 'POST',
			data: { id, sid }
		})
		if (res.code === 1) {
			formConfig.value = res.data
			// Initialize form data
			formConfig.value.fieldData.forEach(field => {
				formData.value[field.title] = ''
			})
			// Set share config
			setShareApp(res.data.shareAppMessage)
			setShareTime(res.data.shareTimeline)
		}
	} catch (error) {
		console.error('获取表单配置失败:', error)
	}
}

// Handle form submission
const handleSubmit = async () => {
	// Validate form data
	for (const field of formConfig.value.fieldData) {
		if (!formData.value[field.title]) {
			uni.showToast({
				title: `请输入${field.title}`,
				icon: 'none'
			})
			return
		}
	}

	try {
		const postData = formConfig.value.fieldData.map(field => ({
			title: field.title,
			value: formData.value[field.title]
		}))

		const res = await api.request.ajax({
			url: '/money/doActFormData',
			type: 'POST',
			data: {
				id: activityId.value,
				sid: sharerId.value,
				postData: JSON.stringify(postData)
			}
		})

		if (res.code === 1) {
			uni.showToast({
				title: '提交成功',
				icon: 'success'
			})
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}
	} catch (error) {
		console.error('提交表单失败:', error)
	}
}

// Handle page load
onLoad((options) => {
	if (options.id) {
		activityId.value = options.id
		sharerId.value = options.sid || ''
		getFormConfig(options.id, options.sid || '')
	}
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	border-top: 1rpx solid #E8E8E8;
	.main_img{
		width: 100%;
	}
	.card{
		margin-top: -44rpx;
		width: 686rpx;
		height: 774rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 60rpx;
		.form_item{
			height: 112rpx;
			width: 610rpx;
			border-bottom: 1rpx solid #EBEBEB;
			display: flex;
			align-items:center;
			justify-content: space-between;
			padding: 0 12rpx;
			.label{
				font-size: 26rpx;
				color: #333;
			}
			input{
				text-align: right;
			}
		}
		.submit{
			margin-top: 62rpx;
			width: 610rpx;
			height: 90rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
			border-radius: 44rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 30rpx;
			color: #fff;
		}
	}
}
</style>