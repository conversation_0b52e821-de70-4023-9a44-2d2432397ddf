<template>
	<tm-app ref="app">
		<view class="main">
			<view class="list">
				<view class="item" v-for="item in evaluationList" :key="item.id">
					<view class="flex-row-center-between">
						<view class="flex-center">
							<image :src="item.avatarUrl" mode="aspectFill" class="avatar"></image>
							<tm-text class="font-bold ml-20" :font-size="28" color="#000" :label="item.nickName"></tm-text>
							<tm-text :font-size="28" color="#000" label="评价"></tm-text>
						</view>
						<tm-text class="ml-10" :font-size="24" color="#000" :label="'共'+item.days+'天'"></tm-text>
					</view>
					<tm-text class="mt-20 d-block text-align-justify" :font-size="28" color="#000" :label="item.content" :decode="true"></tm-text>
					<view class="fulled mt-20" v-if="item.pics?.length">
						<tm-image-group>
							<view class="flex-row-center-start flex-wrap fulled">
								<view class="flex-col flex-col-center-center flex-shrink mb-n2 witem" 
								v-for="(pic,index) in item.pics" 
								:key="pic"
								@click="preview(item.pics,index)">
									<tm-image
									:width="140" 
									:height="140" 
									:round="7" 
									:src="pic"
									model="aspectFill" 
									class="flex-shrink"
									></tm-image>
								</view>
							</view>
						</tm-image-group>
					</view>
				</view>
			</view>
			<tm-divider v-if="!hasMore" label="没有更多了"></tm-divider>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import * as api from '@/api/index.js'
import { parseParams } from '@/until/parseParams'

const evaluationList = ref<any[]>([])
const page = ref(1)
const hasMore = ref(true)
const hid = ref('')
const port = ref('')
const getEvaluation = async () => {
	const res = await api.request.ajax({
		url: '/Center/getEvaluation',
		type: 'POST',
		data: {
			page: page.value,
            hid: hid.value,
			port: port.value,
		}
	})
	if (res.code === 1) {
		if(page.value === 1) {
			evaluationList.value = res.data.list
		} else {
			evaluationList.value.push(...res.data.list)
		}
		hasMore.value = page.value < res.data.pageTotal
	}
}

const preview = (list: string[], index: number) => {
	uni.previewImage({
		urls: list,
		current: index
	})
}

onLoad((e) => {
	const params = parseParams(e)
	hid.value = params.hid || hid.value
	port.value = params.port||''
	getEvaluation()
})

onReachBottom(() => {
	if(hasMore.value) {
		page.value++
		getEvaluation()
	}
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.list {
		width: 690rpx;
		padding: 30rpx 0;
		
		.item {
			width: 100%;
			padding: 30rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
			border-radius: 26rpx;
			margin-bottom: 30rpx;
			
			.witem {
				width: 25%;
			}
			.avatar{
				width: 70rpx;
				height: 70rpx;
				border-radius: 50%;
			}
		}
	}
}
</style> 