<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle||'下户确认'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="process_area" v-if="process == 1">
				<view class="process">
					<view class="process-item" :class="{'active': process >= 1}">
						<div class="top">1</div>
						<div class="bottom">开箱检查</div>
					</view>
					<view class="process-item" :class="{'active': process >= 2}">
						<div class="top">2</div>
						<div class="bottom">下户确认</div>
					</view>
					<view class="process-item" :class="{'active': process >= 3}">
						<div class="top">3</div>
						<div class="bottom">服务评价</div>
					</view>
				</view>
				<image src="/static/img/clockicon3.png" mode="aspectFill" class="qrcode qrcode2"></image>
				<view class="content content2">
					<text class="text1">阿姨准备下户啦</text>
					<text class="text1">请您对阿姨行李开箱检查</text>
					<text class="text1">确认所带物品均为阿姨所有！</text>
				</view>
				<view class="btn btn2" @click="clockOutVerify1">
					<text class="btn-text">已对阿姨行李开箱检查</text>
				</view>
			</view>
			<view class="process_area" v-if="process == 2">
				<view class="process">
					<view class="process-item" :class="{'active': process >= 1}">
						<div class="top">1</div>
						<div class="bottom">开箱检查</div>
					</view>
					<view class="process-item" :class="{'active': process >= 2}">
						<div class="top">2</div>
						<div class="bottom">下户确认</div>
					</view>
					<view class="process-item" :class="{'active': process >= 3}">
						<div class="top">3</div>
						<div class="bottom">服务评价</div>
					</view>
				</view>
				<view class="form">
					<view class="form-label">上户中您家是否有物品损坏</view>
					<view class="form-radio">
						<view class="form-radio-item" :class="{'active': form.is_damage == 0}" @click="form.is_damage = 0">否</view>
						<view class="form-radio-item" :class="{'active': form.is_damage == 1}" @click="form.is_damage = 1">是</view>
					</view>
					<view class="form-label">是否存在其他问题</view>
					<view class="form-radio">
						<view class="form-radio-item" :class="{'active': form.is_other == 0}" @click="form.is_other = 0">否</view>
						<view class="form-radio-item" :class="{'active': form.is_other == 1}" @click="form.is_other = 1">是</view>
					</view>
					<tm-upload class="tm-upload-area"  :imageHeight="180" :width="600" :rows="3" :maxFile="6"
						v-model="list1" :url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024" :onSuccessAfter="cardUpload1.onSuccess"
						:onRemove="e=>cardUpload1.onRemove(e,'16')" :chooesefileAfter="cardUpload1.chooesefileAfter" :formData="{
							token,
							type:'16',
							hid,
						}"
						v-if="form.is_other == 1||form.is_damage == 1"
					>
					</tm-upload>
					<textarea placeholder="请输入您要反馈的问题" class="form-textarea" v-model="form.other_problem" v-if="form.is_other == 1||form.is_damage == 1" />
					<view class="btn" @click="clockOutVerify2">
						<text class="btn-text">确认阿姨可以下户</text>
					</view>
					<view class="contact" @click="contact">联系派单老师，反馈问题</view>
				</view>
			</view>
			<view class="process_area" v-if="process >= 3">
				<view class="process">
					<view class="process-item" :class="{'active': process >= 1}">
						<div class="top">1</div>
						<div class="bottom">开箱检查</div>
					</view>
					<view class="process-item" :class="{'active': process >= 2}">
						<div class="top">2</div>
						<div class="bottom">下户确认</div>
					</view>
					<view class="process-item" :class="{'active': process >= 3}">
						<div class="top">3</div>
						<div class="bottom">服务评价</div>
					</view>
				</view>
				<view class="form">
					<view class="form-desc">尊敬的用户您好，感谢您对皖嫂家政的信任，为了给您带来更好的体验，邀请您对我们的服务做出评价。</view>
					
					<template v-for="item in evaluationConfig.list" :key="item.id">
						<!-- 单选评价 -->
						<template v-if="item.type === 'redio'">
							<view class="form-label2" v-if="item.title">{{ item.title }}</view>
							<view class="form-radio2">
								<view v-for="option in item.option" 
										:key="option"
										class="form-radio2-item"
										:class="{'active': item.value === option}"
										@click="handleRating(item, option)">
									{{ option }}
								</view>
							</view>
							<view class="border_bottom" v-if="item.line==='1'"></view>
						</template>

						<!-- 多选评价 -->
						<template v-if="item.type === 'checkbox'">
							<view class="form-label2" v-if="item.title">{{ item.title }}</view>
							<view class="form-multiple">
								<view v-for="option in item.option" 
										:key="option"
										class="form-multiple-item"
										:class="{'active': item.value?.includes(option)}"
										@click="toggleCheckboxRating(item, option)">
									{{ option }}
								</view>
							</view>
							<view class="border_bottom" v-if="item.line==='1'"></view>
						</template>

						<!-- 文本评价 -->
						<template v-if="item.type === 'textarea'">
							<view class="form-label2" v-if="item.title">{{ item.title }}</view>
							<textarea 
								placeholder="请输入您的评价" 
								class="form-textarea" 
								v-model="item.value"
							/>
							<view class="border_bottom" v-if="item.line==='1'"></view>
						</template>

						<!-- 图片上传 -->
						<template v-if="item.type === 'img'">
							<tm-upload class="tm-upload-area"  
								:imageHeight="180" 
								:width="600" 
								:rows="3" 
								:maxFile="Number(item.maxNum)"
								v-model="list2" 
								:url="uploadUrl" 
								formName="photo" 
								:maxSize="15 * 1024 * 1024" 
								:onSuccessAfter="cardUpload2.onSuccess"
								:onRemove="e=>cardUpload2.onRemove(e,'17')" 
								:chooesefileAfter="cardUpload2.chooesefileAfter" 
								:formData="{
									token,
									type:'17',
									hid,
								}">
							</tm-upload>
							<view class="uploadtip">最多可上传{{ item.maxNum }}张图片哦！</view>
						</template>
					</template>

					<view class="btn" @click="clockOutVerify3">
						<text class="btn-text">确认提交评价</text>
					</view>
				</view>
			</view>
		</view>
		<tm-overlay v-model:show="showWin" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
			<view class="dkpopup">
				<image src="/static/img/dktc.png" mode="widthFix" class="dkcg"></image>
				<view class="title"> 服务完成</view>
				<view class="content content2">
					<text class="content-text">阿姨已成功下户，祝您的生活幸福美满，阖家欢乐！</text>
				</view>
				<view class="confirm" @click="exit">确认关闭</view>
			</view>
		</tm-overlay>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance, onMounted, defineComponent } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { useIdCardUpload } from '@/until/useIdCardUpload'
import { snb } from '@/components/customNavigationBar/snb'
// 分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()


// 页面数据
const { NavigationBarTitle } = snb()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})

const store = useStore()
const process = ref(3)
const showWin = ref(false)
const list1 = ref([])
const list2 = ref([])

const form = ref({
	pic_id: '',
	is_damage: 0,
	is_other: 0,
	other_problem: '',
})

const pics = ref('')

const token = computed(() => store.token)
const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const hid = computed(() => store?.userInfo?.hid)
const cardUpload1 = useIdCardUpload()
const cardUpload2 = useIdCardUpload()
watch(cardUpload1.picId, (newVal) => {
	form.value.pic_id = newVal
})
watch(cardUpload2.picId, (newVal) => {
	pics.value = newVal
})

const evaluationConfig = ref({
	list: []
})

const handleRating = (item: any, rating: string) => {
	item.value = rating
}

const toggleCheckboxRating = (item: any, rating: string) => {
	if (!item.value) {
		item.value = []
	}
	const index = item.value.indexOf(rating)
	if (index === -1) {
		item.value.push(rating)
	} else {
		item.value.splice(index, 1)
	}
}

const contact = () => {
	uni.makePhoneCall({
		phoneNumber: contractDetail.value.phone
	})
}
const exit = () => {
	showWin.value = false
	uni.exitMiniProgram()
}

const id = ref('')

/**************************** 接口函数 ****************************/
const clockFlowData = ref({})
const contractDetail = ref({})
const getEvaluateOptions = async () => {
	try {
		const res = await api.request.ajax({
			url: '/Tools/evaluateOption',
			type: 'POST'
		})

		if (res.code === 1) {
			// 为每个评价项添加value字段
			evaluationConfig.value = {
				...res.data,
				list: res.data.list.map(item => ({
					...item,
					value: item.type === 'checkbox' ? [] : ''
				}))
			}
		}
	} catch (error) {
		console.error('获取评价配置失败:', error)
		uni.showToast({
			title: '获取评价配置失败',
			icon: 'none'
		})
	}
}
const clockOutVerify1 = async () => {
	const res = await api.request.ajax({
		url: '/work/clockOutVerify',
		type: 'POST',
		data: {
			id: id.value,
			step:1
		}
	})
	
	if (res.code == 1) {
		uni.showToast({
			title: res.msg,
			icon: 'none'
		})
		getClockFlow()
	}else{
		uni.showToast({
			title: res.msg,
			icon: 'none'
		})
	}
}
const clockOutVerify2 = async () => {
	// Validate required fields
	if (form.value.is_other === 1 && !form.value.other_problem) {
		uni.showToast({
			title: '请输入反馈问题',
			icon: 'none'
		})
		return
	}

	const res = await api.request.ajax({
		url: '/work/clockOutVerify',
		type: 'POST',
		data: {
			id: id.value,
			step: 2,
			is_damage: form.value.is_damage.toString(),
			is_other: form.value.is_other.toString(),
			content: form.value.other_problem || '',
			attachments: form.value.pic_id || ''
		}
	})
	
	if (res.code == 1) {
		uni.showToast({
			title: res.msg,
			icon: 'none'
		})
		getClockFlow()
	} else {
		uni.showToast({
			title: res.msg,
			icon: 'none'
		})
	}
}
const clockOutVerify3 = async () => {
	// 验证必填项
	for (const item of evaluationConfig.value.list) {
		if (item.ismust === '1') {
			if (item.type === 'redio' && !item.value) {
				uni.showToast({
					title: `请选择${item.title}评价`,
					icon: 'none'
				})
				return
			}
			if (item.type === 'checkbox' && (!item.value || item.value.length === 0)) {
				uni.showToast({
					title: `请选择${item.title}评价`,
					icon: 'none'
				})
				return
			}
			if (item.type === 'textarea' && !item.value) {
				uni.showToast({
					title: `请输入${item.title}`,
					icon: 'none'
				})
				return
			}
		}
	}

	try {
		// 格式化评价列表
		const list = evaluationConfig.value.list.map(item => ({
			...item,
			value: item.type === 'checkbox' ? item.value.join(',') : 
				   item.type === 'img' ? pics.value : 
				   item.value
		}))
		console.log(list);
		
		const res = await api.request.ajax({
			url: '/work/clockOutVerify',
			type: 'POST',
			data: {
				id: id.value,
				pid:evaluationConfig.value.pid,
				step: 3,
				list: JSON.stringify(list)
			}
		})

		if (res.code === 1) {
			showWin.value = true
			getClockFlow()
		} else {
			uni.showToast({
				title: res.msg || '提交失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('提交评价失败:', error)
		uni.showToast({
			title: '提交评价失败',
			icon: 'none'
		})
	}
}

// 添加获取打卡流程函数
const getClockFlow = async () => {
	try {
		const res = await api.request.ajax({
			url: '/work/clockFlow',
			type: 'POST',
			data:{
				id: id.value
			}
		})
		
		if (res.code === 1) {
			clockFlowData.value = res.data
			process.value = clockFlowData.value.clockOutFlow.flow + 1
		}
	} catch (error) {
		console.error('获取打卡流程失败:', error)
		uni.showToast({
			title: '获取打卡进度失败',
			icon: 'none'
		})
	}
}
const getContractDetail = async () => {
	try {
		const res = await api.request.ajax({
			url: '/work/ctDetail',
			type: 'POST',
			data:{
				id: id.value
			}
		})
		
		if (res.code === 1) {
			contractDetail.value = res.data
		}
	} catch (error) {
		console.error('获取合同详情失败:', error)
		uni.showToast({
			title: '获取合同信息失败',
			icon: 'none'
		})
	}
}

onLoad((e) => {
	if(e.scene){
		console.log(e.scene);
		let scene = decodeURIComponent(e.scene)
		const params = {}
		scene.split(',').forEach(item => {
			const [key, value] = item.split(':')
			params[key] = value
		})
		console.log(params);
		id.value = params.id || ''
	}else if(e.id){
		id.value = e.id
	}
	getClockFlow()
	getContractDetail()
})

// Call getEvaluateOptions when component mounts
onMounted(() => {
	getEvaluateOptions()
})

</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	min-height: 100vh;
	padding-bottom: 200rpx;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;
	align-items: center;
	.uploadtip{
		width: 100%;
		text-align: left;
		padding-left: 20rpx;
		font-size: 20rpx;
		color: #000;
		margin: 10rpx 0;
	}
}

</style>