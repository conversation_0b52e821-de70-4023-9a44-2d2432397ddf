<template>
	<tm-app ref="app">
		<view class="main" :class="auntResumeInfo?.resumeValid?'':'ovh'">
			<customNavigationBar :label="NavigationBarTitle || '皖嫂阿姨简历'" :showBack="true" :scrollTop="scrollTop" />
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="card">
				<tm-image class="card_bg" :width="690" :height="314" src="/static/img/resume_cardbg.png"></tm-image>
				<view class="top">
					<view class="avatar_araa" @click="preview([auntResumeInfo.photo], 0)">
						<tm-image class="rounded overflow" :width="130" :height="130"
							:src="auntResumeInfo.photo" v-if="auntResumeInfo.photo"></tm-image>
					</view>
					<view class="flex-col ml-n8">
						<view class="flex-start">
							<tm-text class="text-align-center font-bold" :font-size="30" color="#000"
								:label="auntResumeInfo.name"></tm-text>
							<tm-text class="text-align-center font-bold ml-10" :font-size="30" color="#000"
								:label="auntResumeInfo.age" v-if="auntResumeInfo.age"></tm-text>
						</view>
						<view class="flex-start mt-12" v-if="auntResumeInfo.workYear || auntResumeInfo.area">
							<tm-text class="text-align-center" :font-size="24" color="#858585"
								:label="auntResumeInfo.workYear + '年经验'" v-if="auntResumeInfo.workYear"></tm-text>
							<tm-text class="text-align-center ml-10" :font-size="24" color="#858585"
								:label="auntResumeInfo.area" v-if="auntResumeInfo.area"></tm-text>
						</view>
						<view v-if="auntResumeInfo?.jobName?.[0]">
							<view class=" mt-12" v-for="item in auntResumeInfo.jobName" :key="item">
								<tm-text class="text-align-center mr-15" :font-size="24" color="#000"
									:label="item"></tm-text>
							</view>
						</view>
					</view>
				</view>
				<view class="bottom">
					<view class="tag_area" v-for="item in auntResumeInfo.certPic" :key="item.tid">
						<tm-image :width="50" :height="50" :src="item.pic"></tm-image>
						<tm-text class="ml-8" :font-size="24" color="#000" :label="item.name"></tm-text>
					</view>
				</view>
				<tm-image class="absolute t-n10 r-n15" :width="160" :height="160" :src="auntResumeInfo.signPic"
					v-if="auntResumeInfo.signPic"></tm-image>
				<view class="mark"></view>
				<image class="absolute r-2 t-2 zIndex-3" style="width: 56rpx;" src="/static/img/logo2.png" mode="widthFix"></image>
			</view>
			<!-- <view class="box">
				<view class="flex-center">
					<tm-image class="mr-20" :width="138" :height="34" src="/static/img/logo3.png"></tm-image>
					<view class="flex-center ml-25">
						<tm-image class="" :width="20" :height="20" src="/static/img/star_white.png"></tm-image>
						<tm-text class="ml-7" :font-size="24" color="#fff" label="皖嫂经验"></tm-text>
					</view>
					<view class="flex-center ml-25">
						<tm-image class="" :width="20" :height="20" src="/static/img/star_white.png"></tm-image>
						<tm-text class="ml-7" :font-size="24" color="#fff" label="皖嫂品质培训"></tm-text>
					</view>
					<view class="flex-center ml-25">
						<tm-image class="" :width="20" :height="20" src="/static/img/star_white.png"></tm-image>
						<tm-text class="ml-7" :font-size="24" color="#fff" label="合作机构体检"></tm-text>
					</view>
				</view>
			</view> -->
			<view class="area bigbg" v-if="auntResumeInfo?.content">
				<view class="area_title">自我介绍</view>
				<view class="area_content">
					<kevy-ellipsis :content="auntResumeInfo.content" :rows="6" :font-size="28" :line-height="42"
						fontColor="#000" ref="ke1" />
					<view class="more" @click="showContent" v-if="!show1">
						<text>查看完整</text>
						<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-down"></tm-icon>
					</view>
					<view class="more" @click="showContent" v-if="show1">
						<text>收起全部</text>
						<tm-icon rotate :rotate-deg="0" :font-size="20" color="#909399" name="tmicon-angle-up"></tm-icon>
					</view>
				</view>
				<view class="gzlx" v-if="auntResumeInfo.zhujiastr">
					<text class="gzlx_p1">工作类型：</text>
					<text class="gzlx_p2">{{auntResumeInfo.zhujiastr}}</text>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo?.yysPersonPic?.length">
				<view class="area_title">
					个人证照
				</view>
				<view class="fulled">
					<tm-image-group>
						<view class="flex-row-center-start flex-wrap fulled">
							<view class="flex-col flex-col-center-center flex-shrink mt-n11 witem"
								v-for="(item, index) in auntResumeInfo.yysPersonPic" :key="item.id"
								@click="preview(auntResumeInfo.yysPersonPic.map(item => item.sourceUrl), index)">
								<tm-image :width="140" :height="140" :round="7" :src="item.thumbUrl" model="aspectFill"
									class="flex-shrink"></tm-image>
								<!-- <tm-text class="mt-5" :font-size="20" color="#000" label="2019年5月7日"></tm-text> -->
							</view>
						</view>
					</tm-image-group>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo?.certPic?.length">
				<view class="area_title">阿姨认证</view>
				<view class="tag_list">
					<view v-for="item in auntResumeInfo.certPic" :key="item.tid">
						<view class="tag_area" @click="() => item.show ? preview(item.imgs, 0) : ''">
							<tm-image :width="50" :height="50" :src="item.pic"></tm-image>
							<view class="right">
								<tm-text class="text1" :font-size="28" color="#000" :label="item.desc"></tm-text>
								<tm-text class="text2" :font-size="22" color="#FB243C" :label="item.tpl"></tm-text>
							</view>
							<tm-icon :font-size="24" color="#999" name="tmicon-angle-right" class="icon"
								v-if="item.show"></tm-icon>
						</view>
					</view>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo?.skillsInfo?.length">
				<view class="area_title">阿姨技能</view>
				<view class="skill_list pt-15">
					<view class="flex-start fulled mt-15" v-for="item in auntResumeInfo.skillsInfo" :key="item.name">
						<tm-image :width="24" :height="26" src="/static/img/right.png"></tm-image>
						<tm-text class="ml-18" :font-size="28" color="#000" :label="item.name"></tm-text>
					</view>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo?.otherExperList?.list?.length">
				<view class="area_title">
					工作经历
					<view class="more" @click="goLink('/pages/more_experList/index?hid=' + hid + '&port=' + port)">查看更多</view>
				</view>
				<view class="area_content">
					<view class="experience_card" v-for="item in auntResumeInfo.otherExperList.list" :key="item.id">
						<view class="info_item" v-if="item.area">服务区域：{{ item.area }}</view>
						<view class="info_item" v-if="item.remark">服务情况：{{ item.remark }}</view>
					</view>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo?.resumePhoto?.list?.length">
				<view class="area_title">
					阿姨相册
					<view class="more" @click="goLink('/pages/more_photo/index?hid=' + hid + '&port=' + port)">查看更多</view>
				</view>
				<view class="fulled">
					<tm-image-group>
						<view class="flex-row-center-start flex-wrap fulled">
							<view class="flex-col flex-col-center-center flex-shrink mt-n11 witem"
								v-for="(item, index) in auntResumeInfo.resumePhoto.list" :key="item.id"
								@click="preview(auntResumeInfo.resumePhoto.list.map(item => item.pic), index)">
								<tm-image :width="140" :height="140" :round="7" :src="item.thumbnail" model="aspectFill"
									class="flex-shrink"></tm-image>
								<!-- <tm-text class="mt-5" :font-size="20" color="#000" label="2019年5月7日"></tm-text> -->
							</view>
						</view>
					</tm-image-group>
				</view>
			</view>
			<view class="area" v-if="auntResumeInfo?.scheduleData?.length">
				<view class="area_title">阿姨档期</view>
				<view class="charts">
					<view class="list">
						<view class="line" v-for="(item, index) in auntResumeInfo.scheduleData" :key="index">
							<view class="bar">
								<view class="container">
									<tm-progress :width="304" :height="22" color="#fb243c"
										v-model:percent="item.percent"></tm-progress>
								</view>
							</view>
							<text class="month">{{ item.month }}月</text>
							<text class="day">{{ item.allDay }}天</text>
						</view>
					</view>
					<text class="tip">注意：标红的为该月安排的天数</text>
				</view>
			</view>
			<view class="area bigbg" v-if="auntResumeInfo.evaluationData?.list?.length">
				<view class="area_title">雇主评价 <view class="more" @click="goLink('/pages/more_evaluate/index?hid=' + hid + '&port=' + port)">
						查看更多</view>
				</view>
				<view class="fulled mt-n10" v-if="auntResumeInfo.evaluationData?.list?.length">
					<view class="evaluate-item mb-40" v-for="(item, index) in auntResumeInfo.evaluationData.list"
						:key="index">
						<view class="flex-row-center-between mb-10">
							<view class="flex-center">
								<image :src="item.avatarUrl" mode="aspectFill" class="avatar"></image>
								<tm-text class="font-bold ml-10" :font-size="28" color="#000"
									:label="item.nickName"></tm-text>
								<tm-text :font-size="28" color="#000" label="评价"></tm-text>
							</view>
							<tm-text class="ml-10" :font-size="24" color="#000" :label="'共' + item.days + '天'"></tm-text>
						</view>
						<kevy-ellipsis :content="item.content" :rows="6" :font-size="28" :line-height="42"
							fontColor="#000" actionFontColor="#a0a0a0" expandText="查看全文" collapseText="收起" />
						<view class="fulled mt-20" v-if="item.pics?.length">
							<tm-image-group>
								<view class="flex-row-center-start flex-wrap fulled">
									<view class="flex-col flex-col-center-center flex-shrink mb-n2 witem"
										v-for="pic in item.pics" :key="pic">
										<tm-image :width="140" :height="140" :round="7" preview :src="pic" :key="pic"
											model="aspectFill" class="flex-shrink"></tm-image>
									</view>
								</view>
							</tm-image-group>
						</view>
					</view>
				</view>
			</view>
			<view class="area smallbg">
				<view class="area_title">联系方式</view>
				<view class="fulled mt-n10 flex-col flex-col-center-center">
					<tm-text class="nowrap" :font-size="28" color="#000" label="安徽省皖嫂家政服务平台由省妇联创办于2001年"></tm-text>
					<tm-text class="mt-20" :font-size="28" color="#000" label="点击拨打电话免费咨询"></tm-text>
					<view class="flex-center mt-20" @click="call(auntResumeInfo?.customerService?.mobile)">
						<tm-image :width="48" :height="48" src="/static/img/tel.png"></tm-image>
						<tm-text class="font-bold ml-20" :font-size="40" color="#FB243C" :label="auntResumeInfo?.customerService?.mobile"></tm-text>
					</view>
				</view>

			</view>
			<view class="resumeTips">温馨提示：{{ auntResumeInfo.resumeTips }}</view>
			<view class="float" v-if="(hid !== store.userInfo.hid)&&!sid">
				<view class="left">
					<view class="container" @click="goLink('/pages/index/index')">
						<image src="../../static/img/home.png" class="icon"></image>
						<text class="text">首页</text>
					</view>
					<button class="container" open-type="share" @click="appshare">
						<image src="../../static/img/share2.png" class="icon"></image>
						<text class="text">分享</text>
					</button>
				</view>
				<view class="right">
					<view class="button button2" @click="call(auntResumeInfo?.customerService?.mobile)">电话咨询</view>
					<view class="button button3" @click="goLink('/pages/reserve/index?hid=' + hid)">立即预约</view>
				</view>
			</view>
			<view class="float" v-if="hid === store.userInfo.hid">
				<button class="button1" @click="goLink('/pages/business/index')" v-if="auntResumeInfo?.is_complete">分享个人简历</button>
				<button class="button1" @click="goLink('/pages/resume_treasure/index')" v-else>完善个人简历</button>
			</view>
			<view class="mask" v-if="!auntResumeInfo?.resumeValid">
				<image :src="auntResumeInfo?.afreshData?.src" class="afresh_img" mode="widthFix" @click="call(auntResumeInfo?.afreshData?.tel)"></image>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
import kevyEllipsis from '@/components/kevy-ellipsis/kevy-ellipsis'

const { onShareAppMessage, setShareApp, setShareTime, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()


const { NavigationBarTitle } = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})
const auntResumeInfo = ref({
	resumeValid: 1,
	is_complete: 0,
	resumeScore: 0,
	user_id: '',
	hid: 0,
	photo: '',
	name: '',
	phone: '',
	age: '',
	workYear: '',
	area: '',
	idcard: {
		id_number: '',
		date: ''
	},
	healthDate: '',
	education: '',
	jobName: [] as string[],
	certPic: [] as any[],
	signPic: '',
	content: '',
	contentLen: 0,
	zhujiastr: '',
	signedBill: '',
	skillsInfo: [] as any[],
	yysPersonPic: [] as any[],
	otherExperList: {
		port: 0,
		count: '',
		list: [] as any[]
	},
	scheduleData: [] as any[],
	evaluateUrl: '',
	evaluationData: {
		count: 0,
		thisPage: 1,
		pageTotal: 1,
		list: [] as any[]
	},
	resumePhoto: {
		count: 0,
		thisPage: 1,
		pageTotal: 1,
		list: [] as any[]
	},
	resumeCard: {
		resumeType: 0,
		qrLink: '',
		resumeViews: 0,
		resumeRank: 0
	},
	customerService: {
		mobile: '',
		sname: ''
	},
	resumeTips: '',
	afreshData: {
		src: '',
		tel: '',
		url: '',
		type: 0
	},
	shareData: {
		title: '',
		path: ''
	},
	shareTimeline: {
		title: '',
		query: ''
	}
})
const sid = ref('')
const allAllDayZero = ref(false)
const getAuntResumeInfo = async (params) => {
	const res = await api.request.ajax({
		url: '/Center/auntResumeInfo',
		type: 'POST',
		data: params
	})
	if (res.code === 1) {
		auntResumeInfo.value = {
			...res.data,
			scheduleData: res.data.scheduleData || [],
			evaluationData: res.data.evaluationData || { list: [] },
		}
		allAllDayZero.value = res.data.scheduleData.every(item => item.allDay == 0);
		if(params.sid){
			sid.value = params.sid
		}else{
			sid.value = ''
		}

		setShareApp(res.data.shareData)
		setShareTime(res.data.shareTimeline)
	}else{
		uni.showModal({
			title: '提示',
			content: res.msg,
			showCancel: false,
			success: (res) => {
				if (res.confirm) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}
			}
		})
	}
}

const call = (tel) => uni.$tm.u.callPhone(tel)

const needWaitHid = ref(false)
watch(() => store.userInfo?.hid, (newVal) => {
	if (newVal && needWaitHid.value) {
		hid.value = newVal
		getAuntResumeInfo({ hid: newVal })
		needWaitHid.value = false
	}
})
const preview = (list, index) => {
	uni.previewImage({
		urls: list,
		current: index
	})
}
const ke1 = ref(null)
const show1 = ref(false)
const showContent = ()=>{
	ke1.value.changeCollapse()
	show1.value = !show1.value
}
const hid = ref('')
const port = ref('')
onLoad((e) => {
	if(e.scene){
        let scene = decodeURIComponent(e.scene)
        const params = {}
        scene.split(',').forEach(item => {
            const [key, value] = item.split(':')
            params[key] = value
        })
        hid.value = params.hid || ''
        sid.value = params.sid || ''
        port.value = params.port || ''
		getAuntResumeInfo({ 
			hid: params.hid,
			sid: params.sid,
			time: params.time,
			port: params.port,
		})
    } else if (e.hid) {
		hid.value = e.hid
		sid.value = e.sid
		port.value = e.port
		getAuntResumeInfo({
			hid: e.hid,
			sid: e.sid,
			time: e.time,
			port: e.port,
		})
	} else {
		if (store.userInfo?.hid) {
			hid.value = store.userInfo.hid
			getAuntResumeInfo({ hid: store.userInfo.hid })
		} else {
			needWaitHid.value = true
		}
	}
})

const appshare = () => {
	// #ifdef APP-PLUS
	uni.share({
		provider: 'weixin',
		scene: "WXSceneSession",
		type: 5,
		imageUrl: '',
		title: auntResumeInfo.value.shareData.title,
		miniProgram: {
			id: 'gh_526d20f68d21',
			path: auntResumeInfo.value.shareData.path,
			type: 0,
			webUrl: 'https://wansao.com/'
		},
		success: function (res) {
			console.log("success:" + JSON.stringify(res));
		},
		fail: function (err) {
			uni.showModal({
				title: '分享失败',
				content: '您的手机上未安装微信,fail:' + JSON.stringify(err),
				showCancel: false,
			});
		}
	});
	// #endif
}
const resume_bg = ref('https://wx.wansao.com/statics/wsyjq/test/resume_bg.png')
</script>

<style lang="less" scoped>
.ovh{
	height: 100vh !important;
	overflow-y: hidden !important;
}
.charts{
	margin-top: 20rpx;
}
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: calc(200rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
	display: flex;
	flex-direction: column;
	align-items: center;

	.card {
		width: 690rpx;
		height: 314rpx;
		background-color: #fff;
		border-radius: 20rpx 26rpx 26rpx 20rpx;
		position: relative;
		z-index: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 0 36rpx;
		overflow: hidden;

		.card_bg {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 0;
		}

		.top {
			position: relative;
			z-index: 1;
			display: flex;

			.avatar_araa {
				width: 140rpx;
				height: 140rpx;
				background-color: #fff;
				border-radius: 50%;
				box-shadow: 0rpx -2rpx 7rpx 0rpx rgba(133, 43, 40, 0.4);
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}

		.bottom {
			margin-top: 4vw;
			width: 100%;
			display: flex;
			align-items: center;
			position: relative;
			z-index: 1;

			// justify-content: space-between;
			.tag_area {
				width: 25%;
				display: flex;
				align-items: center;
			}
		}

		.mark {
			width: 122rpx;
			height: 122rpx;
			position: absolute;
			right: 0rpx;
			top: 0rpx;
			background: linear-gradient(to bottom, #fb7041, #ea252b);
			transform: rotate(45deg) translate(0, -75%);
			z-index: 2;
		}
	}

	.box {
		margin-top: 70rpx;
		width: 750rpx;
		height: 705rpx;
		background: linear-gradient(180deg, #FFC7C7, rgba(253, 234, 224, 0.5), rgba(255, 255, 255, 0));
		border-radius: 26rpx;
		display: flex;
		justify-content: center;
		align-items: flex-start;
		padding-top: 30rpx;
		margin-bottom: -650rpx;
	}

	.area {
		margin-top: 50rpx;
		width: 690rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227, 227, 227, 0.87);
		border-radius: 26rpx;
		padding: 40rpx 50rpx;
		position: relative;
		// font-weight: 500;
		.witem {
			width: 25%;
		}

		.area_title {
			position: relative;
			padding-bottom: 16rpx;
			border-bottom: 1rpx solid #E6E6E6;
			font-weight: bold;
			font-size: 32rpx;
			color: #333333;
			display: flex;
			align-items: center;
			
			&:before {
				content: '';
				width: 12rpx;
				height: 30rpx;
				background: linear-gradient(-31deg, #FF6136, #F31630);
				border-radius: 6rpx;
				margin-right: 25rpx;
			}

			.more {
				font-size: 24rpx;
				color: #858585;
				position: absolute;
				right: 10rpx;
			}
		}

		.area_content {
			margin-top: 30rpx;
			color: #000;
			font-size: 26rpx;
			line-height: 50rpx;
			.more {
				margin-top: 16rpx;
				margin-bottom: -20rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				text {
					font-size: 25rpx;
					font-weight: 300;
					color: #909399;
					margin-right: 8rpx;
				}
			}
		}
		.gzlx{
			margin-top: 20rpx;
			.gzlx_p1{
				font-size: 30rpx;
				font-weight: bold;
			}
			.gzlx_p2{
				font-size: 26rpx;
			}
		}
		.tag_list {
			padding-top: 20rpx;

			.tag_area {
				border-bottom: 1rpx solid #E6E6E6;
				display: flex;
				align-items: flex-start;
				padding: 25rpx 0;
				position: relative;

				.right {
					margin-left: 20rpx;
					display: flex;
					flex-direction: column;
					align-items: flex-start;

					.text1 {
						margin-top: 6rpx;
					}

					.text2 {
						margin-top: 6rpx;
					}
				}

				.icon {
					position: absolute;
					right: 0;
					top: 50%;
					transform: translateY(-50%);
				}

				&:nth-last-child(1) {
					border-bottom: none;
				}
			}

		}
		.avatar {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
		}

		.experience_card {
			position: relative;
			padding: 20rpx 0 20rpx 30rpx;
			border-left: 4rpx solid #FB243C;
			
			&:before {
				content: '';
				position: absolute;
				left: -12rpx;
				top: 30rpx;
				width: 20rpx;
				height: 20rpx;
				background: #FB243C;
				border-radius: 50%;
			}
			
			&:last-child {
				margin-bottom: 0;
				
				&:after {
					display: none;
				}
			}
			
			.info_item {
				position: relative;
				font-size: 28rpx;
				color: #000;
				line-height: 44rpx;
				padding: 8rpx 0;
				
				&:first-child {
					color: #FB243C;
					padding-top: 0;
				}
				
				&:last-child {
					padding-bottom: 0;
					color: #000;
				}
			}
		}
	}
	.resumeTips {
		margin-top: 30rpx;
		width: 690rpx;
		padding: 20rpx 30rpx;
		font-size: 24rpx;
		line-height: 40rpx;
		color: #858585;
		background: rgba(251, 36, 60, 0.05);
		border-left: 4rpx solid #FB243C;
		border-radius: 10rpx;
		box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(227, 227, 227, 0.5);
		text-align: justify;
		letter-spacing: 1rpx;
		position: relative;
	}
	.ns {
		box-shadow: none;
	}
	.bigbg{
		background-image: v-bind("'url(' + resume_bg +')'");
		background-size: 270rpx 270rpx;					
		background-repeat: no-repeat;
		background-position:center;
	}
	.smallbg{			
		background-image: v-bind("'url(' + resume_bg +')'");
		background-size: 155rpx 155rpx;					
		background-repeat: no-repeat;
		background-position:center;
		
	}
	.float {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		background: rgba(255, 255, 255, .9);
		backdrop-filter: blur(8rpx);
		box-shadow: 1rpx 1rpx 16rpx 0rpx rgba(194, 186, 186, 0.5);
		padding: 20rpx 60rpx 20rpx 78rpx;
		padding-bottom: 0;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		display: flex;
		align-items: center;
		justify-content: space-between;

		.left {
			width: 150rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.container {
				display: flex;
				flex-direction: column;
				align-items: center;

				&:after {
					display: none;
				}

				.icon {
					width: 30rpx;
					height: 30rpx;
				}

				.text {
					width: 44rpx;
					height: 30rpx;
					line-height: 30rpx;
					margin-top: 10rpx;
					font-size: 22rpx;
					color: #413D3E;
				}
			}
		}

		.right {
			width: 400rpx;
			height: 72rpx;
			border: 1rpx solid #F83B3B;
			border-radius: 36rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			overflow: hidden;

			.button {
				font-size: 36rpx;
				height: 70rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				flex: 1;
			}

			.button2 {
				background-color: #fff;
				color: #F83B3B;
			}

			.button3 {
				background-color: #F83B3B;
				color: #fff;
			}
		}

		.button1 {
			width: 690rpx;
			height: 87rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236, 84, 64, 0.41);
			border-radius: 44rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #fff;
		}
	}
}
.mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 6665;
  display: flex;
  align-items: center;
  justify-content: center;
  .afresh_img {
		width: 600rpx;
	}
}
</style>