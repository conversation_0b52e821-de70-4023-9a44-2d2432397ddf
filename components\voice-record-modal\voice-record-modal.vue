<template>
	<view class="voice-record-modal" v-if="isVisible" @touchmove.stop.prevent>
		<!-- 遮罩层 -->
		<view class="modal-mask" @click="hideVoice"></view>
		
		<!-- 录音内容区域 -->
		<view class="modal-content">
			<!-- 语音气泡 -->
			<view class="voice-bubble" :class="{'recording': formData.isRecording}">
				<!-- 音频波形动画 -->
				<view class="voice-wave">
					<view class="wave-bar" v-for="(item, index) in waveData" :key="index" 
						:style="{height: item + 'rpx', animationDelay: (index * 0.1) + 's'}"
						:class="{'active': formData.isRecording}">
					</view>
				</view>
				<!-- 录音时长 -->
				<view class="voice-time" v-if="formData.isRecording">{{formData.showTime || '00:00'}}</view>
			</view>
			
			<!-- 提示文字 -->
			<view class="voice-tip">松开发送</view>
			
			<!-- 底部按钮区域 -->
			<view class="bottom-buttons">
				<!-- 取消按钮 -->
				<view class="btn-cancel" @click="cancelRecord">
					<view class="btn-icon">✕</view>
				</view>
				
				<!-- 录音按钮 -->
				<view class="record-button" 
					:class="{'recording': formData.isRecording}"
					@touchstart="onTouchStart" 
					@touchend="onTouchEnd" 
					@touchmove="onTouchMove">
					<view class="record-inner">
						<view class="record-wave" v-if="formData.isRecording"></view>
					</view>
				</view>
				
				<!-- 发送按钮 -->
				<view class="btn-send" @click="sendRecord" :class="{'disabled': !canSend}">
					<view class="btn-icon">文</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue'

// 定义事件
const emit = defineEmits(['cbResult'])

// 响应式数据
const isVisible = ref(false)
const recorderManager = ref<any>(null)
const timekeepingObj = ref<any>(null)
const touchObj = ref<any>(null)

// 录音相关数据
const formData = reactive({
	isRecording: false,
	tempFilePath: '',
	showTime: '',
	duration: 0,
	isPlay: false,
	tempFile: null,
	isAuthorized: true,
	moveStartY: 0,
	moveEndY: 0
})

// 音频波形数据
const waveData = ref([20, 35, 45, 25, 55, 30, 40, 50, 25, 35, 45, 30, 40, 25, 35])

// 是否可以发送
const canSend = computed(() => {
	return formData.tempFilePath && formData.duration > 0
})

// 初始化录音管理器
const initRecorderManager = () => {
	// #ifndef H5
	recorderManager.value = uni.getRecorderManager()
	recorderManager.value.onStop((res: any) => {
		if (!formData.isAuthorized) return
		formData.isRecording = false
		formData.tempFile = res
		formData.tempFilePath = res.tempFilePath
		
		// 检查是否上移取消
		if (formData.moveStartY - formData.moveEndY > 80) {
			clearFormData()
		}
	})
	// #endif
}

// 清空表单数据
const clearFormData = () => {
	formData.isRecording = false
	formData.tempFilePath = ''
	formData.showTime = ''
	formData.duration = 0
	formData.isPlay = false
	formData.tempFile = null
	formData.isAuthorized = true
	formData.moveStartY = 0
	formData.moveEndY = 0
}

// 显示录音弹窗
const showVoice = () => {
	isVisible.value = true
	clearFormData()
	nextTick(() => {
		initRecorderManager()
	})
}

// 隐藏录音弹窗
const hideVoice = () => {
	if (formData.isRecording) {
		doEndRecord()
	}
	isVisible.value = false
	clearFormData()
}

// 开始录音
const doStartRecord = async (e?: any) => {
	// #ifdef H5
	uni.showToast({
		icon: 'none',
		title: '暂不支持录音'
	})
	return
	// #endif
	
	// #ifndef H5
	clearFormData()
	
	try {
		// 检查权限
		// #ifdef APP || MP-WEIXIN
		const authResult = await uni.getAppAuthorizeSetting()
		const getAuthorized = authResult.microphoneAuthorized
		if (uni.getSystemInfoSync().platform === 'ios') {
			if (getAuthorized === 'denied') {
				formData.isAuthorized = false
				return uni.showModal({
					title: '提示',
					content: '是否要开启麦克风权限？',
					success(res) {
						if (res.confirm) {
							uni.openAppAuthorizeSetting()
						}
					}
				})
			}
		} else if (getAuthorized !== 'authorized') {
			formData.isAuthorized = false
			return uni.showToast({
				icon: 'none',
				duration: 3000,
				title: '请允许使用或在系统设置中打开麦克风权限'
			})
		}
		// #endif
		
		// 开始录音
		recorderManager.value.start({ duration: 60000 })
		
		if (e && e.touches && e.touches.length) {
			const getY = Number(e.touches[0]['pageY'])
			formData.moveStartY = getY
			formData.moveEndY = getY
		}
		
		formData.isRecording = true
		doStartTimekeeping()
	} catch (error) {
		console.error('录音启动失败:', error)
		uni.showToast({
			icon: 'none',
			title: '录音启动失败'
		})
	}
	// #endif
}

// 结束录音
const doEndRecord = async () => {
	clearTimeout(touchObj.value)
	
	// #ifndef H5
	doEndTimekeeping()
	if (recorderManager.value) {
		recorderManager.value.stop()
	}
	formData.isRecording = false
	// #endif
}

// 开始计时
const doStartTimekeeping = () => {
	doEndTimekeeping()
	let seconds = 0
	timekeepingObj.value = setInterval(() => {
		seconds += 1
		formData.duration = seconds
		formData.showTime = formatTime(seconds)
		if (seconds >= 60) {
			doEndRecord()
		}
	}, 1000)
}

// 结束计时
const doEndTimekeeping = () => {
	if (timekeepingObj.value) {
		clearInterval(timekeepingObj.value)
		timekeepingObj.value = null
	}
}

// 格式化时间
const formatTime = (time: number) => {
	const minute = Math.floor(time / 60)
	const second = time % 60
	return `${minute.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`
}

// 触摸开始
const onTouchStart = (e: any) => {
	touchObj.value = setTimeout(() => {
		doStartRecord(e)
	}, 200)
}

// 触摸结束
const onTouchEnd = () => {
	if (formData.isRecording) {
		doEndRecord()
		// 延迟一点发送，确保录音已停止
		setTimeout(() => {
			if (canSend.value) {
				sendRecord()
			}
		}, 100)
	} else {
		clearTimeout(touchObj.value)
	}
}

// 触摸移动
const onTouchMove = (e: any) => {
	if (e.touches && e.touches.length) {
		formData.moveEndY = Number(e.touches[0]['pageY'])
	}
}

// 取消录音
const cancelRecord = () => {
	if (formData.isRecording) {
		doEndRecord()
	}
	hideVoice()
}

// 发送录音
const sendRecord = () => {
	if (!canSend.value) return
	
	emit('cbResult', {
		tempFilePath: formData.tempFilePath,
		showTime: formData.showTime,
		duration: formData.duration,
		tempFile: formData.tempFile
	})
	
	hideVoice()
}

// 暴露方法给父组件
defineExpose({
	showVoice,
	hideVoice
})
</script>

<style lang="less" scoped>
.voice-record-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
	position: relative;
	z-index: 10;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	height: 100%;
	justify-content: center;
}

.voice-bubble {
	width: 400rpx;
	height: 200rpx;
	background: linear-gradient(135deg, #7ED321, #50E3C2);
	border-radius: 20rpx;
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(126, 211, 33, 0.3);
	
	&::after {
		content: '';
		position: absolute;
		bottom: -20rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 0;
		height: 0;
		border-left: 20rpx solid transparent;
		border-right: 20rpx solid transparent;
		border-top: 20rpx solid #50E3C2;
	}
	
	&.recording {
		animation: bubble-pulse 1.5s ease-in-out infinite;
	}
}

.voice-wave {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 4rpx;
	margin-bottom: 10rpx;
}

.wave-bar {
	width: 6rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 3rpx;
	transition: height 0.3s ease;
	
	&.active {
		animation: wave-animation 1.2s ease-in-out infinite;
	}
}

.voice-time {
	font-size: 24rpx;
	color: #fff;
	font-weight: bold;
}

.voice-tip {
	font-size: 28rpx;
	color: #fff;
	margin-bottom: 100rpx;
	text-align: center;
	
	&.recording-tip {
		color: #FFD700;
	}
}

.bottom-buttons {
	position: absolute;
	bottom: 200rpx;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 100rpx;
}

.btn-cancel, .btn-send {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	
	.btn-icon {
		font-size: 40rpx;
		color: #fff;
		font-weight: bold;
	}
	
	&.disabled {
		opacity: 0.5;
	}
}

.record-button {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	backdrop-filter: blur(10rpx);
	transition: all 0.3s ease;
	
	&.recording {
		background: rgba(255, 255, 255, 0.5);
		transform: scale(1.1);
	}
}

.record-inner {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.record-wave {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
	animation: record-wave 1.5s ease-in-out infinite;
}

@keyframes bubble-pulse {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

@keyframes wave-animation {
	0%, 100% {
		transform: scaleY(1);
	}
	50% {
		transform: scaleY(2);
	}
}

@keyframes record-wave {
	0% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.5);
		opacity: 0;
	}
}
</style>
