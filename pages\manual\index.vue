<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle || '月子护理服务手册'" :scrollTop="scrollTop" :showBack="true"/>
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="manual_top">
				<view class="manual_left">
					<image src="/static/img/manual_tit.png" mode="widthFix" class="manual_tit"></image>
					<view class="manual_stit">
						<text>尊敬的月嫂阿姨</text>
						<text>您正在服务的客户为XXX</text>
					</view>
				</view>
				<image src="/static/img/manual_avatar.png" mode="widthFix" class="manual_avatar"></image>
			</view>
			<view class="card_tab">
				<view class="tab_item" :class="{active:activeTab === 1}" @click="activeTab = 1">
					<image src="/static/img/manual_baby.png" mode="aspectFit" class="tab_img"></image>
					<view class="tab_text">婴儿记录</view>
				</view>
				<view class="tab_item" :class="{active:activeTab === 2}" @click="activeTab = 2">
					<image src="/static/img/manual_mammy.png" mode="aspectFit" class="tab_img"></image>
					<view class="tab_text">宝妈记录</view>
				</view>
			</view>
			<view class="area2" v-if="activeTab===1">
				<view v-for="(item,index) in cardList" :key="index">
					<view class="resume_card" @click="goLink(item.url)" v-if="item.show">
						<image class="bg-image" :src="item.bgImg" mode="aspectFill"></image>
						<view class="card_content">
							<view class="p1">{{item.title}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="area2" v-if="activeTab===2">
				<view v-for="(item,index) in cardList2" :key="index">
					<view class="resume_card" @click="goLink(item.url)" v-if="item.show">
						<image class="bg-image" :src="item.bgImg" mode="aspectFill"></image>
						<view class="card_content">
							<view class="p1">{{item.title}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'

//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()

const { NavigationBarTitle } = snb()

// 页面数据
const store = useStore()
const scrollTop = ref(0)

onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})

const cardList = ref([
	{
		title: '婴儿科学喂养',
		url: '/pages/record_baby_feeding/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card1.png',
		show: true,
	},
	{
		title: '婴儿大小便观察',
		url: '/pages/record_baby_toilet/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card2.png',
		show: true,
	},
	{
		title: '婴儿其他护理记录',
		url: '/pages/record_baby_other/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card3.png',
		show: true,
	},
])

const cardList2 = ref([
	{
		title: '饮食记录',
		url: '/pages/record_mother_diet/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card4.png',
		show: true,
	},
	{
		title: '宝妈护理记录',
		url: '/pages/record_mother_nursing/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card5.png',
		show: true,
	},
	{
		title: '其他记录',
		url: '/pages/record_mother_other/index',
		bgImg: 'https://wx.wansao.com/statics/wsyjq/test/manual_card6.png',
		show: true,
	},
])


const activeTab = ref(1)

</script>

<style lang="scss">
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.manual_top{
		display: flex;
		position: relative;
		.manual_left{
			.manual_tit{
				width: 287rpx;
			}
			.manual_stit{
				margin-top: 30rpx;
				display: flex;
				flex-direction: column;
				color: #fff;
				font-size: 28rpx;
				line-height: 1.5;
			}
		}
		.manual_avatar{
			width: 275rpx;
			margin-left: 54rpx;
		}
	}
	.card_tab{
		margin-top: 60rpx;
		display: flex;
		justify-content: space-between;
		position: relative;
		.tab_item{
			width: 328rpx;
			height: 202rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(187,187,187,0.41);
			border-radius: 26rpx;
			margin: 0 17rpx;
			transition: .3s;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			.tab_img{
				width: 100rpx;
				height: 100rpx;
			}
			.tab_text{
				margin-top: 6rpx;
				font-size: 36rpx;
				color: #FD826F;
				transition: .3s;
			}
		}
		.active{
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
			.tab_text{
				color: #fff;
			}
		}
	}
	.area2{
		margin-top: 44rpx;
		animation: slideInUp 0.6s ease-out forwards;
		.resume_card{
			display: flex;
			align-items: center;
		}
	}
}

@keyframes slideInUp {
	0% {
		opacity: 0;
		transform: translateY(60rpx);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}
</style>