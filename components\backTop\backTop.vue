<template>
	<view class="back_top" @click="backToTop" :show="isShowBackTop" :style="isShowBackTop?'opacity: 1;':'opacity: 0;'">
		<tm-icon :font-size="34" name="tmicon-angle-up" color="#796671"></tm-icon>
	</view>
</template>

<script lang="ts" setup>
	import { ref, computed } from 'vue'
	import tmIcon from '@/tmui/components/tm-icon/tm-icon.vue'
	const props = defineProps({
		scrollTop: {
			type: [String, Number],
			default: 0,
		},
	});

	const isShowBackTop = computed(() => {
		return Number(props.scrollTop) > 400;
	});

	const backToTop = () => {
		if(!isShowBackTop.value)return
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 500
		});
	}
</script>

<style lang="less" scoped>
	.back_top {
		opacity: 1;
		bottom: 200rpx;
		right: 40rpx;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #e1e1e1;
		position: fixed;
		z-index: 10;
		transition:.3s;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
	}
</style>
