<template>
	<tm-app ref="app">
		<view class="main">
			<customNavigationBar :label="NavigationBarTitle || '奖励明细'" :scrollTop="scrollTop" :showBack="true"/>
			<view class="swipe">
				<tm-image :width="750" :height="876" src="/static/img/banner4.png"></tm-image>
			</view>
			<view class="card">
				<view class="p1">已邀请<text class="fz48">{{ summary.peopleNum }}</text>人，已奖励<text class="fz48">{{ summary.peopleAmount }}</text> 元</view>
				<view class="p2">专属海报，邀请姐妹入驻。她签单上户你有推荐金！</view>
				<view class="button" @click="goLink('/pages/business/index')">我的专属海报</view>
			</view>
			<view class="tab_area">
				<view class="tab_top">
					<view 
						v-for="(tab, index) in tabList" 
						:key="index"
						class="tab_item"
						:class="{ pick: currentTab === index }"
						@click="handleTabChange(index)"
					>
						{{ tab.name }}({{ tabCounts[index] }})
					</view>
				</view>
				<view class="tab_bottom">
					<view class="tab_content">
						<template v-if="filteredPeopleList.length">
							<view class="content_item" v-for="item in filteredPeopleList" :key="item.id">
								<view class="time">{{ item.time }} {{ item.title }}</view>
								<view class="type">{{ item.statusMsg }}</view>
							</view>
						</template>
						<view class="no_data" v-else>
							<tm-text :font-size="28" color="#999" label="暂无数据"></tm-text>
						</view>
					</view>
				</view>
			</view>
			<view class="mod_card">
				<view class="title">推荐金明细</view>
				<view class="amount_record">
					<template v-if="wageList.length">
						<view class="item" v-for="(item, index) in wageList" :key="index">
							<view class="item_left">
								<view class="item_title">{{ item.title }}</view>
								<view class="item_time">{{ item.time }}</view>
							</view>
							<view class="item_value amount_red">+{{ item.amount }}</view>
						</view>
					</template>
					<view class="no_data" v-else>
						<tm-text :font-size="28" color="#999" label="暂无数据"></tm-text>
					</view>
				</view>
			</view>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import customNavigationBar from '@/components/customNavigationBar/customNavigationBar.vue'
import { snb } from '@/components/customNavigationBar/snb'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
const { NavigationBarTitle } = snb()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
const showFilter = ref(false)
const currentFilter = ref('')
const currentTab = ref(0)

// 恢复滚动监听
onPageScroll((e) => {
	scrollTop.value = e.scrollTop
})

const tabList = [
	{ name: '审核中', status: 0 },
	{ name: '已审核', status: 1 },
	{ name: '未通过', status: 2 },
	{ name: '已成单', status: 3 }
]

// 数据结构
interface RecommendItem {
	id: string
	title: string
	time: string
	status: string
	statusMsg: string
}

interface WageItem {
	id: number
	title: string
	time: string
	amount: number
}

interface RecommendSummary {
	peopleNum: number
	peopleAmount: number
}

const peopleList = ref<RecommendItem[]>([])
const wageList = ref<WageItem[]>([])
const summary = ref<RecommendSummary>({
	peopleNum: 0,
	peopleAmount: 0
})

// 计算每个状态的数量
const tabCounts = computed(() => {
	return tabList.map(tab => {
		return peopleList.value.filter(item => Number(item.status) === tab.status).length
	})
})

// 根据状态筛选列表
const filteredPeopleList = computed(() => {
	const currentStatus = tabList[currentTab.value].status
	return peopleList.value.filter(item => Number(item.status) === currentStatus)
})

// Tab切换处理函数
const handleTabChange = (index: number) => {
	currentTab.value = index
}


const getData = async () => {
	const res = await api.request.ajax({
		url: '/money/recommendList',
		type: 'POST',
	})
	if (res.code === 1) {
		peopleList.value = res.data.peopleList
		wageList.value = res.data.wageList
		summary.value = res.data.recommend
		
		// 获取数据后，切换到第一个有数据的tab
		const firstTabWithData = tabList.findIndex(tab => {
			return peopleList.value.some(item => Number(item.status) === tab.status)
		})
		
		// 如果找到有数据的tab，就切换过去
		if (firstTabWithData !== -1) {
			currentTab.value = firstTabWithData
		}
	}
}

onLoad(() => {
	getData()
})
</script>

<style lang="less" scoped>
.main {
	width: 750rpx;
	background-color: #fff;
	min-height: 100vh;
	overflow-x: hidden;
	padding-bottom: 100rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.card{
		width: 690rpx;
		height: 393rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		.p1{
			width: 609rpx;
			height: 107rpx;
			background: #F6F6F6;
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ED3444;
			font-weight: bold;
			.fz48{
				font-size: 48rpx;
			}
		}
		.p2{
			font-size: 18rpx;
			color: #606060;
			margin-top: 40rpx;
		}
		.button{
			margin-top: 38rpx;
			width: 609rpx;
			height: 80rpx;
			background: linear-gradient(-15deg, #F31630, #FF6136);
			box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #fff;
			font-size: 30rpx;
			font-weight: bold;
		}
	}
	.tab_area{
		width: 690rpx;
		margin-top: 46rpx;
		position: relative;
		z-index: 2;
		.tab_top{
			display: flex;
			justify-content: space-between;
			.tab_item{
				width: 165rpx;
				height: 70rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 0rpx 0rpx;
				border: 1rpx solid #D9D9D9;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				cursor: pointer;
				
				&:hover {
					opacity: 0.8;
				}
				
				&.pick {
					background: linear-gradient(-15deg, #F31630, #FF6136);
					border-radius: 20rpx 20rpx 0rpx 0rpx;
					color: #fff;
					border: none;
					transform: translateY(-2rpx);
					box-shadow: 0 2rpx 8rpx rgba(243, 22, 48, 0.2);
				}
			}
		}
		.tab_bottom{
			width: 690rpx;
			height: 460rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
			border-radius: 0rpx 0rpx 26rpx 26rpx;
			.tab_content{
				padding: 30rpx 50rpx;
				.content_item{
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 100rpx;
					border-bottom: 1rpx solid #E6E6E6;
					color: #606060;
					font-size: 28rpx;
					&:nth-last-child(1){
						border-bottom: none;
					}
					transition: all 0.3s ease;
					
					&:hover {
						background: rgba(246, 246, 246, 0.5);
					}
				}
			}
		}
	}
	.mod_card {
		margin-top: 30rpx;

		.amount_record {
			.item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #E6E6E6;

				&:last-child {
					border-bottom: none;
				}

				.item_left {
					.item_title {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 10rpx;
					}
					.item_time {
						font-size: 24rpx;
						color: #999;
					}
				}

				.item_value {
					font-size: 32rpx;
					font-weight: bold;
					&.amount_red {
						color: #ED3444;
					}
				}

				.item_status {
					font-size: 28rpx;
					color: #666;
				}
			}

			.no_data {
				padding: 40rpx 0;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
}
</style>