<template>
    <tm-app>
        <view class="container">
            <view class="topCont">
                <image src="https://wx.wansao.com/statics/family/sendhall/wsb_top_bg.png" mode="" />
            </view>

            <view class="duihuan">
                <image src="/static/img/person02.png" mode="" class="person02" />
                <view class="buywsb">
                    <view class="wsb-cont-top">
                        <image src="/static/img/wsb_mx.png" mode="" class="wsb_mx" />
                        <view>当前剩余:{{wsb}}个</view>
                    </view>

                    <view class="wsb-list">
                        <view class="wsb-list-item" v-for="(item, index) in mxList" :key="index">
                            <view class="wsb-list-item-left">
                                <view class="wsb-list-item-left-p1">{{item.subtitle}}</view>
                                <view class="wsb-list-item-left-p2">{{item.date}}</view>
                            </view>
                            <view class="wsb-list-item-score2">{{item.num}}</view>
                        </view>
                    </view>

                    <view class="wsb-btns">
                        <view class="wsb-button" @click="clickcall">反馈问题</view>
                        <navigator url="../ysqiangdan/index" class="wsb-button wsb-button2">返回首页</navigator>
                    </view>
                </view>

                <view class="banquan">安徽皖嫂家政服务有限责任公司</view>
            </view>
        </view>
    </tm-app>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { onShow, onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { share } from '@/tmui/tool/lib/share'
import * as api from '@/api/index.js'

// 分享功能
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

// 响应式数据
const wsb = ref(0)
const mxList = ref([])
const phone = ref('')

// 获取列表数据
const getlist = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/getCoinBill',
            type: 'POST'
        })
        if (res.code === 200) {
            mxList.value = res.data.list
            wsb.value = res.data.total
            phone.value = res.data.serviceTel
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('获取列表失败:', error)
    }
}

// 点击拨打电话
const clickcall = async () => {
    try {
        const res = await api.request.ajax({
            url: 'https://wx.wansao.com/api/Sendhall/feedback',
            type: 'POST'
        })
        if (res.code === 200) {
            uni.makePhoneCall({
                phoneNumber: String(phone.value)
            })
        } else {
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
    } catch (error) {
        console.error('反馈失败:', error)
    }
}

// 生命周期钩子
onLoad(() => {
    getlist()
})
onShow(() => {
    setTimeout(() => {
        uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: '#E33631'
        })
    }, 100);
})

</script>

<style lang="scss" scoped>
page {
    background-color: #f4f6fa;
    font-size: 26rpx;
    color: #6f6e6e;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.topCont {
    width: 750rpx;
    height: 569rpx;
    position: absolute;
    top: 0;

    image {
        width: 750rpx;
        height: 569rpx;
    }
}

.duihuan {
    position: relative;
    margin-top: 80rpx;
}

.person02 {
    width: 156rpx;
    height: 203rpx;
    position: absolute;
    right: 40rpx;
    z-index: 4;
}

.buywsb {
    width: 690rpx;
    background: #fff;
    border-radius: 20rpx;
    position: relative;
    box-shadow: 0 3rpx 16rpx #ddd;
    margin-top: 190rpx;
    padding: 40rpx 30rpx 50rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 3;
}

.wsb-cont-top {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
}

.wsb_mx {
    width: 188rpx;
    height: 62rpx;
}

.wsb-list {
    width: 100%;
    margin: 30rpx 0;

    &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx 0;
        border-bottom: 1px dotted #ddd;

        &-left {
            &-p1 {
                font-size: 30rpx;
                color: #6f6e6e;
            }

            &-p2 {
                font-size: 24rpx;
                color: #ccc;
                margin-top: 15rpx;
            }
        }

        &-score2 {
            color: #333;
            font-size: 30rpx;
        }
    }
}

.wsb-btns {
    margin-top: 40rpx;
    width: 530rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wsb-button {
    width: 245rpx;
    height: 82rpx;
    background: #fba92e;
    line-height: 82rpx;
    border-radius: 50rpx;
    text-align: center;
    font-size: 30rpx;
    color: #fff;

    &2 {
        background: #f14c47;
    }
}

.banquan {
    margin-top: 30rpx;
    color: #c6c1c1;
    text-align: center;
    font-size: 28rpx;
}
</style> 