# 皖嫂一家亲 - 文档样式规范

## 标题规范

### 标题层级与字体大小

| 标题级别 | 类名 | 字体大小(rpx) | 字重 | 使用场景 |
|---------|------|-------------|------|---------|
| H1 | text-size-xl | 40rpx | bold | 页面主标题、重要板块标题 |
| H2 | text-size-lg | 36rpx | bold | 主要板块标题、弹窗标题 |
| H4 | text-size-n | 32rpx | bold | 卡片标题、表单分组标题 |
| H5 | text-size-m | 32rpx | normal | 列表项标题、小标题 |
| H6 | text-size-s | 24rpx | bold | 辅助标题、小型卡片标题 |

### 正文文本规范

| 文本类型 | 类名 | 字体大小(rpx) | 字重 | 使用场景 |
|---------|------|-------------|------|---------|
| 正文-大 | text-size-n | 36rpx | normal | 重要内容、强调文本 |
| 正文-中 | text-size-m | 28rpx | normal | 主要内容文本 |
| 输入框 | text-size-m | 30rpx | normal | 输入框 |
| 提示文本 | text-size-xxs | 26rpx | normal | 提示信息、标签文本 |

### 字重规范

| 字重类型 | 类名 | 值 | 使用场景 |
|---------|------|---|---------|
| 细体 | text-weight-s | 100 | 特殊装饰文本 |
| 常规 | text-weight-n | 400 | 正文、一般文本 |
| 粗体 | text-weight-b | 700 | 标题、强调文本 |

### 对齐方式

| 对齐类型 | 类名 | 使用场景 |
|---------|------|---------|
| 左对齐 | text-align-left | 大段文本、表单文本 |
| 居中对齐 | text-align-center | 标题、按钮文本 |
| 右对齐 | text-align-right | 数值、时间等信息 |
| 两端对齐 | text-align-justify | 大段文本内容 |

### 文本修饰

| 修饰类型 | 类名 | 使用场景 |
|---------|------|---------|
| 删除线 | text-delete | 折扣价格、已完成项 |
| 下划线 | text-underline | 链接文本、强调内容 |

## 使用示例

```html
<!-- 页面主标题 -->
<tm-text class="text-size-xl text-weight-b text-align-center" label="皖嫂一家亲"></tm-text>

<!-- 板块标题 -->
<tm-text class="text-size-lg text-weight-b" label="最新动态"></tm-text>

<!-- 卡片标题 -->
<tm-text class="text-size-n text-weight-b" label="家政服务"></tm-text>

<!-- 正文内容 -->
<tm-text class="text-size-m text-weight-n" label="这是一段正文内容描述..."></tm-text>

<!-- 辅助说明 -->
<tm-text class="text-size-xs text-weight-n" label="发布于2023年5月1日"></tm-text>

<!-- 价格文本 -->
<tm-text class="text-size-n text-weight-b text-align-right" label="¥299"></tm-text>
<tm-text class="text-size-xs text-delete text-align-right" label="¥399"></tm-text>
```

## 注意事项

1. 保持一致性：在相同场景下使用相同的字体大小和样式
2. 层级清晰：确保标题层级清晰，不要跳级使用
3. 对比度：确保文本与背景有足够的对比度，保证可读性
4. 响应式：字体大小已适配不同设备，无需额外调整
5. 简洁为主：避免在一个页面中使用过多不同的字体大小和样式
