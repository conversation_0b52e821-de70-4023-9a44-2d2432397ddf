<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="添加档期"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="转介绍客户给皖嫂，签合同，有保险服务更有保障，还有奖励金哦！"></tm-text>
				</view>
			</view>
			<view class="area">
				<view class="form-item">
					<view class="title">客户称呼</view>
					<input class="input" placeholder="请输入" placeholder-class="c3" v-model="form.client" />
				</view>
				<view class="form-item">
					<view class="title">联系方式</view>
					<input type="tel" class="input" placeholder="请输入" placeholder-class="c3" v-model="form.phone" />
				</view>
				<view class="form-item">
					<view class="title">预产期</view>
					<tm-time-picker
						:showDetail="{
							year: true,
							month: true,
							day: true,
							hour: false,
							minute: false,
							second: false
						}"
						color="red"
						v-model="form.due_date_copy"
						:defaultValue="form.due_date_copy"
						:immediateChange="true"
						format="YYYY-MM-DD"
						v-model:model-str="form.due_date"
						class="flex-1"
					>
						<input class="input mr-n25" placeholder="请选择" placeholder-class="c3" disabled readonly v-model="form.due_date"/>
					</tm-time-picker>
					<tm-icon :font-size="30" color="#D3D2D2" name="tmicon-angle-right" class="absolute r-10"></tm-icon>
				</view>
				<view class="form-item">
					<view class="title">上户天数</view>
					<input type="number" class="input" placeholder="请输入" placeholder-class="c3" v-model="form.service_day" />
				</view>
				<view class="form-item">
					<view class="title">是否签署合同</view>
					<view class="switch">
						<view class="switch_item" :class="form.is_sign===-1?'pick':''" @click="form.is_sign=-1">已签合同</view>
						<view class="switch_item" :class="form.is_sign===1?'pick':''" @click="form.is_sign=1">末签合同</view>
					</view>
				</view>
				<view class="form-item nb">
					<view class="title">是否有保险</view>
					<view class="switch">
						<view class="switch_item pick" :class="form.is_insurance===-1?'pick':''" @click="form.is_insurance=-1">有保险</view>
						<view class="switch_item" :class="form.is_insurance===1?'pick':''" @click="form.is_insurance=1">无保险</view>
					</view>
				</view>
			</view>
			<view class="flex-col flex-col-center-center">
				<view class="button2" @click="addSchedule">添加档期仅自己看</view>
				<view class="button1 mt-n10">推荐给皖嫂签合同拿奖励金</view>
			</view>
			<tm-overlay v-model:show="show1" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
				<view class="popup">
					<tm-image class="absolute t--n15" :width="245" :height="152" src="/static/img/poplogo.png"></tm-image>
					<tm-text class="text-align-center mt-n10" :font-size="48" color="#333" label="录入成功"></tm-text>
					<view class="confirm" @click="back">好的</view>
				</view>
			</tm-overlay>
			<tm-overlay v-model:show="show2" :overlayClick="false" contentAnimation bgColor="rgba(0,0,0,0.8)">
				<view class="popup">
					<tm-image class="absolute t--n15" :width="245" :height="152" src="/static/img/poplogo.png"></tm-image>
					<tm-text class="text-align-center mt-n20" :font-size="30" color="#333" label="您已成功录入档期，请耐心等待皖嫂工作人员联系沟通"></tm-text>
					<view class="confirm" @click="back">好的</view>
				</view>
			</tm-overlay>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia';
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
//分享功能
const { onShareAppMessage, onShareTimeline } = share();
onShareAppMessage();
onShareTimeline()
// 页面数据
const store = useStore()
const scrollTop = ref(0)
onPageScroll((e)=>{
	scrollTop.value = e.scrollTop
})

const form = ref({
	client:'',
	phone:'',
	due_date:'',
	due_date_copy:'',
	service_day:'',
	is_sign:-1,
	is_insurance:-1,
})
const show1 = ref(false)
const show2 = ref(false)
const back = ()=>{
	uni.navigateBack({
		fail: () => {
			goLink('/pages/index/index')
		}
	});
}
const addSchedule = async ()=>{
	if(!form.value.client) return uni.showToast({ icon:'none',title:'请输入客户称呼' })
	const res = await api.request.ajax({
		url: '/Center/addSchedule',
		type: 'POST',
		data: form.value
	})
	if (res.code === 1) {
		show1.value = true
	}else{
		uni.showToast({ icon:'none',title:res.msg })
	}
}
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.area{
		padding-bottom: 160rpx;
		width: 690rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.form-item{
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			min-height: 112rpx;
			padding: 40rpx 0;
			border-bottom: 1rpx solid #EBEBEB;
			.title{
				width: 140rpx;
				color: #333333;
				font-size: 26rpx;
				white-space:nowrap;
			}
			.input{
				text-align: right;
				flex:1
			}
			.right{
				margin-right: 27rpx;
			}
			.switch{
				display: flex;
				align-items: center;
				flex: 1;
				justify-content: flex-end;
				.switch_item{
					margin: 0 19rpx;
					width: 187rpx;
					height: 72rpx;
					border-radius: 10rpx;
					border: 1rpx solid #EBEBEB;
					font-size: 26rpx;
					color: #D3D2D2;
					display: flex;
					justify-content: center;
					align-items: center;
				}
				.pick{
					background-color: #F33C42;
					color: #fff;
					border: none;
				}
			}
		}
		.form-item2{
			flex-direction: column;
			
			.title{
				width: 100%;
				text-align: left;
			}
			.textarea{
				margin-top: 34rpx;
				width: 100%;
				height: 269rpx;
				padding: 42rpx 30rpx;
				border: 1px solid #EBEBEB;
				border-radius: 10px;
			}
			.copa{
				width: 145rpx;
				height: 145rpx;
				background: linear-gradient(-15deg, #4A87F8, #58CFFD);
				box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(204,204,204,0.41);
				border-radius: 50%;
				border: 8rpx solid #FFFFFF;
				display: flex;
				flex-direction: column;
				align-items:center;
				justify-content: center;
				font-weight: 500;
				font-size: 22rpx;
				color: #FFFFFF;
				line-height: 26rpx;
				position: relative;
				z-index: 2;
				top: -72rpx;
			}
		}
		.nb{
			border-bottom: none;
		}

	}
	.button1{
		width: 688rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
	}
	.button2{
		width: 688rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #4A87F8, #58CFFD);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(48,139,227,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
	}
}	
.popup{
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	width: 574rpx;
	height: 371rpx;
	background: #FFEEEE;
	border-radius: 20rpx;
	padding: 0 76rpx;
	&:before{
		width: 545rpx;
		height: 343rpx;
		border: 1px solid rgba(251, 86, 90, 0.35);
		content: '';
		position: absolute;
	}
	.confirm{
		margin-top: 38rpx;
		width: 289rpx;
		height: 87rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 44rpx;
		font-size: 30rpx;
		color: #fff;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 2;
	}
}
</style>