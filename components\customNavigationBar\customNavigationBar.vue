<template>
	<view class="t-0 l-0 fulled zIndex-1 bg">
		<view class="fulled mt-20" :style="{ height: statusBarHeight }"></view>
		<view class="relative flex-center">
			<view class="navi_back" @click="back" v-if="prop.showBack">
				<tm-icon :font-size="40" :color="color" name="tmicon-angle-left"></tm-icon>
			</view>
			<tm-text _class="text-align-center pn" :font-size="32" :color="color" :label="prop.label"></tm-text>
		</view>
		<view class="fulled" :style="{ height: '20px' }"></view>
	</view>
</template>

<script lang="ts" setup>
	import { ref,computed, watch } from 'vue'
	import { goLink } from '@/until/index'
	import { throttle } from '@/until/common'
	const prop = defineProps({
		label: {
			type: String
		},
		showBack:{
			type: Boolean,
			default:false
		},
		scrollTop:{
			type:Number,
			default:0
		}
	})
	const deviceInfo = uni.getSystemInfoSync();
	const statusBarHeight = deviceInfo.statusBarHeight + 'px'
	const back = ()=>{
		uni.navigateBack({
			fail: () => {
				goLink('/pages/index/index')
			}
		});
	}
	const scrollTop = computed(()=>prop.scrollTop)
	const progress = ref(0)
	const color = ref('#ffffff')
	const setColor = ()=>{
		progress.value = Math.min(1,scrollTop.value/200)
		// 计算颜色通道的值，白色的R、G、B值是255，黑色的R、G、B值是0
		const value = Math.round(255 * (1 - progress.value));
		// 将值转换为两位16进制字符串
		const hexValue = value.toString(16).padStart(2, '0');
		color.value = `#${hexValue}${hexValue}${hexValue}`;
	}
	watch(scrollTop,val=>{
		if(val>0){
			throttle(setColor,50)()
		}else{
			setColor()
		}
	})
</script>

<style lang="less" scoped>
	.bg{
		position: fixed;
		background-color: v-bind("'rgba(255, 255, 255, '+progress+')'");
		z-index: 9;
		pointer-events: none;
	}
	.navi_back{
		height: 100%;
		width: 200rpx;
		position: absolute;
		left: 0;
		pointer-events: all;
		display: flex;
		justify-content: flex-start;
		padding-left: 20rpx;
	}
	.pn{
		pointer-events: none;
	}
</style>