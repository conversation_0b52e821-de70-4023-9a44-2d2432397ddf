<template>
	<tm-app ref="app">
		<view class="main">
			<view class="relative flex-col flex-col-center-center mt-16">
				<tm-image :width="688" :height="188" src="/static/img/info_bg.png"></tm-image>
				<view class="absolute flex-col flex-col-center-center" style="width: 591rpx;">
					<tm-text class="text-align-center" :font-size="36" :lineHeight="54" color="#fff" label="个人证照"></tm-text>
					<tm-text class="text-align-center mt-10" :font-size="24" :lineHeight="36" color="#fff" label="资料更新后，经审核无误会展示出来！"></tm-text>
				</view>
			</view>
			<view class="area">
<!-- 				<view class="form-item">
					<view class="title"><text class="name">形象照</text></view>
					<view class="right">
						<tm-upload v-model="photoList" :width="photoList.length?300:150" 
							:rows="photoList.length?2:1" :imageHeight="140"
							:url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024"
							:onSuccessAfter="photoUpload.onSuccess"
							:onRemove="photoUpload.onRemove" 
							:chooesefileAfter="photoUpload.chooesefileAfter"
							:formData="{token, hid, type: '14'}">
						</tm-upload>
					</view>
				</view> -->
				<view class="form-item">
					<view class="title">
						<text class="name">健康证</text>
						<view class="period clickable" @click="showHealthDate = true">
							<tm-icon :font-size="22" color="#F31630" name="tmicon-calendar-alt" class="mr-4"></tm-icon>
							有效期：{{form.health_date || '请选择 >'}} 
						</view>
					</view>
					<view class="right">
						<tm-upload v-model="healthList" :width="healthList.length?300:150" 
							:rows="healthList.length?2:1" :imageHeight="140"
							:url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024"
							:onSuccessAfter="healthUpload.onSuccess"
							:onRemove="healthUpload.onRemove" 
							:chooesefileAfter="healthUpload.chooesefileAfter"
							:formData="{token, hid, type: '3'}">
						</tm-upload>
					</view>
				</view>
				<view class="form-item">
					<view class="title"><text class="name">育婴师师证</text><text class="period">有效期：长期有效</text></view>
					<view class="right">
						<tm-upload v-model="yysList" :width="yysList.length?300:150" 
							:rows="yysList.length?2:1" :imageHeight="140"
							:url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024"
							:onSuccessAfter="yysUpload.onSuccess"
							:onRemove="yysUpload.onRemove" 
							:chooesefileAfter="yysUpload.chooesefileAfter"
							:formData="{token, hid, type: '4'}">
						</tm-upload>
					</view>
				</view>
				<view class="form-item">
					<view class="title"><text class="name">母婴护理证</text><text class="period">有效期：长期有效</text></view>
					<view class="right">
						<tm-upload v-model="myhlList" :width="myhlList.length?300:150" 
							:rows="myhlList.length?2:1" :imageHeight="140"
							:url="uploadUrl" formName="photo" :maxSize="15 * 1024 * 1024"
							:onSuccessAfter="myhlUpload.onSuccess"
							:onRemove="myhlUpload.onRemove" 
							:chooesefileAfter="myhlUpload.chooesefileAfter"
							:formData="{token, hid, type: '5'}">
						</tm-upload>
					</view>
				</view>
			</view>
			<view class="flex-center">
				<view class="button2" @click="back">返回上一页</view>
				<view class="button1" @click="savePhotos">提交审核</view>
			</view>

			<tm-time-picker
				v-model="form.health_date_copy"
				v-model:model-str="form.health_date"
				:defaultValue="form.health_date_copy"
				format="YYYY-MM-DD"
				:showDetail="{
					year: true,
					month: true,
					day: true,
					hour: false,
					minute: false,
					second: false
				}"
				start="2000/1/1"
				end="2048/12/31"
				v-model:show="showHealthDate"
			/>
		</view>
	</tm-app>
</template>

<script lang="ts" setup>
import { ref, computed, watch, getCurrentInstance } from "vue"
import { onShow, onLoad, onReachBottom, onPageScroll } from '@dcloudio/uni-app'
import { useStore } from '@/until/mainpinia'
import { share } from '@/tmui/tool/lib/share'
import { goLink } from '@/until/index'
import * as api from '@/api/index.js'
import { useIdCardUpload } from '@/until/useIdCardUpload'

// ================ 页面初始化 ================
const { onShareAppMessage, onShareTimeline } = share()
onShareAppMessage()
onShareTimeline()

const store = useStore()
const token = computed(() => store.token)
const uploadUrl = api.baseUrl + '/Center/uploadPhotoSave'
const hid = computed(() => store?.userInfo?.hid)

// ================ 表单数据 ================
const form = ref({
  health_date: '', // 健康证有效期
  health_date_copy: '', // 健康证有效期
  health_id: '', // 健康证ID
  other_ids: '', // 其他证件ID(育婴师证、母婴护理证)
  photo_id: '', // 形象照ID
})

// 健康证日期选择器控制
const showHealthDate = ref(false)

// ================ 图片上传实例 ================
// 形象照上传(type=14)
const photoUpload = useIdCardUpload()
const { list: photoList } = photoUpload

// 健康证上传(type=3)
const healthUpload = useIdCardUpload()
const { list: healthList } = healthUpload

// 育婴师证上传(type=4)
const yysUpload = useIdCardUpload()
const { list: yysList } = yysUpload

// 母婴护理证上传(type=5)
const myhlUpload = useIdCardUpload()
const { list: myhlList } = myhlUpload

// ================ 监听上传ID变化 ================
// 监听形象照ID
watch(photoUpload.picId, (newVal) => {
  if(newVal) {
    form.value.photo_id = newVal
  }
})

// 监听健康证ID
watch(healthUpload.picId, (newVal) => {
  form.value.health_id = newVal
})

// 监听育婴师证和母婴护理证ID,拼接到other_ids
watch([yysUpload.picId, myhlUpload.picId, healthUpload.picId], ([yysId, myhlId, healthId]) => {
  const ids = []
  if(yysId) ids.push(yysId)
  if(myhlId) ids.push(myhlId)
  if(healthId) ids.push(healthId)
  form.value.other_ids = ids.join(',')
})

// ================ 表单提交 ================
const savePhotos = async () => {
  // 表单验证
  if(!form.value.photo_id) {
    return uni.showToast({title: '请上传形象照', icon: 'none'})
  }
  if(!form.value.health_date) {
    return uni.showToast({title: '请选择健康证有效期', icon: 'none'})
  }
  if(!form.value.health_id) {
    return uni.showToast({title: '请上传健康证照片', icon: 'none'})
  }
  if(!yysUpload.picId.value && !myhlUpload.picId.value) {
    return uni.showToast({title: '请上传其他证件照片', icon: 'none'})
  }

  // 提交表单
  try {
    const res = await api.request.ajax({
      url: '/Center/subPhotos',
      type: 'POST',
      data: form.value
    })
    
    if(res.code === 1) {
      uni.showToast({title: '提交成功', icon: 'success'})
      setTimeout(() => {
        back()
      }, 1000)
    } else {
      uni.showToast({title: res.msg || '提交失败', icon: 'none'})
    }
  } catch(e) {
    uni.showToast({title: '提交失败', icon: 'none'})
  }
}

const back = () => {
	uni.navigateBack({
		fail:()=>{
			uni.reLaunch({
				url: '/pages/index/index'
			})
		}
	})
}
</script>

<style lang="less" scoped>
/deep/.c3{
	color:#D3D2D2;
}
.main {
	width: 750rpx;
	min-height: 100vh;
	background-color: #fff;
	overflow-x: hidden;
	padding-bottom: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	
	.area{
		padding-bottom: 160rpx;
		width: 690rpx;
		// background: #FFFFFF;
		// box-shadow: 0rpx 0rpx 12rpx 1rpx rgba(227,227,227,0.87);
		border-radius: 26rpx;
		position: relative;
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.form-item{
			position: relative;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			min-height: 112rpx;
			padding: 40rpx 0;
			border-bottom: 1rpx solid #EBEBEB;
			.title{
				display: flex;
				flex-direction: column;
				.name{
					color: #333333;
					font-size: 26rpx;
					white-space:nowrap;
				}
				.period{
					color: #D3D2D2;
					font-size: 26rpx;
					white-space:nowrap;
					
					&.clickable {
						margin-top: 10rpx;
						color: #F31630;
						text-decoration: underline;
						display: flex;
						align-items: center;
					}
				}
			}
			.input{
				text-align: left;
				flex:1
			}
			.right{
				margin-right: 27rpx;
			}
		}
		.nb{
			border-bottom: none;
		}

	}
	.button1{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #F31630, #FF6136);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(236,84,64,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
	.button2{
		width: 320rpx;
		height: 90rpx;
		background: linear-gradient(-15deg, #D5D5D5, #F0F0F0);
		box-shadow: 0rpx 0rpx 9rpx 1rpx rgba(192,192,192,0.41);
		border-radius: 45rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: #333333;
		font-size: 30rpx;
		font-weight: 500;
		margin: 0 25rpx;
	}
}
</style>